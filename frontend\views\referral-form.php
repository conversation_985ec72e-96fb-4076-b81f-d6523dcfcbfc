<?php
/**
 * Referral Form Template
 *
 * Displays the form for entering referral codes.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Get referral code from URL or cookie if exists
$referral_code = '';
if ( isset( $_GET['ref'] ) ) {
    $referral_code = sanitize_text_field( $_GET['ref'] );
} elseif ( isset( $_COOKIE['custom_cms_referral_code'] ) ) {
    $referral_code = sanitize_text_field( $_COOKIE['custom_cms_referral_code'] );
}

// Get discount information from the handler
global $custom_cms_referral_discount_handler;
$discount_info = null;

if (isset($custom_cms_referral_discount_handler) && is_object($custom_cms_referral_discount_handler)) {
    $discount_info = $custom_cms_referral_discount_handler->get_active_discount();
}

// If we don't have the discount handler available, use default values
if (!$discount_info) {
    $discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
    $discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
    $formatted_discount = $discount_type === 'percentage'
        ? $discount_amount . '%'
        : '₹' . number_format($discount_amount, 2);

    $discount_info = array(
        'type' => $discount_type,
        'amount' => $discount_amount,
        'formatted' => $formatted_discount
    );
}
?>
<div class="custom-cms-referral-form" data-theme="<?php echo esc_attr( $atts['theme'] ); ?>">
    <div class="referral-form-header">
        <h3><?php _e( 'Got a Referral Code?', 'custom-cms-referal' ); ?></h3>
        <p>
            <?php
            printf(
                __( 'Enter a referral code to get a %s discount on your purchase.', 'custom-cms-referal' ),
                '<strong>' . esc_html($discount_info['formatted']) . '</strong>'
            );
            ?>
        </p>
    </div>

    <div class="referral-form-content">
        <form id="custom-cms-referral-form" method="post">
            <?php wp_nonce_field( 'custom-cms-referral-nonce', 'referral_nonce' ); ?>
            <div class="form-row">
                <input type="text" name="referral_code" id="referral_code" placeholder="<?php echo esc_attr__( 'e.g., ', 'custom-cms-referal' ) . esc_attr( $code_prefix ) . esc_attr( str_repeat( 'X', $code_length ) ); ?>" value="<?php echo esc_attr( $referral_code_for_input ); ?>" />
                <button type="submit" class="apply-code-btn">
                    <?php _e( 'Apply', 'custom-cms-referal' ); ?>
                </button>
            </div>
            <div class="form-message"></div>
        </form>
    </div>

    <?php if ( ! empty( $referral_code ) ): ?>
    <div class="referral-form-applied">
        <div class="applied-message">
            <?php
            printf(
                __( 'Referral code <strong>%1$s</strong> has been applied. You will receive a <strong>%2$s</strong> discount.', 'custom-cms-referal' ),
                esc_html($referral_code),
                esc_html($discount_info['formatted'])
            );
            ?>
        </div>
    </div>
    <?php endif; ?>
</div>
