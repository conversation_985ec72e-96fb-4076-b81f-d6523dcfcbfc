/**
 * Frontend Styles for Custom CMS Referral Plugin
 *
 * Styling for the referral box, forms, and related components
 */

/* General styles */
.custom-cms-referral-box,
.custom-cms-referral-form,
.custom-cms-referral-stats {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    max-width: 100%;
    margin-bottom: 30px;
    box-sizing: border-box;
}

/* Referral Box Styles */
.custom-cms-referral-box {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.referral-box-header {
    background-color: #0073aa;
    color: white;
    padding: 15px 20px;
}

.referral-box-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.referral-box-content {
    padding: 20px;
}

.referral-code-display,
.referral-link-display {
    margin-bottom: 15px;
}

.referral-code-label,
.referral-link-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.referral-code-container,
.referral-link-container {
    display: flex;
    align-items: center;
}

.referral-code,
.referral-link {
    flex: 1;
    padding: 10px 15px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    background-color: #fff;
    color: #333;
}

.copy-code-btn,
.copy-link-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    border-left: none;
    border-radius: 0 4px 4px 0;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.copy-code-btn:hover,
.copy-link-btn:hover {
    background-color: #e0e0e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.copy-code-btn:active,
.copy-link-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Copy button states */
.copy-code-btn.copying,
.copy-link-btn.copying {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: white;
}

.copy-code-btn.error,
.copy-link-btn.error {
    background-color: #f44336;
    border-color: #f44336;
    color: white;
}

.referral-box-footer {
    background-color: #f0f0f0;
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
}

.referral-share-text {
    margin-bottom: 10px;
    font-weight: 500;
    color: #555;
}

.referral-share-buttons {
    display: flex;
    gap: 10px;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: opacity 0.2s;
}

.share-btn:hover {
    opacity: 0.8;
}

.email-share {
    background-color: #d44638;
}

.whatsapp-share {
    background-color: #25d366;
}

.facebook-share {
    background-color: #3b5998;
}

.twitter-share {
    background-color: #1da1f2;
}

.copy-success-message,
.copy-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transition: opacity 0.3s ease;
    max-width: 300px;
    word-wrap: break-word;
}

.copy-message.success {
    background-color: #4CAF50;
}

.copy-message.error {
    background-color: #f44336;
}

/* Animation for copy message */
.copy-message.show {
    opacity: 1;
}

/* Referral Form Styles */
.custom-cms-referral-form {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.referral-form-header {
    background-color: #0073aa;
    color: white;
    padding: 15px 20px;
}

.referral-form-header h3 {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
}

.referral-form-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.referral-form-content {
    padding: 20px;
}

.form-row {
    display: flex;
    gap: 10px;
}

#referral_code {
    flex: 1;
    padding: 10px 15px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    color: #333;
}

.apply-code-btn {
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.apply-code-btn:hover {
    background-color: #005d8c;
}

.form-message {
    margin-top: 10px;
    font-size: 14px;
}

.form-message.error {
    color: #d32f2f;
}

.form-message.success {
    color: #388e3c;
}

.referral-form-applied {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #e8f5e9;
    border-radius: 4px;
    font-size: 14px;
    color: #388e3c;
}

/* Referral Stats Styles */
.custom-cms-referral-stats {
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.referral-stats-header {
    background-color: #0073aa;
    color: white;
    padding: 15px 20px;
}

.referral-stats-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.referral-stats-content {
    padding: 20px;
}

.stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.stat-item {
    flex: 1;
    min-width: 120px;
    padding: 15px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #0073aa;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: #555;
}

/* Checkout Field Styles */
.custom-cms-referral-checkout-field,
.custom-cms-referral-lms-field {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f7f7f7;
    border-radius: 4px;
}

.woocommerce-referral-code-field input,
.stm_lms_checkout_referral input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.referral-code-applied {
    display: inline-block;
    margin-top: 10px;
    padding: 5px 10px;
    background-color: #e8f5e9;
    border-radius: 4px;
    color: #388e3c;
    font-size: 13px;
}

.referral-input-container {
    display: flex;
    gap: 10px;
}

.referral-input-container input {
    flex: 1;
}

.apply-referral-btn {
    padding: 10px 15px;
    background-color: #0073aa;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.referral-message {
    margin-top: 10px;
    font-size: 13px;
}

.referral-message.error {
    color: #d32f2f;
}

.referral-message.success {
    color: #388e3c;
}

/* Responsive styles */
@media screen and (max-width: 768px) {
    .stats-container {
        flex-direction: column;
        gap: 10px;
    }

    .form-row {
        flex-direction: column;
    }

    .apply-code-btn {
        width: 100%;
    }

    .referral-share-buttons {
        justify-content: center;
    }
}

/* Theme variations */
.custom-cms-referral-box[data-theme="dark"],
.custom-cms-referral-form[data-theme="dark"],
.custom-cms-referral-stats[data-theme="dark"] {
    background-color: #333;
    border-color: #444;
    color: #f0f0f0;
}

.custom-cms-referral-box[data-theme="dark"] .referral-box-header,
.custom-cms-referral-form[data-theme="dark"] .referral-form-header,
.custom-cms-referral-stats[data-theme="dark"] .referral-stats-header {
    background-color: #1e1e1e;
}

.custom-cms-referral-box[data-theme="dark"] .referral-box-footer {
    background-color: #2a2a2a;
    border-top-color: #444;
}

.custom-cms-referral-box[data-theme="dark"] .referral-code-label,
.custom-cms-referral-box[data-theme="dark"] .referral-link-label,
.custom-cms-referral-box[data-theme="dark"] .referral-share-text {
    color: #ccc;
}

.custom-cms-referral-box[data-theme="dark"] .referral-code,
.custom-cms-referral-box[data-theme="dark"] .referral-link,
.custom-cms-referral-form[data-theme="dark"] #referral_code {
    background-color: #444;
    border-color: #555;
    color: #f0f0f0;
}

.custom-cms-referral-box[data-theme="dark"] .copy-code-btn,
.custom-cms-referral-box[data-theme="dark"] .copy-link-btn {
    background-color: #555;
    border-color: #666;
    color: #f0f0f0;
}

.custom-cms-referral-stats[data-theme="dark"] .stat-item {
    background-color: #2a2a2a;
    border-color: #444;
}
