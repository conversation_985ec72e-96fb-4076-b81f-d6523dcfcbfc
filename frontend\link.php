<?php
/**
 * Frontend Link File
 *
 * This file links the frontend functionality to the main plugin.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define frontend directory constants
define( 'CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_URL', plugin_dir_url( __FILE__ ) );
define( 'CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR', plugin_dir_path( __FILE__ ) );

/**
 * Load frontend files
 */
require_once CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'shortcodes.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'frontend.php';

/**
 * Initialize the frontend
 *
 * Note: Frontend initialization is now handled by class-core.php to avoid conflicts.
 * This function is kept for backward compatibility but no longer initializes classes.
 */
function custom_cms_referral_init_frontend() {
    // Frontend initialization is now handled by class-core.php
    // This function is kept for any future frontend-specific initialization
    error_log('CMS Referral: Frontend link initialization called (handled by core)');
}

// Initialize the frontend - keeping for backward compatibility
add_action( 'plugins_loaded', 'custom_cms_referral_init_frontend' );
