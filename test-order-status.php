<?php
/**
 * Test script to check actual MasterStudy LMS order status
 *
 * This script helped us discover that MasterStudy LMS stores order status
 * in the 'status' meta field, not in WordPress post_status!
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== MasterStudy LMS Order Status Test ===\n";
echo "Current time: " . current_time('mysql') . "\n\n";

// Get some recent MasterStudy orders
global $wpdb;

$orders = $wpdb->get_results("
    SELECT p.ID, p.post_status, p.post_author, p.post_date
    FROM {$wpdb->posts} p
    WHERE p.post_type = 'stm-orders'
    ORDER BY p.post_date DESC
    LIMIT 5
");

if (empty($orders)) {
    echo "❌ No MasterStudy orders found\n";
    exit;
}

echo "Found " . count($orders) . " MasterStudy orders:\n\n";

foreach ($orders as $order) {
    echo "=== ORDER ID: {$order->ID} ===\n";
    echo "Post Status: {$order->post_status}\n";
    echo "Post Author: {$order->post_author}\n";
    echo "Post Date: {$order->post_date}\n";

    // Get all meta for this order
    $meta = $wpdb->get_results($wpdb->prepare("
        SELECT meta_key, meta_value
        FROM {$wpdb->postmeta}
        WHERE post_id = %d
        ORDER BY meta_key
    ", $order->ID));

    echo "Meta fields:\n";
    foreach ($meta as $m) {
        echo "  {$m->meta_key}: {$m->meta_value}\n";
    }

    // Test our get_order_status method logic
    echo "\n🔍 Testing our get_order_status method:\n";

    // Simulate the method logic
    $post_type = get_post_type($order->ID);
    echo "Post Type: {$post_type}\n";

    if ($post_type === 'stm-orders') {
        // Check status meta field (THE KEY DISCOVERY!)
        $masterstudy_status = get_post_meta($order->ID, 'status', true);
        echo "Meta 'status': " . ($masterstudy_status ? $masterstudy_status : 'EMPTY') . "\n";

        // Check other possible meta keys
        $alt_status_keys = array('_stm_lms_order_status', 'order_status', '_order_status');
        foreach ($alt_status_keys as $meta_key) {
            $status = get_post_meta($order->ID, $meta_key, true);
            echo "Meta '{$meta_key}': " . ($status ? $status : 'EMPTY') . "\n";
        }
    }

    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== ANALYSIS ===\n";
echo "🔍 KEY DISCOVERY: MasterStudy LMS stores order status in 'status' meta field!\n";
echo "📝 WordPress post_status is usually 'publish' for all orders (misleading!)\n";
echo "✅ The REAL order status is in get_post_meta(\$order_id, 'status', true)\n";
echo "🎯 Common MasterStudy status values: pending, completed, cancelled, processing\n\n";

echo "=== DEBUGGING TECHNIQUE LEARNED ===\n";
echo "1. When WordPress post_status doesn't match expected behavior\n";
echo "2. Check ALL meta fields for the post type\n";
echo "3. Look for plugin-specific meta keys that store the real status\n";
echo "4. Test with actual data, don't assume WordPress conventions\n";
echo "5. Use scripts like this to explore database structure\n";
?>