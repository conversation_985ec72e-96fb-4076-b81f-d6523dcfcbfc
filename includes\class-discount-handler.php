<?php
/**
 * Discount Handler Class
 *
 * Handles the application and management of discounts for the referral system.
 * This is a completely new implementation that fixes previous issues with discount persistence.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Discount Handler class
 */
class Custom_CMS_Referral_Discount_Handler {

    /**
     * Holds the session discount information
     *
     * @var array
     */
    private $active_discount = null;

    /**
     * Static cache to store original prices to prevent multiple discount applications
     * Key format: 'item_id' => [original_price, discounted_price, 'is_discounted' => true]
     *
     * @var array
     */
    private static $price_cache = [];

    /**
     * Track which items have been discounted to prevent multiple applications
     * Key format: 'item_id' => true
     *
     * @var array
     */
    private static $discounted_items = [];

    /**
     * Flag to track if discount has been applied to the current session
     *
     * @var bool
     */
    private static $discount_applied_this_session = false;

    /**
     * Cookie name for storing referral discount information
     */
    const DISCOUNT_COOKIE = 'custom_cms_referral_discount';

    /**
     * Transient prefix for discount data
     */
    const DISCOUNT_TRANSIENT_PREFIX = 'custom_cms_referral_discount_';

    /**
     * Constructor
     */
    public function __construct() {
        // Initialize discount handler
        $this->init();
    }

    /**
     * Initialize hooks
     */
    public function init() {
        // === CRITICAL DATABASE APPROACH FOR PAYMENT PROCESSING ===
        // This is the ONLY method we use for actual payment processing - direct database modification
        // FIXED: Added multiple hooks to ensure database modification happens
        add_action('wp_ajax_stm_lms_purchase', array($this, 'modify_cart_database_directly'), 0);
        add_action('wp_ajax_nopriv_stm_lms_purchase', array($this, 'modify_cart_database_directly'), 0);

        // Additional hooks to catch the purchase process at different stages
        add_action('init', array($this, 'setup_purchase_hooks'), 1);
        add_action('wp_loaded', array($this, 'setup_purchase_hooks'), 1);

        // Clear ALL discount data after order is completed to prevent issues with future orders
        add_action('stm_lms_order_accepted', array($this, 'clear_all_discount_data'), 99, 2);

        // Additional hooks to ensure cleanup happens on successful purchase
        add_action('masterstudy_lms_order_completed', array($this, 'clear_all_discount_data'), 99, 4);
        add_action('transition_post_status', array($this, 'handle_order_completion_cleanup'), 99, 3);

        // REMOVED: Don't reset discount flag on plugin load - this was causing issues
        // delete_option('_custom_cms_discount_already_applied');

        // === UI UPDATE HOOKS (for frontend display only) ===
        // These hooks update the UI but don't affect actual payment processing
        add_filter('stm_lms_cart_items', array($this, 'apply_discount_to_cart_items_ui'), 10, 1);
        add_filter('stm_lms_cart_total_amount', array($this, 'apply_discount_to_checkout_total_ui'), 99, 1);

        // Add JavaScript to update prices on checkout page
        add_action('wp_footer', array($this, 'add_checkout_price_update_script'), 99);

        // --- COURSE PRICE DISPLAY HOOKS (for frontend display only) ---
        add_filter('stm_lms_course_price_html', array($this, 'add_discount_badge_to_price'), 999, 2);
        add_filter('stm_lms_course_price_display', array($this, 'filter_displayed_price'), 999, 2);
        add_filter('stm_lms_get_course_price', array($this, 'apply_discount_to_course_price'), 10, 2);

        // --- AJAX HANDLERS ---
        // AJAX handler for applying/removing discounts (legacy action)
        add_action('wp_ajax_apply_referral_discount', array($this, 'ajax_apply_discount'));
        add_action('wp_ajax_nopriv_apply_referral_discount', array($this, 'ajax_apply_discount'));
        // Note: 'custom_cms_referral_apply_discount' is handled by frontend.php to avoid conflicts

        // --- CHECKOUT DISPLAY ---
        // Add visual elements to checkout page to show discount
        add_action('stm_lms_before_checkout_display_total', array($this, 'display_checkout_discount_info'), 10, 1);

        // --- SCRIPTS & STYLES ---
        add_action('wp_enqueue_scripts', array($this, 'enqueue_discount_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Initialize the discount session if available
        $this->maybe_initialize_discount_session();
    }

    /**
     * CRITICAL: Directly modify cart items in the database before payment processing
     * This method runs at priority 0 to ensure it happens before any other processing
     * FIXED: Removed the flag check that was preventing database modification
     */
    public function modify_cart_database_directly() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            error_log('CMS Referral: No active discount for database modification');
            return;
        }

        error_log('CMS Referral: *** STARTING CRITICAL DATABASE CART MODIFICATION ***');

        // Get current user ID
        if (!function_exists('get_current_user_id')) {
            error_log('CMS Referral: get_current_user_id function not available');
            return;
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            error_log('CMS Referral: No user ID for database modification');
            return;
        }

        // Access the database directly
        global $wpdb;
        $cart_table = $wpdb->prefix . 'stm_lms_user_cart';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$cart_table}'");
        if (!$table_exists) {
            error_log('CMS Referral: Cart table does not exist: ' . $cart_table);
            return;
        }

        // Get all cart items for this user
        $cart_items = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$cart_table} WHERE user_id = %d",
            $user_id
        ), ARRAY_A);

        if (empty($cart_items)) {
            error_log('CMS Referral: No cart items found in database for user ' . $user_id);
            return;
        }

        error_log('CMS Referral: Found ' . count($cart_items) . ' cart items in database');

        // Get discount settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        error_log("CMS Referral: Applying {$admin_discount_type} discount of {$admin_discount_amount}");

        // Check if any item already has a discount applied by checking for stored original prices
        $already_discounted = false;
        foreach ($cart_items as $item) {
            $item_id = $item['item_id'];
            $current_price = floatval($item['price']);

            // Check if we have stored the original price for this item
            $stored_original = get_user_meta($user_id, '_cms_original_price_' . $item_id, true);
            if (!empty($stored_original) && floatval($stored_original) > $current_price) {
                $already_discounted = true;
                error_log("CMS Referral: Item {$item_id} already discounted - stored original: {$stored_original}, current: {$current_price}");
                break;
            }
        }

        if ($already_discounted) {
            error_log('CMS Referral: Cart items already have discount applied, skipping database modification');
            return;
        }

        // Apply discount to each cart item directly in the database
        $updated_count = 0;
        foreach ($cart_items as $item) {
            $item_id = $item['item_id'];
            $original_price = floatval($item['price']);

            // Store the original price before modification
            update_user_meta($user_id, '_cms_original_price_' . $item_id, $original_price);

            $discounted_price = $this->calculate_discounted_price($original_price, true);

            if (abs($discounted_price - $original_price) > 0.01) {
                // Update the price directly in the database
                $updated = $wpdb->update(
                    $cart_table,
                    array('price' => $discounted_price),
                    array(
                        'user_id' => $user_id,
                        'item_id' => $item_id
                    ),
                    array('%f'),
                    array('%d', '%d')
                );

                if ($updated !== false) {
                    $updated_count++;
                    error_log("CMS Referral: *** SUCCESSFULLY UPDATED CART ITEM {$item_id} PRICE: {$original_price} -> {$discounted_price} ***");
                } else {
                    error_log("CMS Referral: *** FAILED TO UPDATE CART ITEM {$item_id} IN DATABASE ***");
                    error_log("CMS Referral: Database error: " . $wpdb->last_error);
                }
            }
        }

        // Log the final result
        if ($updated_count > 0) {
            error_log("CMS Referral: *** DATABASE MODIFICATION SUCCESSFUL - UPDATED {$updated_count} ITEMS ***");
        } else {
            error_log("CMS Referral: *** DATABASE MODIFICATION FAILED - NO ITEMS UPDATED ***");
        }

        error_log("CMS Referral: *** DIRECT DATABASE CART MODIFICATION COMPLETE ***");
    }

    /**
     * Setup additional purchase hooks to ensure database modification happens
     * This method is called on init and wp_loaded to catch the purchase process
     */
    public function setup_purchase_hooks() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Add additional hooks to catch purchase at different stages
        add_action('wp_ajax_stm_lms_purchase', array($this, 'modify_cart_database_directly'), -10);
        add_action('wp_ajax_nopriv_stm_lms_purchase', array($this, 'modify_cart_database_directly'), -10);

        // Hook into MasterStudy LMS specific actions if they exist
        if (has_action('stm_lms_before_purchase')) {
            add_action('stm_lms_before_purchase', array($this, 'modify_cart_database_directly'), 1);
        }

        error_log('CMS Referral: Additional purchase hooks setup complete');
    }

    /**
     * Apply discount to cart items for UI display only (does not affect payment processing)
     * This method updates the frontend display without interfering with the database approach
     */
    public function apply_discount_to_cart_items_ui($cart_items) {
        // Only proceed if we have an active discount and this is not during payment processing
        if (!$this->active_discount || $this->is_payment_processing()) {
            return $cart_items;
        }

        // Don't modify if we're in the payment AJAX call
        if (defined('DOING_AJAX') && DOING_AJAX && isset($_POST['action']) && $_POST['action'] === 'stm_lms_purchase') {
            return $cart_items;
        }

        if (empty($cart_items) || !is_array($cart_items)) {
            return $cart_items;
        }

        // Apply discount to each cart item for display purposes only
        foreach ($cart_items as $key => $item) {
            if (isset($item['price']) && is_numeric($item['price'])) {
                $original_price = floatval($item['price']);
                $discounted_price = $this->calculate_discounted_price($original_price);

                // Only update if there's a meaningful difference
                if (abs($discounted_price - $original_price) > 0.01) {
                    $cart_items[$key]['price'] = $discounted_price;
                    $cart_items[$key]['original_price'] = $original_price;
                    $cart_items[$key]['discount_applied'] = true;
                }
            }
        }

        return $cart_items;
    }

    /**
     * Apply discount to checkout total for UI display only (does not affect payment processing)
     */
    public function apply_discount_to_checkout_total_ui($total) {
        // Only proceed if we have an active discount and this is not during payment processing
        if (!$this->active_discount || $this->is_payment_processing()) {
            return $total;
        }

        // Don't modify if we're in the payment AJAX call
        if (defined('DOING_AJAX') && DOING_AJAX && isset($_POST['action']) && $_POST['action'] === 'stm_lms_purchase') {
            return $total;
        }

        if (!is_numeric($total) || $total <= 0) {
            return $total;
        }

        $discounted_total = $this->calculate_discounted_price(floatval($total));
        return max(0, $discounted_total);
    }

    /**
     * Check if we're currently processing a payment
     */
    private function is_payment_processing() {
        // Check if we're in a payment-related AJAX call
        if (defined('DOING_AJAX') && DOING_AJAX) {
            $payment_actions = ['stm_lms_purchase', 'stm_lms_checkout', 'stm_lms_payment'];
            if (isset($_POST['action']) && in_array($_POST['action'], $payment_actions)) {
                return true;
            }
        }

        // Check if the discount flag is set (indicating payment processing)
        return get_option('_custom_cms_discount_already_applied', 0);
    }

    /**
     * CRITICAL: Intercept the AJAX purchase call to modify cart items
     * This is the most important hook - it runs before payment processing
     */
    public function intercept_purchase_ajax() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Get current user
        if (!function_exists('get_current_user_id')) {
            return;
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            return;
        }

        error_log('CMS Referral: Intercepting AJAX purchase call');

        // CRITICAL: Override the stm_lms_get_cart_items function temporarily
        // We need to modify the cart items before they're passed to get_cart_totals()
        add_filter('stm_lms_cart_items_fields', function($fields) {
            // Ensure we get all necessary fields
            if (!in_array('price', $fields)) {
                $fields[] = 'price';
            }
            if (!in_array('item_id', $fields)) {
                $fields[] = 'item_id';
            }
            return $fields;
        }, 1);

        // CRITICAL: This is the key - we need to modify the result of stm_lms_get_cart_items
        // when it's called during payment processing
        add_filter('stm_lms_get_cart_items_result', array($this, 'modify_cart_items_for_payment'), 1, 2);

        // Also add a global filter to catch the cart items
        global $stm_lms_cart_items_override;
        $stm_lms_cart_items_override = array($this, 'modify_cart_items_for_payment');

        // CRITICAL: Override the global cart items function temporarily
        // This is the nuclear option - we replace the function during payment processing
        $this->setup_function_override();

        error_log('CMS Referral: Set up cart item modification filters');
    }

    /**
     * CRITICAL: Modify cart items specifically for payment processing
     * This applies the discount to cart items when they're retrieved for payment
     */
    public function modify_cart_items_for_payment($cart_items, $user_id = null) {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return $cart_items;
        }

        if (empty($cart_items) || !is_array($cart_items)) {
            return $cart_items;
        }

        error_log('CMS Referral: Modifying cart items for payment - ' . count($cart_items) . ' items');

        // Apply discount to each cart item
        foreach ($cart_items as $key => $cart_item) {
            if (isset($cart_item['price']) && is_numeric($cart_item['price'])) {
                $original_price = floatval($cart_item['price']);
                $discounted_price = $this->calculate_discounted_price($original_price, true);

                // Update the price in the cart item
                $cart_items[$key]['price'] = $discounted_price;

                error_log("CMS Referral: Modified cart item price: {$original_price} -> {$discounted_price}");
            }
        }

        return $cart_items;
    }

    /**
     * CRITICAL: Setup function override for payment processing
     * This is the nuclear option - we override the MasterStudy function during payment
     */
    public function setup_function_override() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Set up a global variable to track when we're in payment processing
        global $cms_referral_payment_processing;
        $cms_referral_payment_processing = false;

        // Hook into the AJAX action to set the flag
        add_action('wp_ajax_stm_lms_purchase', function() {
            global $cms_referral_payment_processing;
            $cms_referral_payment_processing = true;
            error_log('CMS Referral: Payment processing flag set to TRUE');
        }, 0); // Priority 0 to run before everything else

        // Hook into the end of AJAX action to clear the flag
        add_action('wp_ajax_stm_lms_purchase', function() {
            global $cms_referral_payment_processing;
            $cms_referral_payment_processing = false;
            error_log('CMS Referral: Payment processing flag set to FALSE');
        }, 999); // Priority 999 to run after everything else

        // CRITICAL: Override the stm_lms_get_cart_items function when called during payment
        if (!function_exists('stm_lms_get_cart_items_original')) {
            // Create a backup of the original function
            if (function_exists('stm_lms_get_cart_items')) {
                // We can't actually override a function, but we can use output buffering
                // and global variables to intercept the result
                $this->setup_cart_items_interception();
            }
        }

        error_log('CMS Referral: Function override setup complete');
    }

    /**
     * Setup cart items interception using global variables and hooks
     */
    private function setup_cart_items_interception() {
        // Use a global variable to store our override function
        global $cms_referral_cart_override;
        $cms_referral_cart_override = array($this, 'override_cart_items_during_payment');

        // Hook into WordPress to intercept function calls
        add_filter('pre_option_stm_lms_cart_items', array($this, 'intercept_cart_option'), 10, 1);

        error_log('CMS Referral: Cart items interception setup complete');
    }

    /**
     * Override cart items specifically during payment processing
     */
    public function override_cart_items_during_payment($user_id, $fields = array()) {
        global $cms_referral_payment_processing;

        // Only override during payment processing
        if (!$cms_referral_payment_processing || !$this->active_discount) {
            // Call the original function if available
            if (function_exists('stm_lms_get_cart_items')) {
                return stm_lms_get_cart_items($user_id, $fields);
            }
            return array();
        }

        error_log('CMS Referral: Overriding cart items during payment processing');

        // Get the original cart items
        $cart_items = array();
        if (function_exists('stm_lms_get_cart_items')) {
            // Temporarily disable our override to prevent recursion
            global $cms_referral_payment_processing;
            $original_flag = $cms_referral_payment_processing;
            $cms_referral_payment_processing = false;

            $cart_items = stm_lms_get_cart_items($user_id, $fields);

            // Restore the flag
            $cms_referral_payment_processing = $original_flag;
        }

        // Apply discount to the cart items
        return $this->modify_cart_items_for_payment($cart_items, $user_id);
    }

    /**
     * Intercept cart option to apply discounts
     */
    public function intercept_cart_option($value) {
        global $cms_referral_payment_processing;

        // Only intercept during payment processing
        if (!$cms_referral_payment_processing || !$this->active_discount) {
            return $value;
        }

        // If we have cart items, apply discount
        if (is_array($value) && !empty($value)) {
            return $this->modify_cart_items_for_payment($value);
        }

        return $value;
    }

    /**
     * Ensure price field is included in cart items fields
     */
    public function ensure_price_field_included($fields) {
        if (!in_array('price', $fields)) {
            $fields[] = 'price';
        }
        return $fields;
    }

    /**
     * CRITICAL: Modify cart totals for payment processing
     * This hooks into the get_cart_totals calculation
     */
    public function modify_cart_totals_for_payment($totals) {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return $totals;
        }

        // If totals is an array with 'total' key, modify it
        if (is_array($totals) && isset($totals['total'])) {
            $original_total = $totals['total'];
            $discounted_total = $this->calculate_discounted_price($original_total, true);
            $totals['total'] = $discounted_total;

            error_log("CMS Referral: Modified cart totals for payment: {$original_total} -> {$discounted_total}");
        }

        return $totals;
    }

    /**
     * CRITICAL: Modify cart totals result
     * This is a fallback to catch any cart total calculations
     */
    public function modify_cart_totals_result($result) {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return $result;
        }

        // If result is a number, apply discount
        if (is_numeric($result)) {
            $discounted_result = $this->calculate_discounted_price($result, true);
            error_log("CMS Referral: Modified cart totals result: {$result} -> {$discounted_result}");
            return $discounted_result;
        }

        // If result is an array with total, modify it
        if (is_array($result) && isset($result['total'])) {
            $original_total = $result['total'];
            $discounted_total = $this->calculate_discounted_price($original_total, true);
            $result['total'] = $discounted_total;

            error_log("CMS Referral: Modified cart totals result array: {$original_total} -> {$discounted_total}");
        }

        return $result;
    }

    /**
     * Override the stm_lms_get_cart_items function to add discount filtering
     * Since the core function has no filters, we need to intercept it at the WordPress level
     */
    public function override_cart_items_function() {
        // Only override if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Add a filter to the result of stm_lms_get_cart_items calls
        // We'll use output buffering and function replacement
        if (!function_exists('stm_lms_get_cart_items_with_discount')) {
            function stm_lms_get_cart_items_with_discount($user_id, $fields = array()) {
                // Get the original cart items
                global $wpdb;
                $table = stm_lms_user_cart_name($wpdb);
                $fields = (empty($fields)) ? '*' : implode(',', $fields);

                $cart_items = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT {$fields} FROM {$table} WHERE user_ID = %d",
                        $user_id
                    ),
                    ARRAY_A
                );

                // Apply discount if we have an active discount handler
                global $custom_cms_referral_discount_handler;
                if ($custom_cms_referral_discount_handler && $custom_cms_referral_discount_handler->active_discount) {
                    $cart_items = $custom_cms_referral_discount_handler->intercept_cart_items($cart_items, $user_id);
                }

                return $cart_items;
            }
        }
    }

    /**
     * Add JavaScript to update checkout prices on the frontend
     * This is a fallback method to ensure prices are displayed correctly
     */
    public function add_checkout_price_update_script() {
        // Only add script if we have an active discount and we're on checkout page
        if (!$this->active_discount) {
            return;
        }

        // Check if we're on the checkout page
        $is_checkout = (is_page() && strpos($_SERVER['REQUEST_URI'], 'checkout') !== false) ||
                      strpos($_SERVER['REQUEST_URI'], 'checkout') !== false;

        if (!$is_checkout) {
            return;
        }

        $discount_info = $this->get_active_discount();
        if (!$discount_info) {
            return;
        }

        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            console.log('CMS Referral: Adding checkout price update script');

            // Discount information
            var discountType = '<?php echo esc_js($discount_info['type']); ?>';
            var discountAmount = <?php echo floatval($discount_info['amount']); ?>;
            var discountCode = '<?php echo esc_js($discount_info['code']); ?>';
            var discountFormatted = '<?php echo esc_js($discount_info['formatted']); ?>';

            // Function to apply discount to a price
            function applyDiscount(originalPrice) {
                if (isNaN(originalPrice) || originalPrice <= 0) return originalPrice;

                var discountedPrice;
                if (discountType === 'percentage') {
                    discountedPrice = originalPrice - (originalPrice * (discountAmount / 100));
                } else {
                    discountedPrice = originalPrice - discountAmount;
                }

                return Math.max(0, discountedPrice);
            }

            // Function to update price elements
            function updatePriceElements() {
                console.log('CMS Referral: Updating price elements');

                // Update individual item prices
                $('.masterstudy-checkout-course-info__price span').each(function() {
                    var $element = $(this);
                    var priceText = $element.text();

                    // Skip if already processed
                    if ($element.hasClass('cms-discount-processed')) {
                        return;
                    }

                    // Extract price from text (remove currency symbols and formatting)
                    var priceMatch = priceText.match(/[\d,]+\.?\d*/);
                    if (priceMatch) {
                        var originalPrice = parseFloat(priceMatch[0].replace(/,/g, ''));
                        var discountedPrice = applyDiscount(originalPrice);

                        if (Math.abs(discountedPrice - originalPrice) > 0.01) {
                            // Replace the price with discounted price
                            var newPriceText = priceText.replace(priceMatch[0], discountedPrice.toFixed(0));
                            $element.html(newPriceText);
                            $element.addClass('cms-discount-processed');

                            console.log('Updated item price: ' + originalPrice + ' -> ' + discountedPrice);
                        }
                    }
                });

                // Update total price
                $('.masterstudy-checkout-table__footer .masterstudy-checkout-course-info__price').each(function() {
                    var $element = $(this);
                    var priceText = $element.text();

                    // Skip if already processed
                    if ($element.hasClass('cms-discount-processed')) {
                        return;
                    }

                    // Extract price from text
                    var priceMatch = priceText.match(/[\d,]+\.?\d*/);
                    if (priceMatch) {
                        var originalPrice = parseFloat(priceMatch[0].replace(/,/g, ''));
                        var discountedPrice = applyDiscount(originalPrice);

                        if (Math.abs(discountedPrice - originalPrice) > 0.01) {
                            // Replace the price with discounted price
                            var newPriceText = priceText.replace(priceMatch[0], discountedPrice.toFixed(0));
                            $element.html(newPriceText);
                            $element.addClass('cms-discount-processed');

                            console.log('Updated total price: ' + originalPrice + ' -> ' + discountedPrice);
                        }
                    }
                });

                // Update pay button
                $('.stm_lms_pay_button span').each(function() {
                    var $element = $(this);
                    var buttonText = $element.text();

                    // Skip if already processed
                    if ($element.hasClass('cms-discount-processed')) {
                        return;
                    }

                    // Extract price from button text
                    var priceMatch = buttonText.match(/[\d,]+\.?\d*/);
                    if (priceMatch) {
                        var originalPrice = parseFloat(priceMatch[0].replace(/,/g, ''));
                        var discountedPrice = applyDiscount(originalPrice);

                        if (Math.abs(discountedPrice - originalPrice) > 0.01) {
                            // Replace the price with discounted price
                            var newButtonText = buttonText.replace(priceMatch[0], discountedPrice.toFixed(0));
                            $element.html(newButtonText);
                            $element.addClass('cms-discount-processed');

                            console.log('Updated pay button: ' + originalPrice + ' -> ' + discountedPrice);
                        }
                    }
                });
            }

            // Add discount notification if not already present
            if ($('.discount-notification').length === 0) {
                var notification = '<div class="discount-notification" style="margin: 15px 0; padding: 10px; background-color: #e6f7e8; border-left: 4px solid #46b450; border-radius: 3px;"><strong>Discount Applied:</strong> ' + discountFormatted + ' off with code <code>' + discountCode + '</code></div>';
                $('.masterstudy-checkout-container').prepend(notification);
            }

            // Update prices immediately
            updatePriceElements();

            // Also update prices after a short delay to catch any dynamically loaded content
            setTimeout(updatePriceElements, 500);
            setTimeout(updatePriceElements, 1000);

            // Watch for any changes to the checkout area and update prices
            if (window.MutationObserver) {
                var observer = new MutationObserver(function(mutations) {
                    var shouldUpdate = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            shouldUpdate = true;
                        }
                    });

                    if (shouldUpdate) {
                        setTimeout(updatePriceElements, 100);
                    }
                });

                var checkoutContainer = document.querySelector('.masterstudy-checkout-container');
                if (checkoutContainer) {
                    observer.observe(checkoutContainer, {
                        childList: true,
                        subtree: true
                    });
                }
            }
        });
        </script>
        <?php
    }

    /**
     * Check if there's an active discount
     *
     * @return bool True if discount is active
     */
    public function has_active_discount() {
        return !empty($this->active_discount);
    }

    /**
     * Get the original price for an item (before any discounts)
     *
     * @param int $item_id The item ID
     * @param float $current_price The current price in cart
     * @return float The original price
     */
    private function get_original_price($item_id, $current_price) {
        // Check if we have the original price cached
        if (isset(self::$price_cache[$item_id])) {
            return self::$price_cache[$item_id][0]; // Return original price
        }

        // Get the original price from post meta
        $meta_price = get_post_meta($item_id, 'price', true);
        if (!empty($meta_price) && is_numeric($meta_price)) {
            return floatval($meta_price);
        }

        // Fallback to current price if we can't find the original
        return $current_price;
    }

    /**
     * Check if an item has already been discounted
     *
     * @param int $item_id The item ID
     * @param float $current_price The current price
     * @return bool True if already discounted
     */
    private function is_already_discounted($item_id, $current_price) {
        // Check if this item is marked as discounted
        if (isset(self::$discounted_items[$item_id])) {
            return true;
        }

        // Get the original price and compare
        $original_price = $this->get_original_price($item_id, $current_price);

        // If current price is significantly lower than original, it's likely already discounted
        if ($current_price < ($original_price * 0.95)) { // 5% threshold
            return true;
        }

        return false;
    }

    /**
     * Apply discount to a single price with safeguards
     *
     * @param int $item_id The item ID
     * @param float $price The current price
     * @return float The discounted price
     */
    private function apply_single_discount($item_id, $price) {
        // Check if already discounted
        if ($this->is_already_discounted($item_id, $price)) {
            error_log("SKIP: Item {$item_id} already discounted (current price: {$price})");
            return $price;
        }

        // Get the true original price
        $original_price = $this->get_original_price($item_id, $price);

        // Get discount settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Calculate discounted price
        if ($admin_discount_type === 'percentage') {
            $discount_value = ($original_price * $admin_discount_amount) / 100;
            $discounted_price = $original_price - $discount_value;
        } else {
            $discounted_price = max(0, $original_price - $admin_discount_amount);
        }

        $discounted_price = round($discounted_price, 2);

        // Cache the prices and mark as discounted
        self::$price_cache[$item_id] = [$original_price, $discounted_price];
        self::$discounted_items[$item_id] = true;

        error_log("DISCOUNT APPLIED: Item {$item_id} - Original: {$original_price}, Discounted: {$discounted_price}");

        return $discounted_price;
    }

    /**
     * Modify cart in database directly - this is the most reliable approach
     * This method directly updates the cart prices in the database
     */
    public function modify_cart_in_database() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Prevent multiple executions in the same request
        if (self::$discount_applied_this_session) {
            error_log("SKIP: Discount already applied in this session");
            return;
        }

        // Only for logged-in users (MasterStudy LMS requires login for cart)
        if (!is_user_logged_in()) {
            return;
        }

        $user_id = get_current_user_id();

        // Get current cart items from database
        if (!function_exists('stm_lms_get_cart_items')) {
            return;
        }

        $cart_items = stm_lms_get_cart_items($user_id, array('item_id', 'price', 'user_cart_id'));

        if (empty($cart_items)) {
            return;
        }

        global $wpdb;
        $cart_table = $wpdb->prefix . 'stm_lms_user_cart';
        $updated_items = 0;

        foreach ($cart_items as $item) {
            $item_id = $item['item_id'];
            $current_price = floatval($item['price']);

            // Apply single discount with safeguards
            $discounted_price = $this->apply_single_discount($item_id, $current_price);

            // Only update if price actually changed
            if (abs($discounted_price - $current_price) > 0.01) {
                $wpdb->update(
                    $cart_table,
                    array('price' => $discounted_price),
                    array('user_cart_id' => $item['user_cart_id']),
                    array('%f'),
                    array('%d')
                );

                $updated_items++;
                error_log("DATABASE UPDATE: Item {$item_id} price updated from {$current_price} to {$discounted_price}");
            }
        }

        // Mark session as processed
        self::$discount_applied_this_session = true;

        error_log("DISCOUNT SESSION COMPLETE: Updated {$updated_items} items");
    }



    /**
     * Enqueue frontend scripts and styles for discount display
     */
    public function enqueue_discount_scripts() {
        // Only enqueue if active discount is present
        if ($this->get_active_discount()) {
            // Register inline styles for discount badges
            wp_add_inline_style('stm-lms-styles', $this->get_discount_styles());

            // Add inline script for discount calculations
            wp_add_inline_script('stm-lms-scripts', $this->get_discount_scripts(), 'after');
        }
    }

    /**
     * Enqueue admin scripts for discount management
     */
    public function enqueue_admin_scripts($hook) {
        // Only on our plugin settings page
        if (strpos($hook, 'custom-cms-referral') !== false) {
            // Add inline script for admin discount handling
            wp_add_inline_script('custom-cms-referral-admin', $this->get_admin_discount_scripts(), 'after');
        }
    }

    /**
     * Get CSS styles for discount display
     *
     * @return string CSS styles
     */
    private function get_discount_styles() {
        ob_start();
        ?>
        .cms-discount-badge {
            display: inline-block;
            background-color: #ff5252;
            color: white;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-left: 10px;
            vertical-align: middle;
        }
        .cms-original-price {
            text-decoration: line-through;
            opacity: 0.7;
            margin-right: 5px;
        }
        .cms-discounted-price {
            font-weight: bold;
            color: #ff5252;
        }
        <?php
        return ob_get_clean();
    }

    /**
     * Get JavaScript for discount interactions
     *
     * @return string JavaScript code
     */
    private function get_discount_scripts() {
        ob_start();
        ?>
        (function($) {
            $(document).ready(function() {
                // Update any dynamic prices that might load after page load
                $(document).on('stm_lms_course_price_loaded', function(e, priceElement) {
                    if (window.customCmsReferralDiscount) {
                        var originalPrice = $(priceElement).text();
                        var numericPrice = parseFloat(originalPrice.replace(/[^0-9.]/g, ''));
                        if (!isNaN(numericPrice)) {
                            var discountType = window.customCmsReferralDiscount.type;
                            var discountAmount = window.customCmsReferralDiscount.amount;
                            var discountedPrice;

                            if (discountType === 'percentage') {
                                discountedPrice = numericPrice * (1 - (discountAmount / 100));
                            } else {
                                discountedPrice = Math.max(0, numericPrice - discountAmount);
                            }

                            // Format the price with the same currency symbol
                            var currencySymbol = originalPrice.replace(/[\d.,]/g, '').trim();
                            $(priceElement).html('<span class="cms-original-price">' + originalPrice + '</span><span class="cms-discounted-price">' + currencySymbol + discountedPrice.toFixed(2) + '</span><span class="cms-discount-badge">-' + window.customCmsReferralDiscount.formatted + '</span>');
                        }
                    }
                });
            });
        })(jQuery);
        <?php
        return ob_get_clean();
    }

    /**
     * Get JavaScript for admin discount management
     *
     * @return string JavaScript code
     */
    private function get_admin_discount_scripts() {
        ob_start();
        ?>
        (function($) {
            $(document).ready(function() {
                // Update discount preview when admin changes settings
                $('#custom_cms_referral_discount_type, #custom_cms_referral_discount_amount').on('change input', function() {
                    var discountType = $('#custom_cms_referral_discount_type').val();
                    var discountAmount = parseFloat($('#custom_cms_referral_discount_amount').val());

                    if (!isNaN(discountAmount)) {
                        var formattedDiscount = '';
                        if (discountType === 'percentage') {
                            formattedDiscount = discountAmount + '%';
                        } else {
                            formattedDiscount = '₹' + discountAmount.toFixed(2);
                        }

                        $('.discount-preview').text(formattedDiscount);
                    }
                });
            });
        })(jQuery);
        <?php
        return ob_get_clean();
    }

    /**
     * Generate cart refresh script
     *
     * @param array $discount_info Discount information
     * @return string JavaScript code
     */
    public function generate_cart_refresh_script($discount_info) {
        // Before generating the refresh script, directly modify the cart items
        $this->force_update_cart_with_discount();

        ob_start();
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Force page reload if there are course cards visible
            var courseCards = $('.stm_lms_courses__single');
            if (courseCards.length > 0) {
                location.reload();
                return;
            }

            // Update cart display
            setTimeout(function() {
                // Check if we're on a cart page
                if ($('.stm_lms_cart_page').length > 0 || $('.stm-lms-wrapper').length > 0) {
                    if (typeof stm_lms_get_cart !== 'undefined') {
                        // MasterStudy LMS has a function to reload cart contents
                        // This will trigger the app to fetch updated cart with discounted prices
                        stm_lms_get_cart();
                    } else {
                        // Fallback - just reload the page
                        location.reload();
                    }
                }
            }, 500);

            // If the user is on the checkout page, refresh the cart contents
            if ($('.stm_lms_checkout').length > 0 || $('.masterstudy-checkout-table').length > 0) {
                location.reload();
            }
        });
        </script>
        <?php
        $script = ob_get_clean();
        return $script;
    }

    /**
     * Initialize discount session if a referral code cookie exists or URL parameter is present
     */
    public function maybe_initialize_discount_session() {
        // Check for URL parameter first (highest priority)
        // This handles the case when a user just applied a discount code and the page refreshed
        if (isset($_GET['apply_discount']) && isset($_GET['code'])) {
            $code = sanitize_text_field($_GET['code']);

            // Initialize referral codes class
            global $custom_cms_referral_codes;
            if (isset($custom_cms_referral_codes) && is_object($custom_cms_referral_codes)) {
                // Validate the referral code
                $referrer_id = $custom_cms_referral_codes->validate_referral_code($code);

                if ($referrer_id) {
                    // Get discount settings
                    $discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
                    $discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));

                    // Set the active discount with forced refresh
                    $success = $this->set_active_discount($code, $referrer_id, $discount_type, $discount_amount);

                    if ($success) {
                        // Add JavaScript to modify cart/checkout dynamically
                        add_action('wp_footer', function() {
                            $discount_info = $this->get_active_discount();
                            echo '<script>jQuery(document).ready(function($) { window.location.href = window.location.href.split("?")[0]; });</script>';
                        });

                        // Return early since we've already set the discount
                        return;
                    }
                }
            }
        }

        // Check for cookie next
        if (isset($_COOKIE[self::DISCOUNT_COOKIE])) {
            $cookie_data = json_decode(stripslashes($_COOKIE[self::DISCOUNT_COOKIE]), true);
            if ($this->is_valid_discount_data($cookie_data)) {
                // Ensure we have the correct discount amount from settings
                $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
                $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

                // Only override if the types match to prevent incorrect calculations
                if ($cookie_data['type'] === $admin_discount_type) {
                    $cookie_data['amount'] = $admin_discount_amount;

                    // Update formatted value
                    if ($admin_discount_type === 'percentage') {
                        $cookie_data['formatted'] = $admin_discount_amount . '%';
                    } else {
                        $currency = function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '₹';
                        $cookie_data['formatted'] = $currency . number_format($admin_discount_amount, 2);
                    }
                }

                $this->active_discount = $cookie_data;
                return;
            }
        }

        // If no cookie, check for user-specific transient
        if (function_exists('is_user_logged_in') && is_user_logged_in()) {
            $user_id = get_current_user_id();
            $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
            $transient_data = get_transient($transient_key);

            if ($this->is_valid_discount_data($transient_data)) {
                // Ensure we have the correct discount amount from settings
                $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
                $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

                // Only override if the types match to prevent incorrect calculations
                if ($transient_data['type'] === $admin_discount_type) {
                    $transient_data['amount'] = $admin_discount_amount;

                    // Update formatted value
                    if ($admin_discount_type === 'percentage') {
                        $transient_data['formatted'] = $admin_discount_amount . '%';
                    } else {
                        $currency = function_exists('get_woocommerce_currency_symbol') ? get_woocommerce_currency_symbol() : '₹';
                        $transient_data['formatted'] = $currency . number_format($admin_discount_amount, 2);
                    }
                }

                $this->active_discount = $transient_data;
            }
        }

        // Add a hook to check user login status after WordPress is fully loaded
        add_action('init', array($this, 'initialize_user_discount_data'));

        // Set global JavaScript variable for discount info
        if ($this->active_discount) {
            add_action('wp_head', function() {
                $discount_info = $this->get_active_discount();
                echo '<script>window.customCmsReferralDiscount = ' . json_encode($discount_info) . ';</script>';
            });
        }
    }

    /**
     * Validate discount data structure
     *
     * @param mixed $data The discount data to validate
     * @return bool Whether the data is valid
     */
    private function is_valid_discount_data($data) {
        if (!is_array($data) || empty($data)) {
            return false;
        }

        // Check required fields
        $required_fields = array('code', 'referrer_id', 'type', 'amount', 'formatted');
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                error_log("Missing required discount field: {$field}");
                return false;
            }
        }

        // Validate discount type
        if (!in_array($data['type'], array('percentage', 'fixed'))) {
            error_log("Invalid discount type: {$data['type']}");
            return false;
        }

        // Validate amount is numeric
        if (!is_numeric($data['amount'])) {
            error_log("Invalid discount amount: {$data['amount']}");
            return false;
        }

        return true;
    }

    /**
     * Initialize discount data for logged-in users
     * This runs after WordPress is fully loaded
     */
    public function initialize_user_discount_data() {
        // Only proceed if we don't already have an active discount from cookies
        if ($this->active_discount === null && function_exists('is_user_logged_in') && is_user_logged_in()) {
            // For logged-in users, check for stored discount in user meta
            $user_id = get_current_user_id();
            $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
            $discount_data = get_transient($transient_key);

            if ($discount_data) {
                $this->active_discount = $discount_data;

                // Also set cookie for cross-session persistence
                $this->set_discount_cookie($discount_data);
            }
        }
    }

    /**
     * Set discount cookie
     *
     * @param array $discount_data Discount data to store
     */
    private function set_discount_cookie($discount_data) {
        // Get cookie duration from admin settings (fallback to 7 days for backward compatibility)
        $cookie_duration_days = get_option('custom_cms_referral_cookie_duration', 7);

        setcookie(
            self::DISCOUNT_COOKIE,
            json_encode($discount_data),
            time() + ($cookie_duration_days * DAY_IN_SECONDS),
            COOKIEPATH,
            COOKIE_DOMAIN,
            is_ssl(),
            true
        );
    }

    /**
     * Apply discount to a single price value
     *
     * @param float $price Original price
     * @return float Discounted price
     */
    public function apply_discount_to_price($price) {
        // If no active discount or invalid price, return original price
        if (!$this->active_discount || !is_numeric($price)) {
            return $price;
        }

        // Apply discount and return
        return $this->calculate_discounted_price($price);
    }

    /**
     * Apply discount to course price
     *
     * @param float $price Original course price
     * @param array $course_meta Course meta data
     * @return float Discounted price
     */
    public function apply_discount_to_course_price($price, $course_meta = array()) {
        // Skip discount if not active
        if (!$this->active_discount) {
            return $price;
        }

        // Skip if price is already 0
        if (empty($price) || $price <= 0) {
            return $price;
        }

        // Get current discount settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Calculate discounted price directly to ensure consistency
        if ($admin_discount_type === 'percentage') {
            $discount_value = ($price * $admin_discount_amount) / 100;
            $discounted_price = $price - $discount_value;
        } else {
            $discounted_price = $price - $admin_discount_amount;
        }

        // Ensure price doesn't go below zero
        return max(0, $discounted_price);
    }

    /**
     * Filter the displayed price on course pages
     *
     * @param string $displayed_price Formatted price string
     * @param float $price Numeric price value
     * @return string Modified displayed price
     */
    public function filter_displayed_price($displayed_price, $price) {
        // If no active discount, return original price display
        if (!$this->active_discount) {
            return $displayed_price;
        }

        // Calculate discounted price
        $discounted_price = $this->calculate_discounted_price($price);

        // Get discount info
        $discount_info = $this->get_active_discount();

        // Format the display with strikethrough and discount badge
        $new_display = '<span class="cms-original-price">' . $displayed_price . '</span>';

        // Use STM LMS formatting function if available, otherwise basic formatting
        if (function_exists('STM_LMS_Helpers::display_price')) {
            $new_display .= '<span class="cms-discounted-price">' . STM_LMS_Helpers::display_price($discounted_price) . '</span>';
        } else {
            $currency = get_woocommerce_currency_symbol();
            $new_display .= '<span class="cms-discounted-price">' . $currency . number_format($discounted_price, 2) . '</span>';
        }

        $new_display .= '<span class="cms-discount-badge">-' . $discount_info['formatted'] . '</span>';

        return $new_display;
    }

    /**
     * Apply discount to cart items
     *
     * This is our SINGLE SOURCE OF TRUTH for discount application.
     * All discount calculations happen here and only here.
     *
     * @param array $cart_items Cart items
     * @return array Modified cart items
     */
    public function apply_discount_to_cart_items($cart_items) {
        // If no active discount or no items, return original cart
        if (!$this->active_discount || empty($cart_items)) {
            return $cart_items;
        }

        // CRITICAL: Check if we're already in a discount processing loop
        static $processing = false;
        if ($processing) {
            error_log("PROTECTION: Preventing recursive discount application in apply_discount_to_cart_items");
            return $cart_items;
        }

        // Set processing flag to prevent recursion
        $processing = true;

        // Get discount settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Flag to track if we're running the first discount pass
        $first_discount_pass = !get_option('_custom_cms_discount_already_applied');
        if ($first_discount_pass) {
            // Set flag to indicate first discount has been applied
            update_option('_custom_cms_discount_already_applied', 1);

            // Clear price cache on first pass to ensure fresh calculations
            self::$price_cache = [];
            error_log("FIRST DISCOUNT PASS: Cleared price cache");
        }

        // Apply discount to each cart item using our cache system
        foreach ($cart_items as $key => $item) {
            if (!isset($item['price']) || !isset($item['item_id'])) continue;

            $item_id = $item['item_id'];
            $current_price = floatval($item['price']);

            // Skip items already marked as discounted in the current session
            if (isset($item['has_discount']) && $item['has_discount'] === true) {
                error_log("SKIP: Item {$item_id} already marked as discounted");
                continue;
            }

            // Check if this item already has a cached discount
            if (isset(self::$price_cache[$item_id])) {
                // Item already has a calculated discount - use the cached value
                list($cached_original, $cached_discount) = self::$price_cache[$item_id];

                // If current price is already the discounted price (within a small margin of error),
                // we don't need to apply the discount again
                if (abs($current_price - $cached_discount) < 0.01) {
                    error_log("SKIP: Item {$item_id} price {$current_price} already matches cached discount {$cached_discount}");
                    // Mark as discounted and continue
                    $cart_items[$key]['has_discount'] = true;
                    continue;
                }

                // If current price matches the original price, apply the cached discount
                if (abs($current_price - $cached_original) < 0.01) {
                    $cart_items[$key]['price'] = $cached_discount;
                    $cart_items[$key]['original_price'] = $cached_original;
                    $cart_items[$key]['has_discount'] = true;
                    error_log("CACHE APPLIED: Item {$item_id} price updated from {$cached_original} to {$cached_discount}");
                    continue;
                }

                // If we're here, the price has changed - log it and recalculate
                error_log("PRICE CHANGED: Item {$item_id} price {$current_price} doesn't match cached original {$cached_original} or discount {$cached_discount}");
            }

            // Calculate a new discount for this item
            $original_price = $current_price;
            $discounted_price = $original_price;

            if ($admin_discount_type === 'percentage') {
                // Apply percentage discount
                $discount_value = ($original_price * $admin_discount_amount) / 100;
                $discounted_price = $original_price - $discount_value;
                error_log("NEW DISCOUNT: Item {$item_id} - {$admin_discount_amount}% off {$original_price} = {$discounted_price}");
            } else {
                // Apply fixed discount (ensuring price doesn't go below zero)
                $discount_value = min($original_price, $admin_discount_amount);
                $discounted_price = max(0, $original_price - $discount_value);
                error_log("NEW DISCOUNT: Item {$item_id} - {$admin_discount_amount} off {$original_price} = {$discounted_price}");
            }

            // Round to 2 decimal places
            $discounted_price = round($discounted_price, 2);

            // Sanity check - ensure discount actually reduced the price
            if ($discounted_price >= $original_price) {
                $discounted_price = $original_price * 0.8; // 20% discount as failsafe
                error_log("FAILSAFE: Using 20% discount for item {$item_id}: {$original_price} -> {$discounted_price}");
            }

            // Cache the original and discounted prices
            self::$price_cache[$item_id] = [$original_price, $discounted_price];

            // Update cart item price
            $cart_items[$key]['price'] = $discounted_price;

            // Store original price for reference
            $cart_items[$key]['original_price'] = $original_price;
            $cart_items[$key]['has_discount'] = true;

            // Store discount info with the item
            $cart_items[$key]['discount_info'] = array(
                'type' => $admin_discount_type,
                'amount' => $admin_discount_amount,
                'original_price' => $original_price,
                'discount_value' => $original_price - $discounted_price
            );
        }

        // Reset processing flag
        $processing = false;

        return $cart_items;
    }

    /**
     * Display discount information on the checkout page
     *
     * @param float $total Current checkout total
     */
    public function display_checkout_discount_info($total) {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Get discount info
        $discount_info = $this->get_active_discount();
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Format discount for display
        $formatted_discount = $admin_discount_type === 'percentage' ?
            $admin_discount_amount . '%' :
            STM_LMS_Helpers::display_price($admin_discount_amount);

        // Output discount information
        echo '<div class="masterstudy-checkout-discount-info" style="margin-bottom: 15px; padding: 10px; background-color: #f9f9f9; border-radius: 4px;">';
        echo '<div class="discount-label" style="font-weight: bold; margin-bottom: 5px;">' .
             sprintf(esc_html__('Referral Code %s Applied', 'custom-cms-referal'),
             '<span style="color: #4986e7;">' . esc_html($discount_info['code']) . '</span>') .
             '</div>';
        echo '<div class="discount-value">' .
             sprintf(esc_html__('Discount: %s', 'custom-cms-referal'),
             '<span style="color: #16a085; font-weight: bold;">' . esc_html($formatted_discount) . '</span>') .
             '</div>';
        echo '</div>';
    }

    /**
     * AJAX handler for applying discounts
     */
    public function ajax_apply_discount() {
        // Check nonce for security - support multiple nonce parameter names for compatibility
        $nonce = '';
        if (isset($_POST['nonce'])) {
            $nonce = $_POST['nonce'];
            $nonce_action = 'custom-cms-referral-nonce';
        } elseif (isset($_POST['security'])) {
            $nonce = $_POST['security'];
            $nonce_action = 'custom-cms-referral-form';
        }

        if (empty($nonce) || !wp_verify_nonce($nonce, $nonce_action)) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'custom-cms-referal')
            ));
            return;
        }

        // Get referral code from request - support multiple parameter names
        $code = '';
        if (isset($_POST['referral_code'])) {
            $code = sanitize_text_field($_POST['referral_code']);
        } elseif (isset($_POST['code'])) {
            $code = sanitize_text_field($_POST['code']);
        }

        if (empty($code)) {
            wp_send_json_error(array(
                'message' => __('Please enter a referral code', 'custom-cms-referal')
            ));
            return;
        }

        // Verify referral code using the existing validation method
        $referral_codes = new Custom_CMS_Referral_Codes();
        $referrer_id = $referral_codes->validate_referral_code($code);

        if (!$referrer_id) {
            wp_send_json_error(array(
                'message' => __('Invalid referral code', 'custom-cms-referal')
            ));
            return;
        }

        // Get discount settings
        $discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
        $discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));

        // Set the active discount
        $success = $this->set_active_discount($code, $referrer_id, $discount_type, $discount_amount);

        if ($success) {
            $discount_info = $this->get_active_discount();

            // Generate cart refresh JavaScript
            $refresh_script = $this->generate_refresh_script_internal($discount_info);

            wp_send_json_success(array(
                'message' => sprintf(
                    __('Referral code applied! You will receive a %s discount on your purchase.', 'custom-cms-referal'),
                    '<strong>' . $discount_info['formatted'] . '</strong>'
                ),
                'discount' => $discount_info,
                'refresh_script' => $refresh_script,
                'refresh' => true
            ));
        } else {
            wp_send_json_error(array(
                'message' => __('Error applying discount. Please try again.', 'custom-cms-referal')
            ));
        }
    }

    /**
     * Generate JavaScript to refresh cart display (internal implementation)
     *
     * @param array $discount_info Discount information
     * @return string JavaScript code for refreshing cart
     */
    private function generate_refresh_script_internal($discount_info) {
        // Format discount percentage for display
        $formatted_discount = $discount_info['formatted'];
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));

        ob_start();
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Force immediate refresh with special parameter to ensure discount is applied
            var currentUrl = window.location.href;
            var separator = currentUrl.indexOf('?') > -1 ? '&' : '?';
            var refreshUrl = currentUrl + separator + 'apply_discount=1&code=<?php echo esc_js($discount_info['code']); ?>&t=' + new Date().getTime();

            // Show notification before refresh
            var noticeHtml = '<div class="discount-applied-message" style="position: fixed; top: 30%; left: 50%; transform: translate(-50%, -50%); z-index: 9999; ' +
                             'padding: 20px; background-color: rgba(255, 255, 255, 0.95); border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); ' +
                             'text-align: center; max-width: 90%; width: 400px;">' +
                             '<div style="font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #4986e7;">Referral Code Applied!</div>' +
                             '<div style="margin-bottom: 15px;">Your <?php echo esc_js($formatted_discount); ?> discount will be applied.</div>' +
                             '<div style="font-size: 14px; color: #666;">Refreshing page to update prices...</div>' +
                             '<div class="spinner" style="margin: 15px auto; width: 50px; height: 50px; border: 3px solid #f3f3f3; ' +
                             'border-top: 3px solid #4986e7; border-radius: 50%; animation: spin 1s linear infinite;"></div>' +
                             '</div>';

            // Add animation for spinner
            $('<style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>').appendTo('head');

            // Show notice and overlay
            $('body').append('<div id="discount-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.4); z-index: 9998;"></div>');
            $('body').append(noticeHtml);

            // Store discount flag in session storage to ensure it persists through page reload
            if (window.sessionStorage) {
                sessionStorage.setItem('cms_discount_applied', 'true');
                sessionStorage.setItem('cms_discount_amount', '<?php echo esc_js($admin_discount_amount); ?>');
                sessionStorage.setItem('cms_discount_code', '<?php echo esc_js($discount_info['code']); ?>');
            }

            // Force refresh after short delay
            setTimeout(function() {
                window.location.href = refreshUrl;
            }, 800);
        });
        </script>
        <?php
        return ob_get_clean();
    }



    /**
     * Force update the cart when discount is active
     * This ensures MasterStudy LMS cart is always updated with the correct discounted prices
     */
    public function force_update_cart_with_discount() {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Prevent multiple executions in the same request
        if (self::$discount_applied_this_session) {
            error_log("SKIP: Discount already applied in this session for force update");
            return;
        }

        // Get current cart items if they exist in the session
        $cart_items = $this->get_masterstudy_cart_items();

        if (empty($cart_items)) {
            return;
        }

        // CRITICAL PROTECTION: Check if we're already in a discount processing loop
        static $processing = false;
        if ($processing) {
            error_log("PROTECTION: Preventing recursive discount application");
            return $cart_items;
        }
        $processing = true;

        // Get the discount information
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Set a flag to indicate that discount is being applied
        update_option('_custom_cms_discount_already_applied', 1);

        // CRITICAL - Log the original cart for debugging
        error_log('*** CART BEFORE DISCOUNT PROCESSING ***');

        // Process each cart item with single-application safeguards
        $updated_items = 0;
        foreach ($cart_items as $key => $item) {
            if (!isset($item['price']) || !isset($item['item_id'])) continue;

            $item_id = $item['item_id'];
            $current_price = floatval($item['price']);

            // Apply single discount with safeguards
            $discounted_price = $this->apply_single_discount($item_id, $current_price);

            // Only update if price actually changed
            if (abs($discounted_price - $current_price) > 0.01) {
                // Store metadata in the cart item
                $cart_items[$key]['original_price'] = $this->get_original_price($item_id, $current_price);
                $cart_items[$key]['has_discount'] = true;
                $cart_items[$key]['price'] = $discounted_price;

                $updated_items++;

                // Also update in database if user is logged in
                $user_id = get_current_user_id();
                if ($user_id) {
                    global $wpdb;
                    $cart_table = $wpdb->prefix . 'stm_lms_user_cart';

                    $wpdb->update(
                        $cart_table,
                        ['price' => $discounted_price],
                        [
                            'user_id' => $user_id,
                            'item_id' => $item_id
                        ],
                        ['%f'],
                        ['%d', '%d']
                    );

                    // Also store a user meta record of the original price
                    update_user_meta($user_id, '_cms_original_price_' . $item_id, $cart_items[$key]['original_price']);
                }
            }
        }

        // Done processing - release lock
        $processing = false;

        // Mark session as processed if we updated any items
        if ($updated_items > 0) {
            self::$discount_applied_this_session = true;
            error_log("FORCE UPDATE SESSION COMPLETE: Updated {$updated_items} items");
        }

        // Log modified cart for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Modified cart items after discount: ' . json_encode($cart_items));
        }

        // Update the cart session with discounted items
        $this->update_masterstudy_cart($cart_items);

        // CRITICAL: Store the cart items in a transient to ensure they persist across requests
        $user_id = get_current_user_id();
        $transient_key = 'custom_cms_discounted_cart_' . ($user_id ? $user_id : session_id());
        set_transient($transient_key, $cart_items, HOUR_IN_SECONDS);

        // Add JavaScript to ensure prices are updated when page loads
        add_action('wp_footer', function() {
            // Get current discount info
            $discount_info = $this->get_active_discount();
            $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
            $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

            // Build JavaScript using PHP string concatenation to avoid quote escaping issues
            $js = "<script type='text/javascript'>\n";
            $js .= "jQuery(document).ready(function($) {\n";
            $js .= "    // Function to apply discount to a price\n";
            $js .= "    function applyDiscount(price) {\n";
            $js .= "        if (isNaN(price) || price <= 0) return price;\n\n";
            $js .= "        var discountType = \"" . esc_js($admin_discount_type) . "\";\n";
            $js .= "        var discountAmount = " . floatval($admin_discount_amount) . ";\n";
            $js .= "        var discountedPrice;\n\n";
            $js .= "        if (discountType === \"percentage\") {\n";
            $js .= "            discountedPrice = price - (price * discountAmount / 100);\n";
            $js .= "        } else {\n";
            $js .= "            discountedPrice = Math.max(0, price - discountAmount);\n";
            $js .= "        }\n\n";
            $js .= "        return discountedPrice.toFixed(2);\n";
            $js .= "    }\n\n";
            $js .= "    // Function to update price elements\n";
            $js .= "    function updatePriceElement(element) {\n";
            $js .= "        if ($(element).data(\"discounted\") === \"true\") return;\n\n";
            $js .= "        var priceText = $(element).text().trim();\n";
            $js .= "        var priceMatch = priceText.match(/([^0-9]*)([0-9.,]+)([^0-9]*)/);\n\n";
            $js .= "        if (priceMatch) {\n";
            $js .= "            var prefix = priceMatch[1] || \"\";\n";
            $js .= "            var price = parseFloat(priceMatch[2].replace(/,/g, \"\"));\n";
            $js .= "            var suffix = priceMatch[3] || \"\";\n\n";
            $js .= "            if (!isNaN(price)) {\n";
            $js .= "                var discountedPrice = applyDiscount(price);\n";
            $js .= "                $(element).html(prefix + discountedPrice + suffix).data(\"discounted\", \"true\");\n";
            $js .= "                console.log(\"Updated price from \" + price + \" to \" + discountedPrice);\n";
            $js .= "            }\n";
            $js .= "        }\n";
            $js .= "    }\n\n";
            $js .= "    // Apply to all price elements on checkout page\n";
            $js .= "    setTimeout(function() {\n";
            $js .= "        // Target total price at bottom of checkout\n";
            $js .= "        $(\".masterstudy-checkout-table__footer .masterstudy-checkout-course-info__price\").each(function() {\n";
            $js .= "            updatePriceElement(this);\n";
            $js .= "        });\n\n";
            $js .= "        // Target individual course prices\n";
            $js .= "        $(\".masterstudy-checkout-course-info__price\").each(function() {\n";
            $js .= "            updatePriceElement(this);\n";
            $js .= "        });\n\n";
            $js .= "        // Target the total on the payment button\n";
            $js .= "        $(\".stm_lms_pay_button, .pay-button\").find(\"span\").each(function() {\n";
            $js .= "            var btnText = $(this).text();\n";
            $js .= "            if (btnText.match(/pay\\s+[^0-9]*([0-9.,]+)/i)) {\n";
            $js .= "                updatePriceElement(this);\n";
            $js .= "            }\n";
            $js .= "        });\n\n";
            $js .= "        // Target other price elements that might be on the page\n";
            $js .= "        $(\".masterstudy-course-price, .stm_lms_courses__single_price, .course-price-number\").each(function() {\n";
            $js .= "            updatePriceElement(this);\n";
            $js .= "        });\n\n";
            $js .= "        // Add a discount notification if not already present\n";
            $js .= "        if ($(\".discount-notification\").length === 0) {\n";
            $js .= "            var discountFormatted = \"" . esc_js($discount_info['formatted']) . "\";\n";
            $js .= "            var discountCode = \"" . esc_js($discount_info['code']) . "\";\n\n";
            $js .= "            var notification = '<div class=\"discount-notification\" style=\"margin: 15px 0; padding: 10px; background-color: #f0f8ff; border-left: 4px solid #4986e7; border-radius: 3px;\"><strong>Discount Applied:</strong> ' + discountFormatted + ' off with code <code>' + discountCode + '</code></div>';\n\n";
            $js .= "            $(\".masterstudy-checkout-table__footer\").before(notification);\n";
            $js .= "        }\n";
            $js .= "    }, 500); // Small delay to ensure all elements are loaded\n";
            $js .= "});\n";
            $js .= "</script>";

            echo $js;
        });

        // Log modified cart for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Modified cart items after discount: ' . json_encode($cart_items));
        }

        // Update the cart session with discounted items
        $this->update_masterstudy_cart($cart_items);

        // CRITICAL: Store the cart items in a transient to ensure they persist across requests
        $user_id = get_current_user_id();
        $transient_key = 'custom_cms_discounted_cart_' . ($user_id ? $user_id : session_id());
        set_transient($transient_key, $cart_items, HOUR_IN_SECONDS);
    }

    /**
     * Intercept cart items being fetched from the database and apply discount
     * This is a critical hook that modifies the actual data used for payment processing
     *
     * @param array $cart_items The cart items from database
     * @param int $user_id The user ID
     * @return array Modified cart items with discount applied
     */
    public function intercept_cart_items($cart_items, $user_id) {
        // If no active discount, return original cart items
        if (!$this->active_discount || empty($cart_items)) {
            return $cart_items;
        }

        // Prevent multiple executions in the same request
        if (self::$discount_applied_this_session) {
            error_log("SKIP: Discount already applied in this session for intercept");
            return $cart_items;
        }

        error_log('*** INTERCEPTING CART ITEMS FROM DATABASE ***');

        $updated_items = 0;
        // Loop through each cart item and apply the discount
        foreach ($cart_items as $key => $item) {
            // Skip if no price or item_id
            if (!isset($item['price']) || !isset($item['item_id'])) {
                continue;
            }

            $item_id = $item['item_id'];
            $current_price = floatval($item['price']);

            // Apply single discount with safeguards
            $discounted_price = $this->apply_single_discount($item_id, $current_price);

            // Only update if price actually changed
            if (abs($discounted_price - $current_price) > 0.01) {
                // Store original price for reference if needed
                $cart_items[$key]['original_price'] = $this->get_original_price($item_id, $current_price);
                $cart_items[$key]['discount_applied'] = true;
                $cart_items[$key]['price'] = $discounted_price;
                $cart_items[$key]['discount_info'] = [
                    'type' => $this->active_discount['type'],
                    'amount' => $this->active_discount['amount'],
                    'referral_code' => $this->active_discount['code'],
                    'discount_value' => $cart_items[$key]['original_price'] - $discounted_price
                ];

                $updated_items++;
            }
        }

        // Mark session as processed if we updated any items
        if ($updated_items > 0) {
            self::$discount_applied_this_session = true;
            error_log("INTERCEPT SESSION COMPLETE: Updated {$updated_items} items");
        }

        // Debug log
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Referral discount applied to intercepted cart items: ' . $updated_items . ' items updated');
        }

        return $cart_items;
    }

    /**
     * Modify order data before it's created in the database
     * This ensures the correct discounted price is stored in the order
     *
     * @param array $order_data The order data
     * @return array Modified order data
     */
    public function modify_order_before_creation($order_data) {
        // If no active discount, return original order data
        if (!$this->active_discount) {
            return $order_data;
        }

        // If we have price and cart_items, modify them to apply discount
        if (isset($order_data['price']) && is_numeric($order_data['price'])) {
            $original_price = floatval($order_data['price']);
            $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
            $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

            // Apply discount
            if ($admin_discount_type === 'percentage') {
                $discount_amount = ($original_price * $admin_discount_amount) / 100;
                $order_data['price'] = max(0, $original_price - $discount_amount);
            } else {
                $order_data['price'] = max(0, $original_price - $admin_discount_amount);
            }

            // Add discount info to order meta
            $order_data['meta_input']['discount_applied'] = true;
            $order_data['meta_input']['discount_type'] = $admin_discount_type;
            $order_data['meta_input']['discount_amount'] = $admin_discount_amount;
            $order_data['meta_input']['original_price'] = $original_price;
            $order_data['meta_input']['referral_code'] = $this->active_discount['code'];

            // Debug log
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('Referral discount applied to order before creation:');
                error_log('Original price: ' . $original_price);
                error_log('Discounted price: ' . $order_data['price']);
                error_log('Discount type: ' . $admin_discount_type);
                error_log('Discount amount: ' . $admin_discount_amount);
            }
        }

        return $order_data;
    }

    /**
     * Modify WooCommerce cart item data (if WooCommerce is used for payments)
     *
     * @param array $cart_item_data Cart item data
     * @param int $product_id Product ID
     * @param int $variation_id Variation ID
     * @return array Modified cart item data
     */
    public function modify_woocommerce_price($cart_item_data, $product_id, $variation_id) {
        // If no active discount, return original data
        if (!$this->active_discount) {
            return $cart_item_data;
        }

        // Add discount info to cart item data so we can modify the price later
        $cart_item_data['referral_discount'] = [
            'active' => true,
            'code' => $this->active_discount['code'],
            'type' => $this->active_discount['type'],
            'amount' => $this->active_discount['amount']
        ];

        return $cart_item_data;
    }

    /**
     * Apply discount to WooCommerce product price
     *
     * @param float $price Product price
     * @param object $product WC_Product object
     * @return float Modified price
     */
    public function apply_discount_to_wc_price($price, $product) {
        // If no active discount, return original price
        if (!$this->active_discount || !is_numeric($price)) {
            return $price;
        }

        global $woocommerce;
        if (!$woocommerce || !isset($woocommerce->cart)) {
            return $price;
        }

        // Check if this product is in the cart with our discount
        foreach ($woocommerce->cart->get_cart() as $cart_item) {
            if ($cart_item['product_id'] === $product->get_id() && isset($cart_item['referral_discount'])) {
                $discount_data = $cart_item['referral_discount'];

                // Apply discount based on type
                if ($discount_data['type'] === 'percentage') {
                    $discount_amount = ($price * $discount_data['amount']) / 100;
                    return max(0, $price - $discount_amount);
                } else {
                    return max(0, $price - $discount_data['amount']);
                }
            }
        }

        return $price;
    }

    /**
     * Directly modify cart totals before final calculation
     * This ensures the discount is applied to the actual payment amount
     *
     * @param array $totals The cart totals array
     * @return array Modified cart totals
     */
    public function modify_cart_totals($totals) {
        if (!$this->has_active_discount()) {
            return $totals;
        }

        // Get discount data
        $discount_data = $this->get_active_discount();
        $admin_discount_type = $discount_data['type'];
        $admin_discount_amount = $discount_data['amount'];

        // Log original totals for debugging
        error_log('Original cart total: ' . print_r($totals, true));

        // Apply discount to the total
        if (is_array($totals) && isset($totals['total'])) {
            $original_total = $totals['total'];

            if ($admin_discount_type === 'percentage') {
                $discount_value = ($original_total * $admin_discount_amount) / 100;
                $totals['total'] = max(0, $original_total - $discount_value);
            } else {
                // Fixed amount
                $totals['total'] = max(0, $original_total - $admin_discount_amount);
            }

            // Log the modified total
            error_log('Modified cart total after discount: ' . $totals['total']);
        }

        return $totals;
    }

    /**
     * Directly modify the final payment amount before processing
     * This is the last chance to apply the discount
     *
     * @param float $amount The payment amount
     * @return float Modified payment amount
     */
    public function modify_payment_amount($amount) {
        // If no discount active, return original amount
        if (!$this->active_discount || !is_numeric($amount)) {
            return $amount;
        }

        // CRITICAL: Check the flag to see if discount was already applied at cart level
        // If so, we don't need to apply it again
        if (get_option('_custom_cms_discount_already_applied', 0)) {
            return $amount; // Return the amount as is - already discounted
        }

        // Get the discounted price
        $discounted_amount = $this->calculate_discounted_price($amount, true);

        // Debug info
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log(sprintf('Modifying payment amount: %s -> %s', $amount, $discounted_amount));
        }

        return $discounted_amount;
    }

    /**
     * Modify payment totals during AJAX checkout process
     * This is the critical hook for actual payment processing
     *
     * @param array $totals Cart totals array with 'total' and 'item_name' keys
     * @return array Modified cart totals
     */
    public function modify_payment_totals($totals) {
        // If no discount active or invalid totals, return original totals
        if (!$this->active_discount || !is_array($totals) || !isset($totals['total'])) {
            return $totals;
        }

        // CRITICAL: Even if discount was already applied at cart level,
        // we MUST apply it here too since this is the final payment amount
        // If we don't apply it here, the customer will be charged the full price!

        // Get original total
        $original_total = floatval($totals['total']);

        // Check if we have a cached discounted total for this amount
        if (isset(self::$price_cache['total']) && abs(self::$price_cache['total'][0] - $original_total) < 0.01) {
            // Use the cached discount
            $discounted_total = self::$price_cache['total'][1];
            error_log("PAYMENT: Using cached discounted total: {$original_total} -> {$discounted_total}");
        } else {
            // Calculate a fresh discount
            // Get discount settings
            $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
            $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

            // Calculate discount directly to avoid any issues
            $discounted_total = $original_total;
            if ($admin_discount_type === 'percentage') {
                $discount_value = ($original_total * $admin_discount_amount) / 100;
                $discounted_total = $original_total - $discount_value;
                error_log("PAYMENT: Applied {$admin_discount_amount}% discount: {$original_total} - {$discount_value} = {$discounted_total}");
            } else {
                $discount_value = min($original_total, $admin_discount_amount);
                $discounted_total = max(0, $original_total - $discount_value);
                error_log("PAYMENT: Applied fixed discount: {$original_total} - {$discount_value} = {$discounted_total}");
            }

            // Round to 2 decimal places
            $discounted_total = round($discounted_total, 2);

            // Cache the discount
            self::$price_cache['total'] = [$original_total, $discounted_total];
        }

        // CRITICAL: Force an exact 20% discount if other calculations fail
        // This is our last-resort safety measure
        if ($discounted_total >= $original_total) {
            $discounted_total = $original_total * 0.8; // 20% discount
            error_log("PAYMENT: Failsafe - forcing 20% discount: {$original_total} -> {$discounted_total}");
        }

        // Update the total in the array
        $totals['total'] = $discounted_total;

        // Set the flag to indicate discount has been applied
        update_option('_custom_cms_discount_already_applied', 1);

        // Store this in a transient to ensure consistency
        $user_id = get_current_user_id();
        $transient_key = 'stm_lms_discounted_total_' . ($user_id ? $user_id : session_id());
        set_transient($transient_key, $discounted_total, HOUR_IN_SECONDS);

        // Also force it into WordPress options for maximum persistence
        update_option('_custom_cms_last_payment_amount', $discounted_total);

        // Debug info - CRITICAL FOR DEBUGGING
        error_log("PAYMENT TOTALS MODIFIED: Original={$original_total}, Discounted={$discounted_total}");
        error_log("PAYMENT DATA: " . json_encode($totals));

        return $totals;
    }

    /**
     * Clear ALL discount data after an order is completed
     * This comprehensive method ensures no discount data persists across purchases
     *
     * @param mixed $user_id User ID (from stm_lms_order_accepted hook)
     * @param mixed $cart_items Cart items (from stm_lms_order_accepted hook)
     */
    public function clear_all_discount_data($user_id = null, $cart_items = null) {
        // Log to both error_log and our custom debug file
        $this->log_discount_cleanup('*** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***');
        $this->log_discount_cleanup('Cleanup triggered with user_id: ' . ($user_id ?: 'NULL') . ', cart_items: ' . (is_array($cart_items) ? count($cart_items) . ' items' : 'NULL'));

        // Log current discount state before cleanup
        $had_active_discount = !empty($this->active_discount);
        $discount_flag_before = get_option('_custom_cms_discount_already_applied', 0);
        $this->log_discount_cleanup('Before cleanup - Active discount: ' . ($had_active_discount ? 'YES' : 'NO') . ', Flag: ' . $discount_flag_before);

        // Clear the processing flag
        delete_option('_custom_cms_discount_already_applied');
        $this->log_discount_cleanup('Cleared processing flag');

        // Clear active discount from memory
        if ($this->active_discount) {
            $discount_code = $this->active_discount['code'] ?? 'UNKNOWN';
            $discount_amount = $this->active_discount['formatted'] ?? 'UNKNOWN';
            $this->log_discount_cleanup("Clearing active discount - Code: {$discount_code}, Amount: {$discount_amount}");
            $this->active_discount = null;
        }

        // Clear static caches
        $price_cache_count = count(self::$price_cache);
        $discounted_items_count = count(self::$discounted_items);
        self::$price_cache = [];
        self::$discounted_items = [];
        self::$discount_applied_this_session = false;
        $this->log_discount_cleanup("Cleared static caches - Price cache: {$price_cache_count} items, Discounted items: {$discounted_items_count} items");

        // Clear cookies
        $this->clear_discount_cookies();

        // Clear transients and user meta
        $this->clear_user_discount_data($user_id);

        // Clear original price user meta data
        $this->clear_original_price_meta($user_id, $cart_items);

        // Add JavaScript to clear localStorage
        $this->add_localstorage_cleanup_script();

        $this->log_discount_cleanup('*** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***');
        $this->log_discount_cleanup('User should now need to re-enter referral code for future purchases');
    }

    /**
     * Log discount cleanup activities to both error_log and debug file
     */
    private function log_discount_cleanup($message) {
        $timestamp = date('Y-m-d H:i:s');
        $formatted_message = "[{$timestamp}] DISCOUNT CLEANUP: {$message}";

        // Log to WordPress error log
        error_log('CMS Referral: ' . $message);

        // Also log to our custom debug file
        $debug_file = plugin_dir_path(__FILE__) . '../debug.log';
        if (file_exists($debug_file) && is_writable($debug_file)) {
            file_put_contents($debug_file, $formatted_message . "\n", FILE_APPEND | LOCK_EX);
        }
    }

    /**
     * Handle order completion cleanup for transition_post_status hook
     * This ensures cleanup happens when order status changes to completed
     */
    public function handle_order_completion_cleanup($new_status, $old_status, $post) {
        // Only process MasterStudy LMS orders
        if ($post->post_type !== 'stm-orders') {
            return;
        }

        // Only cleanup when order is completed/published
        if ($new_status !== 'publish') {
            return;
        }

        $this->log_discount_cleanup("Order {$post->ID} completed, triggering discount cleanup");

        // Get user ID from post meta or author
        $user_id = get_post_meta($post->ID, 'user_id', true);
        if (!$user_id) {
            $user_id = $post->post_author;
        }

        $this->log_discount_cleanup("Order {$post->ID} - User ID: {$user_id}");

        // Trigger comprehensive cleanup
        $this->clear_all_discount_data($user_id);
    }

    /**
     * Clear all discount-related cookies
     */
    private function clear_discount_cookies() {
        $cookies_cleared = [];

        // Clear the main discount cookie
        $result1 = setcookie(
            self::DISCOUNT_COOKIE,
            '',
            time() - 3600,
            COOKIEPATH,
            COOKIE_DOMAIN,
            is_ssl(),
            true
        );
        if ($result1) $cookies_cleared[] = self::DISCOUNT_COOKIE;

        // Clear the referral code cookie
        $result2 = setcookie(
            'custom_cms_referral_code',
            '',
            time() - 3600,
            COOKIEPATH,
            COOKIE_DOMAIN,
            is_ssl(),
            true
        );
        if ($result2) $cookies_cleared[] = 'custom_cms_referral_code';

        error_log('CMS Referral: Discount cookies cleared: ' . implode(', ', $cookies_cleared));
        if (empty($cookies_cleared)) {
            error_log('CMS Referral: WARNING - No cookies were successfully cleared (headers may have been sent)');
        }
    }

    /**
     * Clear user-specific discount data (transients and user meta)
     */
    private function clear_user_discount_data($user_id = null) {
        // If no user ID provided, try to get current user
        if (!$user_id && function_exists('is_user_logged_in') && is_user_logged_in()) {
            $user_id = get_current_user_id();
        }

        if ($user_id) {
            // Clear discount transient
            $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
            delete_transient($transient_key);

            // Clear cart total transients
            delete_transient('stm_lms_cart_total_' . $user_id);
            delete_transient('stm_lms_discounted_total_' . $user_id);

            // Clear user meta cart data
            delete_user_meta($user_id, 'stm_lms_cart_items');
            delete_user_meta($user_id, 'stm_lms_cart_total');

            error_log("CMS Referral: User discount data cleared for user {$user_id}");
        }
    }

    /**
     * Clear original price user meta data for cart items
     */
    private function clear_original_price_meta($user_id = null, $cart_items = null) {
        // If no user ID provided, try to get current user
        if (!$user_id && function_exists('is_user_logged_in') && is_user_logged_in()) {
            $user_id = get_current_user_id();
        }

        if (!$user_id) {
            return;
        }

        // If cart items provided, clear specific item meta
        if (!empty($cart_items) && is_array($cart_items)) {
            foreach ($cart_items as $item) {
                $item_id = isset($item['item_id']) ? $item['item_id'] : (isset($item['id']) ? $item['id'] : null);
                if ($item_id) {
                    delete_user_meta($user_id, '_cms_original_price_' . $item_id);
                }
            }
        } else {
            // Clear all original price meta for this user (fallback approach)
            global $wpdb;
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->usermeta} WHERE user_id = %d AND meta_key LIKE %s",
                $user_id,
                '_cms_original_price_%'
            ));
        }

        error_log("CMS Referral: Original price meta cleared for user {$user_id}");
    }

    /**
     * Add JavaScript to clear localStorage data
     */
    private function add_localstorage_cleanup_script() {
        add_action('wp_footer', function() {
            ?>
            <script type="text/javascript">
            (function() {
                // Clear localStorage discount data
                if (typeof localStorage !== 'undefined') {
                    localStorage.removeItem('custom_cms_referral_code');
                    localStorage.removeItem('custom_cms_referral_timestamp');
                    localStorage.removeItem('custom_cms_referral_discount');
                    console.log('CMS Referral: localStorage discount data cleared');
                }

                // Clear any discount-related cookies via JavaScript as backup
                document.cookie = 'custom_cms_referral_code=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                document.cookie = 'custom_cms_referral_discount=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                console.log('CMS Referral: Discount cleanup complete');
            })();
            </script>
            <?php
        }, 1); // High priority to run early
    }

    /**
     * Get MasterStudy LMS cart items from session
     *
     * @return array Cart items or empty array if none
     */
    private function get_masterstudy_cart_items() {
        // Check if MasterStudy LMS is using cookies or sessions
        if (isset($_COOKIE['stm_lms_cart'])) {
            $cart_data = stripslashes($_COOKIE['stm_lms_cart']);
            $decoded = json_decode($cart_data, true);
            return is_array($decoded) ? $decoded : array();
        }

        // Try getting from session - stm_lms_get_cart_items requires user_id parameter
        if (function_exists('stm_lms_get_cart_items')) {
            $user_id = is_user_logged_in() ? get_current_user_id() : 0;
            $cart_items = stm_lms_get_cart_items($user_id);
            return is_array($cart_items) ? $cart_items : array();
        }

        return array();
    }

    /**
     * Update MasterStudy LMS cart with new items
     *
     * @param array $cart_items Updated cart items
     */
    private function update_masterstudy_cart($cart_items) {
        if (empty($cart_items)) {
            return;
        }

        // Log before update
        error_log('UPDATING CART WITH DISCOUNTED ITEMS: ' . json_encode($cart_items));

        // Encode cart items to JSON for storage in cookie
        $encoded_items = json_encode($cart_items);

        // CRITICAL: Set the cookie with proper parameters to ensure it's saved
        // Include secure and httponly flags for better security
        $secure = is_ssl();
        $httponly = true;

        // Use stronger cookie setting to ensure it persists
        if (!headers_sent()) {
            // Primary cart cookie
            setcookie('stm_lms_cart', $encoded_items, time() + DAY_IN_SECONDS, COOKIEPATH, COOKIE_DOMAIN, $secure, $httponly);

            // Backup cookie with longer expiration
            setcookie('stm_lms_cart_backup', $encoded_items, time() + WEEK_IN_SECONDS, COOKIEPATH, COOKIE_DOMAIN, $secure, $httponly);

            error_log('Cart cookies have been set');
        } else {
            error_log('WARNING: Headers already sent, could not set cookie');

            // Try to use JavaScript as fallback for setting cookie
            add_action('wp_footer', function() use ($encoded_items) {
                echo "<script>document.cookie = 'stm_lms_cart=" . esc_js($encoded_items) . "; path=/; max-age=" . DAY_IN_SECONDS . "';</script>";
                echo "<script>console.log('Setting cart cookie via JavaScript fallback');</script>";
            });
        }

        // If user is logged in, update all possible storage locations
        if (is_user_logged_in()) {
            $user_id = get_current_user_id();

            // Store cart items in user meta (WordPress standard approach)
            update_user_meta($user_id, 'stm_lms_cart_items', $cart_items);
            error_log("Updated user meta cart for user {$user_id}");

            // CRITICAL: Use MasterStudy LMS API functions if available
            if (function_exists('stm_lms_update_user_cart')) {
                stm_lms_update_user_cart($user_id, $cart_items);
                error_log("Called stm_lms_update_user_cart for user {$user_id}");
            }

            // Ensure cart total is also updated for this user
            $cart_total = 0;
            foreach ($cart_items as $item) {
                if (isset($item['price'])) {
                    $cart_total += floatval($item['price']);
                }
            }

            // Save the cart total in a transient
            $transient_key = 'stm_lms_cart_total_' . $user_id;
            set_transient($transient_key, $cart_total, HOUR_IN_SECONDS);
            error_log("Set cart total transient for user {$user_id}: {$cart_total}");

            // Also save in user meta as backup
            update_user_meta($user_id, 'stm_lms_cart_total', $cart_total);
        }
    }

    /**
     * Calculate discounted price
     * FIXED: Removed flag check that was preventing discount calculation during payment
     *
     * @param float $original_price Original price
     * @param bool $force_apply Force apply the discount even if it appears already discounted
     * @return float Discounted price
     */
    public function calculate_discounted_price($original_price, $force_apply = false) {
        // If no discount active or invalid price, return original price
        if (!$this->active_discount || !is_numeric($original_price)) {
            return $original_price;
        }

        // Always use the latest discount amount from admin settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        $discounted_price = $original_price;

        // Apply discount based on type
        if ($admin_discount_type === 'percentage') {
            // Calculate percentage discount
            $discount_value = ($original_price * $admin_discount_amount) / 100;
            $discounted_price = $original_price - $discount_value;
        } else {
            // Apply fixed amount discount
            $discounted_price = $original_price - $admin_discount_amount;
        }

        // Ensure price doesn't go below zero
        return max(0, round($discounted_price, 2));
    }

    /**
     * Add discount badge to price HTML
     *
     * @param string $price_html Original price HTML
     * @param float $price Original price value
     * @return string Modified price HTML with discount badge
     */
    public function add_discount_badge_to_price($price_html, $price) {
        // Skip if no active discount
        if (!$this->active_discount || !is_numeric($price)) {
            return $price_html;
        }

        $discount_info = $this->get_active_discount();

        // Add discount badge to price HTML
        $badge = '<span class="cms-discount-badge">' . esc_html($discount_info['formatted']) . ' OFF</span>';

        return $price_html . $badge;
    }

    /**
     * Get the active discount data
     *
     * @return array|null Discount data or null if no discount
     */
    public function get_active_discount() {
        if (!$this->active_discount) {
            return null;
        }

        // Always use the latest discount amount from admin settings
        $discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
        $discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));

        // Format the discount for display
        if ($discount_type === 'percentage') {
            $formatted_discount = $discount_amount . '%';
        } else {
            $currency_symbol = function_exists('get_woocommerce_currency_symbol') ?
                get_woocommerce_currency_symbol() : '₹';
            $formatted_discount = $currency_symbol . number_format($discount_amount, 2);
        }

        return array(
            'type' => $discount_type,
            'amount' => $discount_amount,
            'formatted' => $formatted_discount,
            'code' => isset($this->active_discount['code']) ? $this->active_discount['code'] : null,
            'referrer_id' => isset($this->active_discount['referrer_id']) ? $this->active_discount['referrer_id'] : null
        );
    }

    /**
     * Set an active discount
     *
     * @param string $code Referral code
     * @param int $referrer_id Referrer user ID
     * @param string $discount_type Type of discount (percentage or fixed)
     * @param float $discount_amount Amount of discount
     * @return bool Success or failure
     */
    public function set_active_discount($code, $referrer_id, $discount_type, $discount_amount) {
        // Create discount data array
        $discount_data = array(
            'code' => $code,
            'referrer_id' => $referrer_id,
            'type' => $discount_type,
            'amount' => $discount_amount,
            'created' => time()
        );

        // Format the discount for display
        if ($discount_type === 'percentage') {
            $discount_data['formatted'] = $discount_amount . '%';
        } else {
            $currency_symbol = function_exists('get_woocommerce_currency_symbol') ?
                get_woocommerce_currency_symbol() : '₹';
            $discount_data['formatted'] = $currency_symbol . number_format($discount_amount, 2);
        }

        // Set active discount in memory
        $this->active_discount = $discount_data;

        // Store in cookie for all users
        $this->set_discount_cookie($discount_data);

        // Store in transient for logged-in users if WordPress is fully loaded
        if (function_exists('is_user_logged_in') && is_user_logged_in()) {
            $user_id = get_current_user_id();
            $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
            // Get cookie duration from admin settings (fallback to 7 days for backward compatibility)
            $cookie_duration_days = get_option('custom_cms_referral_cookie_duration', 7);
            set_transient($transient_key, $discount_data, $cookie_duration_days * DAY_IN_SECONDS);
        } else {
            // Add a hook to save this data after WordPress is fully loaded
            add_action('init', function() use ($discount_data) {
                if (function_exists('is_user_logged_in') && is_user_logged_in()) {
                    $user_id = get_current_user_id();
                    $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
                    // Get cookie duration from admin settings (fallback to 7 days for backward compatibility)
                    $cookie_duration_days = get_option('custom_cms_referral_cookie_duration', 7);
                    set_transient($transient_key, $discount_data, $cookie_duration_days * DAY_IN_SECONDS);
                }
            });
        }

        return true;
    }

    /**
     * Clear the active discount
     */
    public function clear_active_discount() {
        // Clear active discount from memory
        $this->active_discount = null;

        // Clear cookie
        setcookie(
            self::DISCOUNT_COOKIE,
            '',
            time() - 3600,
            COOKIEPATH,
            COOKIE_DOMAIN,
            is_ssl(),
            true
        );

        // Clear transient for logged-in users if WordPress is fully loaded
        if (function_exists('is_user_logged_in') && is_user_logged_in()) {
            $user_id = get_current_user_id();
            $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
            delete_transient($transient_key);
        } else {
            // Schedule cleanup on init if we're too early in the loading process
            add_action('init', function() {
                if (function_exists('is_user_logged_in') && is_user_logged_in()) {
                    $user_id = get_current_user_id();
                    $transient_key = self::DISCOUNT_TRANSIENT_PREFIX . $user_id;
                    delete_transient($transient_key);
                }
            });
        }
    }

    /**
     * Apply discount to checkout total
     * This is a critical method that handles the final total price calculation.
     * We use a special cache key 'total' to track if we've already processed the total.
     *
     * @param float $total Current checkout total
     * @return float Discounted checkout total
     */
    public function apply_discount_to_checkout_total($total) {
        // If no discount active or invalid price, return original total
        if (!$this->active_discount || !is_numeric($total)) {
            return $total;
        }

        // CRITICAL: Check if we have already calculated a discount for this total
        // We use a special cache key 'total' for the checkout total discount
        if (isset(self::$price_cache['total'])) {
            list($original_total, $discounted_total) = self::$price_cache['total'];

            // If the totals are almost identical (within a small margin), use the cached discount
            if (abs($total - $original_total) < 0.01) {
                error_log("CACHE HIT: Using cached checkout total: Original={$original_total}, Discounted={$discounted_total}");
                return $discounted_total;
            }

            // If the total has changed significantly, we'll recalculate
            error_log("CACHE MISS: Total changed from {$original_total} to {$total}, recalculating");
        }

        // Get current discount settings
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Calculate the discount
        $discounted_price = $total;

        if ($admin_discount_type === 'percentage') {
            // Calculate percentage discount
            $discount_value = ($total * $admin_discount_amount) / 100;
            $discounted_price = $total - $discount_value;
            error_log("CHECKOUT: Applied {$admin_discount_amount}% discount to total: {$total} - {$discount_value} = {$discounted_price}");
        } else {
            // Apply fixed amount discount
            $discount_value = min($total, $admin_discount_amount); // Don't discount more than the total
            $discounted_price = $total - $discount_value;
            error_log("CHECKOUT: Applied fixed discount of {$discount_value} to total: {$total} - {$discount_value} = {$discounted_price}");
        }

        // Round to 2 decimal places
        $discounted_price = round($discounted_price, 2);

        // Sanity check - ensure the discount actually reduced the price
        if ($discounted_price >= $total) {
            error_log("ERROR: Checkout discount calculation failed! {$discounted_price} is not less than {$total}");
            $discounted_price = $total * 0.8; // Apply a 20% discount as failsafe
            error_log("CHECKOUT: Using failsafe 20% discount: {$discounted_price}");
        }

        // Store in cache for future reference
        self::$price_cache['total'] = [$total, $discounted_price];

        // Force MasterStudy to recognize the discount by setting a transient
        $transient_key = 'stm_lms_cart_total_' . (is_user_logged_in() ? get_current_user_id() : session_id());
        set_transient($transient_key, $discounted_price, HOUR_IN_SECONDS);

        // Ensure price doesn't go below zero
        return max(0, $discounted_price);
    }

    /**
     * Update checkout total display
     * This is called directly in the checkout template to modify the displayed total
     *
     * @param float $total Current checkout total
     */
    public function update_checkout_total($total) {
        // Only proceed if we have an active discount
        if (!$this->active_discount) {
            return;
        }

        // Get the current discount information
        $discount_info = $this->get_active_discount();
        $admin_discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));
        $admin_discount_type = get_option('custom_cms_referral_discount_type', 'percentage');

        // Calculate discount
        $original_total = $total;
        $discounted_total = $this->apply_discount_to_checkout_total($total);
        $discount_value = $original_total - $discounted_total;

        // Output discount information
        echo '<div class="masterstudy-checkout-discount-info">';
        echo '<div class="discount-label">' . esc_html__('Referral Discount:', 'custom-cms-referal') . '</div>';
        echo '<div class="discount-value">-' . STM_LMS_Helpers::display_price($discount_value) . '</div>';
        echo '</div>';

        // Add a script to update the total display
        echo '<script type="text/javascript">';
        echo 'jQuery(document).ready(function($) {';
        echo '  // Update the displayed total';
        echo '  setTimeout(function() {';
        echo '    $(".masterstudy-checkout-course-info__price:contains(\"Total:\") + .masterstudy-checkout-course-info__price").html("' . STM_LMS_Helpers::display_price($discounted_total) . '");';
        echo '    // Update pay button text if present';
        echo '    $(".stm_lms_pay_button span").each(function() {';
        echo '      var text = $(this).text();';
        echo '      if (text.includes("Pay")) {';
        echo '        $(this).html("Pay ' . STM_LMS_Helpers::display_price($discounted_total) . '");';
        echo '      }';
        echo '    });';
        echo '  }, 500);';
        echo '});';
        echo '</script>';
    }

}
