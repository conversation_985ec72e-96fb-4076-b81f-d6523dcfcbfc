<?php
/**
 * DEEP Investigation script with debug log monitoring
 * 
 * This script will:
 * 1. Show current state
 * 2. Monitor debug log for hook activity
 * 3. Check if hooks are actually firing
 * 4. Detect any changes after manual update
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== DEEP INVESTIGATION WITH DEBUG MONITORING ===\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n\n";

global $wpdb;

// Function to get debug log content
function get_debug_log_tail($lines = 20) {
    $debug_file = 'debug.log';
    if (!file_exists($debug_file)) {
        return "Debug log file not found.";
    }
    
    $content = file_get_contents($debug_file);
    if (empty($content)) {
        return "Debug log is empty.";
    }
    
    $log_lines = explode("\n", trim($content));
    $tail_lines = array_slice($log_lines, -$lines);
    return implode("\n", $tail_lines);
}

// Function to capture current state
function capture_current_state() {
    global $wpdb;
    
    $orders = $wpdb->get_results("
        SELECT p.ID, p.post_status, p.post_author, p.post_date
        FROM {$wpdb->posts} p 
        WHERE p.post_type = 'stm-orders' 
        ORDER BY p.post_date DESC 
        LIMIT 5
    ");
    
    $state = array();
    foreach ($orders as $order) {
        $masterstudy_status = $wpdb->get_var($wpdb->prepare("
            SELECT meta_value FROM {$wpdb->postmeta} 
            WHERE post_id = %d AND meta_key = 'status'
        ", $order->ID));
        
        $referrals = $wpdb->get_results($wpdb->prepare("
            SELECT id, status, referrer_id, referred_id, updated_at 
            FROM wp_mst_referrals 
            WHERE order_id = %d
        ", $order->ID));
        
        $state[$order->ID] = array(
            'wp_status' => $order->post_status,
            'ms_status' => $masterstudy_status ?: 'EMPTY',
            'referrals' => $referrals
        );
    }
    
    return $state;
}

echo "STEP 1: Capturing initial state...\n";
$state_before = capture_current_state();

echo "Current orders and referrals:\n";
foreach ($state_before as $order_id => $data) {
    echo "📦 Order {$order_id}:\n";
    echo "   WordPress Status: {$data['wp_status']}\n";
    echo "   MasterStudy Status: {$data['ms_status']}\n";
    echo "   Referrals: " . count($data['referrals']) . "\n";
    foreach ($data['referrals'] as $ref) {
        echo "     └─ Referral {$ref->id}: {$ref->status} (Updated: {$ref->updated_at})\n";
    }
    echo "\n";
}

echo "STEP 2: Checking current debug log...\n";
$debug_before = get_debug_log_tail(10);
echo "Last 10 lines of debug log:\n";
echo str_repeat("-", 40) . "\n";
echo $debug_before . "\n";
echo str_repeat("-", 40) . "\n\n";

echo str_repeat("=", 70) . "\n";
echo "STEP 3: NOW MAKE YOUR MANUAL CHANGE\n";
echo "Instructions:\n";
echo "1. Go to WordPress Admin\n";
echo "2. Navigate to MasterStudy LMS → Orders\n";
echo "3. Pick Order 49688 (or any order from the list above)\n";
echo "4. Change its status from 'pending' to 'completed'\n";
echo "5. Click 'Save' or 'Update'\n";
echo "6. Come back here and press ENTER\n";
echo "\n";
echo "🎯 We're specifically looking for:\n";
echo "- Hook activity in debug log\n";
echo "- Changes to referral status\n";
echo "- Whether multiple referrals get updated\n";
echo str_repeat("=", 70) . "\n";
echo "Waiting for your input... ";

// Wait for user input
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

echo "\nSTEP 4: Checking what changed...\n";

// Check debug log for new activity
$debug_after = get_debug_log_tail(20);
echo "Debug log after your change:\n";
echo str_repeat("-", 40) . "\n";
echo $debug_after . "\n";
echo str_repeat("-", 40) . "\n\n";

// Check state changes
$state_after = capture_current_state();

echo "STEP 5: Comparing states...\n";
$changes_found = false;

foreach ($state_after as $order_id => $after_data) {
    if (!isset($state_before[$order_id])) continue;
    
    $before_data = $state_before[$order_id];
    
    // Check MasterStudy status change
    if ($before_data['ms_status'] !== $after_data['ms_status']) {
        echo "🔄 Order {$order_id}: MasterStudy status changed from '{$before_data['ms_status']}' to '{$after_data['ms_status']}'\n";
        $changes_found = true;
    }
    
    // Check referral changes
    $before_refs = array();
    foreach ($before_data['referrals'] as $ref) {
        $before_refs[$ref->id] = array('status' => $ref->status, 'updated' => $ref->updated_at);
    }
    
    foreach ($after_data['referrals'] as $ref) {
        if (isset($before_refs[$ref->id])) {
            $before_ref = $before_refs[$ref->id];
            if ($before_ref['status'] !== $ref->status || $before_ref['updated'] !== $ref->updated_at) {
                echo "🚨 Order {$order_id}: Referral {$ref->id} changed from '{$before_ref['status']}' to '{$ref->status}' (Updated: {$ref->updated_at})\n";
                $changes_found = true;
            }
        }
    }
}

echo "\n=== FINAL ANALYSIS ===\n";
if ($changes_found) {
    echo "✅ CHANGES DETECTED! The hooks are working.\n";
    echo "Now check if multiple referrals changed when you only changed one order.\n";
} else {
    echo "❌ NO CHANGES DETECTED.\n";
    echo "Possible reasons:\n";
    echo "1. The admin interface doesn't trigger our hooks\n";
    echo "2. The hooks are not registered properly\n";
    echo "3. The order status wasn't actually saved\n";
    echo "4. Our hooks only fire on frontend, not admin\n";
}

echo "\n🔍 Check the debug log above for hook activity.\n";
echo "Look for lines containing 'ORDER STATUS CHANGE' or 'MASTERSTUDY'.\n";
?>
