<?php
/**
 * Links all include files to the main plugin
 *
 * This file serves as a central point to include all functionality files
 * in the includes directory.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define includes directory constant
define( 'CUSTOM_CMS_REFERAL_PLUGIN_INCLUDES_DIR', plugin_dir_path( __FILE__ ) );
define( 'CUSTOM_CMS_REFERAL_PLUGIN_INCLUDES_URL', plugin_dir_url( __FILE__ ) );

// Include classes
require_once CUSTOM_CMS_REFERAL_PLUGIN_INCLUDES_DIR . 'class-referral-codes.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_INCLUDES_DIR . 'class-discount-handler.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_INCLUDES_DIR . 'class-email-templates.php';

/**
 * Initialize the includes functionality
 *
 * Note: Global variables are now initialized in class-core.php to avoid conflicts.
 * This function is kept for backward compatibility but no longer initializes globals.
 */
function custom_cms_referral_init_includes() {
    // Global variables are now handled by class-core.php
    // This function is kept for any future includes-specific initialization
    error_log('CMS Referral: Includes initialization called (globals handled by core)');
}

// Hook into WordPress - keeping for backward compatibility
add_action( 'plugins_loaded', 'custom_cms_referral_init_includes', 15 ); // Priority 15 to run after frontend and admin
