<?php
/**
 * ENHANCED Investigation script to monitor manual order status changes
 * 
 * This script will:
 * 1. Show current state of orders and referrals
 * 2. Wait for you to make a manual change
 * 3. Show what changed after the manual update
 * 4. Identify which referrals were affected
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== ENHANCED HOOK BEHAVIOR INVESTIGATION ===\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n\n";

global $wpdb;

// Function to capture current state
function capture_current_state() {
    global $wpdb;
    
    $orders = $wpdb->get_results("
        SELECT p.ID, p.post_status, p.post_author, p.post_date
        FROM {$wpdb->posts} p 
        WHERE p.post_type = 'stm-orders' 
        ORDER BY p.post_date DESC 
        LIMIT 5
    ");
    
    $state = array();
    foreach ($orders as $order) {
        $masterstudy_status = $wpdb->get_var($wpdb->prepare("
            SELECT meta_value FROM {$wpdb->postmeta} 
            WHERE post_id = %d AND meta_key = 'status'
        ", $order->ID));
        
        $referrals = $wpdb->get_results($wpdb->prepare("
            SELECT id, status, referrer_id, referred_id 
            FROM wp_mst_referrals 
            WHERE order_id = %d
        ", $order->ID));
        
        $state[$order->ID] = array(
            'wp_status' => $order->post_status,
            'ms_status' => $masterstudy_status ?: 'EMPTY',
            'referrals' => $referrals
        );
    }
    
    return $state;
}

// Function to compare states
function compare_states($before, $after) {
    echo "\n=== CHANGES DETECTED ===\n";
    echo str_repeat("-", 50) . "\n";
    
    $changes_found = false;
    
    foreach ($after as $order_id => $after_data) {
        if (!isset($before[$order_id])) {
            echo "NEW ORDER: {$order_id}\n";
            $changes_found = true;
            continue;
        }
        
        $before_data = $before[$order_id];
        
        // Check WordPress status change
        if ($before_data['wp_status'] !== $after_data['wp_status']) {
            echo "Order {$order_id}: WordPress status changed from '{$before_data['wp_status']}' to '{$after_data['wp_status']}'\n";
            $changes_found = true;
        }
        
        // Check MasterStudy status change
        if ($before_data['ms_status'] !== $after_data['ms_status']) {
            echo "Order {$order_id}: MasterStudy status changed from '{$before_data['ms_status']}' to '{$after_data['ms_status']}'\n";
            $changes_found = true;
        }
        
        // Check referral changes
        $before_refs = array();
        foreach ($before_data['referrals'] as $ref) {
            $before_refs[$ref->id] = $ref->status;
        }
        
        $after_refs = array();
        foreach ($after_data['referrals'] as $ref) {
            $after_refs[$ref->id] = $ref->status;
        }
        
        foreach ($after_refs as $ref_id => $after_status) {
            if (isset($before_refs[$ref_id]) && $before_refs[$ref_id] !== $after_status) {
                echo "🚨 Order {$order_id}: Referral {$ref_id} status changed from '{$before_refs[$ref_id]}' to '{$after_status}'\n";
                $changes_found = true;
            }
        }
    }
    
    if (!$changes_found) {
        echo "✅ No changes detected.\n";
    }
    
    return $changes_found;
}

echo "STEP 1: Capturing current state...\n";
$state_before = capture_current_state();

echo "Current state captured. Here's what we have:\n";
foreach ($state_before as $order_id => $data) {
    echo "Order {$order_id}: WP={$data['wp_status']}, MS={$data['ms_status']}, Referrals=" . count($data['referrals']) . "\n";
    foreach ($data['referrals'] as $ref) {
        echo "  └─ Referral {$ref->id}: {$ref->status} (Referrer: {$ref->referrer_id}, Referred: {$ref->referred_id})\n";
    }
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "STEP 2: NOW GO TO ADMIN AND CHANGE ONE ORDER STATUS\n";
echo "Instructions:\n";
echo "1. Go to WordPress Admin → MasterStudy LMS → Orders\n";
echo "2. Pick ONE specific order (note the Order ID)\n";
echo "3. Change its status (pending → completed or vice versa)\n";
echo "4. Save the order\n";
echo "5. Come back here and press ENTER\n";
echo str_repeat("=", 70) . "\n";
echo "Waiting for your input... ";

// Wait for user input
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

echo "\nSTEP 3: Capturing state after your change...\n";
$state_after = capture_current_state();

$changes_detected = compare_states($state_before, $state_after);

echo "\n=== ANALYSIS ===\n";
echo str_repeat("-", 50) . "\n";

if ($changes_detected) {
    echo "🔍 INVESTIGATION RESULTS:\n";
    echo "- Changes were detected after your manual order status change\n";
    echo "- Look above to see which specific referrals were affected\n";
    echo "- If multiple referrals changed when you only changed one order,\n";
    echo "  this confirms the MASS UPDATE BUG!\n\n";
    
    echo "🎯 EXPECTED BEHAVIOR:\n";
    echo "- Only referrals for the specific order you changed should update\n";
    echo "- Other orders' referrals should remain unchanged\n\n";
    
    echo "🚨 BUG CONFIRMED IF:\n";
    echo "- Multiple referrals changed when you only changed one order\n";
    echo "- Referrals for different orders got the same status change\n";
} else {
    echo "✅ No changes detected.\n";
    echo "This could mean:\n";
    echo "1. The order status change didn't trigger our hooks\n";
    echo "2. The hooks are working correctly (order-specific)\n";
    echo "3. You didn't actually change any order status\n";
}

echo "\n=== NEXT STEPS ===\n";
echo "If mass updates were detected, the issue is in:\n";
echo "- sync_referral_status_by_user_and_courses() method\n";
echo "- Query uses user_id instead of order_id (too broad)\n";
echo "- Fix: Change query to be order-specific\n";
?>
