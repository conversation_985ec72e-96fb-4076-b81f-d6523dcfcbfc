<?php
/**
 * Admin Dashboard UI
 *
 * This file displays the main admin dashboard of the Custom CMS Referral Plugin.
 * It includes metrics, activity feed, and system status indicators.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check user permissions
if ( ! current_user_can( 'manage_options' ) ) {
    return;
}

// Get real data from referral tracker
global $custom_cms_referral_tracker;
$stats = array();
$recent_referrals = array();

if ( $custom_cms_referral_tracker ) {
    $stats = $custom_cms_referral_tracker->get_stats();
    $recent_data = $custom_cms_referral_tracker->get_referrals( array( 'per_page' => 5 ) );
    $recent_referrals = $recent_data['referrals'];
}

// Default values if no data
$total_referrals = isset( $stats['total_referrals'] ) ? $stats['total_referrals'] : 0;
$completed_referrals = isset( $stats['completed_referrals'] ) ? $stats['completed_referrals'] : 0;
$pending_referrals = isset( $stats['pending_referrals'] ) ? $stats['pending_referrals'] : 0;
$total_rewards = isset( $stats['total_rewards'] ) ? number_format( $stats['total_rewards'], 2 ) : '0.00';
?>

<div class="wrap custom-cms-referal-dashboard">
    <h1><?php echo esc_html( get_admin_page_title() ); ?></h1>

    <!-- Metrics Section -->
    <div class="dashboard-section metrics-section">
        <h2 class="section-title"><span class="dashicons dashicons-chart-bar"></span> <?php _e('Metrics Overview', 'custom-cms-referal'); ?></h2>

        <div class="metrics-container">
            <!-- Total Referrals -->
            <div class="metric-box">
                <div class="metric-value"><?php echo esc_html( $total_referrals ); ?></div>
                <div class="metric-label"><?php _e('Total Referrals', 'custom-cms-referal'); ?></div>
            </div>

            <!-- Conversions -->
            <div class="metric-box">
                <div class="metric-value"><?php echo esc_html( $completed_referrals ); ?></div>
                <div class="metric-label"><?php _e('Conversions', 'custom-cms-referal'); ?></div>
            </div>

            <!-- Pending -->
            <div class="metric-box">
                <div class="metric-value"><?php echo esc_html( $pending_referrals ); ?></div>
                <div class="metric-label"><?php _e('Pending', 'custom-cms-referal'); ?></div>
            </div>

            <!-- Total Rewards -->
            <div class="metric-box">
                <div class="metric-value">₹<?php echo esc_html( $total_rewards ); ?></div>
                <div class="metric-label"><?php _e('Total Rewards', 'custom-cms-referal'); ?></div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="dashboard-section activity-section">
        <h2 class="section-title"><span class="dashicons dashicons-schedule"></span> <?php _e('Recent Activity', 'custom-cms-referal'); ?></h2>

        <div class="activity-container">
            <table class="widefat activity-table">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'custom-cms-referal'); ?></th>
                        <th><?php _e('User', 'custom-cms-referal'); ?></th>
                        <th><?php _e('Action', 'custom-cms-referal'); ?></th>
                        <th><?php _e('Details', 'custom-cms-referal'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ( ! empty( $recent_referrals ) ): ?>
                        <?php foreach ( $recent_referrals as $referral ): ?>
                            <tr>
                                <td><?php echo esc_html( date( 'M j, Y', strtotime( $referral->created_at ) ) ); ?></td>
                                <td><?php echo esc_html( $referral->referrer_name ); ?></td>
                                <td>
                                    <?php if ( $referral->status === 'completed' ): ?>
                                        <span class="status-badge status-completed"><?php _e('Referral Completed', 'custom-cms-referal'); ?></span>
                                    <?php elseif ( $referral->status === 'pending' ): ?>
                                        <span class="status-badge status-pending"><?php _e('Referral Pending', 'custom-cms-referal'); ?></span>
                                    <?php else: ?>
                                        <span class="status-badge status-cancelled"><?php _e('Referral Cancelled', 'custom-cms-referal'); ?></span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ( $referral->referred_name ): ?>
                                        <?php echo esc_html( sprintf( __( 'Referred: %s', 'custom-cms-referal' ), $referral->referred_name ) ); ?>
                                        <?php if ( $referral->course_name ): ?>
                                            <br><small><?php echo esc_html( sprintf( __( 'Course: %s', 'custom-cms-referal' ), $referral->course_name ) ); ?></small>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <?php echo esc_html( sprintf( __( 'Code: %s', 'custom-cms-referal' ), $referral->referral_code ) ); ?>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr class="placeholder-row">
                            <td colspan="4"><?php _e('No recent activity found.', 'custom-cms-referal'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- System Status Section -->
    <div class="dashboard-section status-section">
        <h2 class="section-title"><span class="dashicons dashicons-info"></span> <?php _e('System Status', 'custom-cms-referal'); ?></h2>

        <div class="status-container">
            <table class="widefat status-table">
                <tbody>
                    <!-- MasterStudy LMS Integration -->
                    <tr>
                        <td class="status-label"><?php _e('MasterStudy LMS Integration', 'custom-cms-referal'); ?></td>
                        <td class="status-indicator">
                            <?php if (custom_cms_referral_is_masterstudy_active()): ?>
                                <span class="status-badge status-ok"><?php _e('Active', 'custom-cms-referal'); ?></span>
                            <?php else: ?>
                                <span class="status-badge status-error"><?php _e('Not Active', 'custom-cms-referal'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>

                    <!-- Database Tables -->
                    <tr>
                        <td class="status-label"><?php _e('Database Tables', 'custom-cms-referal'); ?></td>
                        <td class="status-indicator">
                            <?php if (custom_cms_referral_check_database_tables()): ?>
                                <span class="status-badge status-ok"><?php _e('Installed', 'custom-cms-referal'); ?></span>
                            <?php else: ?>
                                <span class="status-badge status-error"><?php _e('Not Installed', 'custom-cms-referal'); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>

                    <!-- Plugin Version -->
                    <tr>
                        <td class="status-label"><?php _e('Plugin Version', 'custom-cms-referal'); ?></td>
                        <td class="status-indicator">
                            <span class="status-badge status-ok">1.0.0</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Links Section -->
    <div class="dashboard-section links-section">
        <h2 class="section-title"><span class="dashicons dashicons-admin-links"></span> <?php _e('Quick Actions', 'custom-cms-referal'); ?></h2>

        <div class="quick-links">
            <?php
            $quick_actions = custom_cms_referral_get_quick_actions();
            foreach ($quick_actions as $action):
            ?>
                <a href="<?php echo esc_url($action['url']); ?>" class="quick-link-button" <?php echo isset($action['onclick']) ? 'onclick="' . esc_attr($action['onclick']) . '"' : ''; ?>>
                    <span class="dashicons <?php echo esc_attr($action['icon']); ?>"></span>
                    <?php echo esc_html($action['title']); ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</div>
