<?php
/**
 * Referral Stats Template
 *
 * Displays user's referral statistics.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>
<div class="custom-cms-referral-stats" data-theme="<?php echo esc_attr( $atts['theme'] ); ?>">
    <div class="referral-stats-header">
        <h3><?php _e( 'Your Referral Statistics', 'custom-cms-referal' ); ?></h3>
    </div>
    
    <div class="referral-stats-content">
        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value"><?php echo esc_html( $user_stats['total_referrals'] ); ?></div>
                <div class="stat-label"><?php _e( 'Total Referrals', 'custom-cms-referal' ); ?></div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value"><?php echo esc_html( $user_stats['successful_referrals'] ); ?></div>
                <div class="stat-label"><?php _e( 'Successful', 'custom-cms-referal' ); ?></div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value"><?php echo esc_html( $user_stats['pending_referrals'] ); ?></div>
                <div class="stat-label"><?php _e( 'Pending', 'custom-cms-referal' ); ?></div>
            </div>
            
            <div class="stat-item">
                <div class="stat-value"><?php echo esc_html( $user_stats['current_points'] ); ?></div>
                <div class="stat-label"><?php _e( 'Points Balance', 'custom-cms-referal' ); ?></div>
            </div>
        </div>
    </div>
</div>
