/**
 * Custom CMS Referral Plugin - Admin Styles
 *
 * This file contains styles for the admin dashboard, referrals page,
 * and settings page of the Custom CMS Referral Plugin.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

/* Common Styles */
.custom-cms-referal-dashboard {
    margin: 20px 0;
}

/* Settings Page Styles */
.custom-cms-referal-settings {
    background: #fff;
    margin: 20px 0;
}

.custom-cms-referal-settings h1 {
    color: #1d2327;
    font-size: 23px;
    font-weight: 400;
    margin: 0 0 20px 0;
    padding: 0;
    line-height: 1.3;
}

.custom-cms-referal-settings .nav-tab-wrapper {
    border-bottom: 1px solid #c3c4c7;
    margin-bottom: 20px;
}

.custom-cms-referal-settings .nav-tab {
    background: #f6f7f7;
    border: 1px solid #c3c4c7;
    color: #50575e;
    text-decoration: none;
    padding: 8px 12px;
    margin-right: 5px;
    border-bottom: none;
    position: relative;
    top: 1px;
}

.custom-cms-referal-settings .nav-tab:hover {
    background: #fff;
    color: #2271b1;
}

.custom-cms-referal-settings .nav-tab-active {
    background: #fff;
    border-bottom: 1px solid #fff;
    color: #1d2327;
    font-weight: 600;
}

.custom-cms-referal-settings .nav-tab .dashicons {
    margin-right: 5px;
    vertical-align: middle;
}

/* Settings Sections */
.settings-section {
    background: #fff;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.settings-section h2 {
    margin-top: 0;
    color: #1d2327;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.settings-section .description {
    color: #646970;
    font-size: 13px;
    margin-bottom: 20px;
    line-height: 1.5;
}

.dashboard-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 4px;
}

.section-title {
    border-bottom: 1px solid #eee;
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    color: #23282d;
}

.section-title .dashicons {
    color: #2271b1;
    margin-right: 5px;
    font-size: 18px;
    vertical-align: middle;
}

/* Metrics Section */
.metrics-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 10px;
}

.metric-box {
    background-color: #f9f9f9;
    border-radius: 3px;
    flex: 1;
    min-width: 150px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-top: 3px solid #2271b1;
}

.metric-value {
    color: #2271b1;
    font-size: 32px;
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 8px;
}

.metric-label {
    color: #50575e;
    font-size: 14px;
}

/* Activity Section */
.activity-container {
    margin-top: 10px;
}

.activity-table th {
    font-weight: 600;
}

.placeholder-row td {
    text-align: center;
    color: #999;
    padding: 15px;
    font-style: italic;
}

/* Status Section */
.status-container {
    margin-top: 10px;
}

.status-table {
    table-layout: fixed;
}

.status-label {
    font-weight: 600;
    width: 70%;
}

.status-indicator {
    text-align: right;
    width: 30%;
}

.status-badge {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-ok {
    background-color: #edfaef;
    color: #2a9d3f;
    border: 1px solid #c3e6cb;
}

.status-warning {
    background-color: #fff8e5;
    color: #d4a000;
    border: 1px solid #ffeeba;
}

.status-error {
    background-color: #fbeaea;
    color: #c23934;
    border: 1px solid #f5c6cb;
}

.status-unknown {
    background-color: #f0f0f1;
    color: #666;
    border: 1px solid #ddd;
}

/* Quick Links Section */
.quick-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.quick-link-button {
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #2271b1;
    display: inline-flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
    padding: 8px 12px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.quick-link-button:hover {
    background-color: #f0f0f1;
    border-color: #c3c4c7;
    color: #135e96;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quick-link-button .dashicons {
    margin-right: 5px;
}

/* Form Styling */
.custom-cms-referal-settings .form-table {
    margin-top: 0;
}

.custom-cms-referal-settings .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.custom-cms-referal-settings .form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

.custom-cms-referal-settings .form-table .description {
    margin-top: 5px;
    margin-bottom: 0;
    color: #646970;
    font-size: 13px;
}

/* Toggle Switch Styles */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #2271b1;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Discount Settings Styles */
.discount-amount-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

#discount_amount {
    width: 80px;
}

#discount-symbol {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.4;
    color: #2271b1;
    background: #f0f6fc;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #c3c4c7;
}

.discount-type-select {
    min-width: 200px;
}

/* Submit Button Styling */
.custom-cms-referal-settings .submit {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

.custom-cms-referal-settings .button-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    text-decoration: none;
    text-shadow: none;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 3px;
    position: relative;
}

.custom-cms-referal-settings .button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.custom-cms-referal-settings .button-primary:disabled {
    background: #c3c4c7 !important;
    border-color: #c3c4c7 !important;
    color: #a7aaad !important;
    cursor: not-allowed;
}

/* Spinner for ajax requests */
.spinner.is-active {
    visibility: visible;
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 5px;
}

/* Settings Message */
#settings-message {
    margin-bottom: 15px;
}

/* Form Validation Styles */
.form-table input.error,
.form-table select.error {
    border-color: #dc3232;
    box-shadow: 0 0 2px rgba(220, 50, 50, 0.8);
}

.form-table input.error:focus,
.form-table select.error:focus {
    border-color: #dc3232;
    box-shadow: 0 0 2px rgba(220, 50, 50, 0.8);
}

/* Notice Styles */
.cms-referral-notice {
    margin: 15px 0;
}

.notice.notice-success {
    border-left-color: #46b450;
}

.notice.notice-error {
    border-left-color: #dc3232;
}

/* Plugin Footer */
.plugin-footer {
    margin-top: 30px;
    padding: 20px 0;
    border-top: 1px solid #e1e1e1;
    background: #f9f9f9;
    border-radius: 0 0 4px 4px;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 100%;
    margin: 0 auto;
    padding: 0 20px;
}

.footer-left .plugin-info {
    margin: 0;
    color: #646970;
    font-size: 13px;
}

.footer-left .plugin-info strong {
    color: #1d2327;
}

.footer-right .thank-you-message {
    margin: 0;
    color: #646970;
    font-size: 13px;
}

.footer-right .thank-you-message a {
    color: #2271b1;
    text-decoration: none;
}

.footer-right .thank-you-message a:hover {
    color: #135e96;
    text-decoration: underline;
}

/* Email Template Styling */
.custom-cms-referal-settings textarea.large-text {
    width: 100%;
    max-width: 500px;
    font-family: Consolas, Monaco, monospace;
    font-size: 13px;
    line-height: 1.4;
    background: #f9f9f9;
    border: 1px solid #c3c4c7;
    border-radius: 3px;
    padding: 10px;
}

.custom-cms-referal-settings textarea.large-text:focus {
    background: #fff;
    border-color: #2271b1;
    box-shadow: 0 0 0 1px #2271b1;
}

/* Responsive Adjustments */
@media screen and (max-width: 782px) {
    .metrics-container {
        flex-direction: column;
    }

    .metric-box {
        flex: none;
        width: 100%;
    }

    .quick-links {
        flex-direction: column;
    }

    .quick-link-button {
        width: 100%;
    }

    .discount-type-select {
        width: 100%;
        max-width: 100%;
    }

    .footer-content {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .custom-cms-referal-settings .form-table th {
        width: auto;
        padding: 10px 0;
    }

    .custom-cms-referal-settings .form-table td {
        padding: 10px 0;
    }
}