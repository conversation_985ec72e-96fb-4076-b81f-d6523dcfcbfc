<?php
/**
 * Test file for referral code usage limit functionality
 * 
 * This file can be used to test the usage limit validation
 * Run this file in a WordPress environment to test the functionality
 * 
 * IMPORTANT: Remove this file after testing!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll include WordPress if not already loaded
    require_once('../../../wp-config.php');
}

// Test the usage limit functionality
function test_usage_limit_functionality() {
    echo "<h2>Testing Referral Code Usage Limit Functionality</h2>\n";
    
    // Initialize the referral codes class
    if (class_exists('Custom_CMS_Referral_Codes')) {
        $referral_codes = new Custom_CMS_Referral_Codes();
        
        // Test with a sample referral code
        $test_code = 'USER11'; // Replace with an actual code from your system
        
        echo "<h3>Testing Code: {$test_code}</h3>\n";
        
        // Get current usage limit setting
        $usage_limit = get_option('custom_cms_referral_usage_limit', 0);
        echo "<p><strong>Current Usage Limit Setting:</strong> " . ($usage_limit == 0 ? 'Unlimited' : $usage_limit) . "</p>\n";
        
        // Test detailed validation
        echo "<h4>Detailed Validation Test:</h4>\n";
        $validation_result = $referral_codes->validate_referral_code_detailed($test_code);
        
        echo "<pre>";
        print_r($validation_result);
        echo "</pre>";
        
        // Test usage statistics
        echo "<h4>Usage Statistics:</h4>\n";
        $usage_stats = $referral_codes->get_code_usage_stats($test_code);
        
        echo "<pre>";
        print_r($usage_stats);
        echo "</pre>";
        
        // Test different scenarios
        echo "<h4>Testing Different Scenarios:</h4>\n";
        
        // Test 1: Empty code
        echo "<p><strong>Test 1 - Empty Code:</strong></p>";
        $result1 = $referral_codes->validate_referral_code_detailed('');
        echo "<p>Result: " . ($result1['valid'] ? 'VALID' : 'INVALID') . " - " . $result1['message'] . "</p>\n";
        
        // Test 2: Invalid code
        echo "<p><strong>Test 2 - Invalid Code:</strong></p>";
        $result2 = $referral_codes->validate_referral_code_detailed('INVALID123');
        echo "<p>Result: " . ($result2['valid'] ? 'VALID' : 'INVALID') . " - " . $result2['message'] . "</p>\n";
        
        // Test 3: Valid code (if exists)
        echo "<p><strong>Test 3 - Test Code ({$test_code}):</strong></p>";
        $result3 = $referral_codes->validate_referral_code_detailed($test_code);
        echo "<p>Result: " . ($result3['valid'] ? 'VALID' : 'INVALID') . " - " . $result3['message'] . "</p>\n";
        
        // Show current tracking data
        echo "<h4>Current Tracking Data:</h4>\n";
        global $wpdb;
        $table_name = $wpdb->prefix . 'custom_cms_referral_tracking';
        
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") == $table_name) {
            $tracking_data = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY date_created DESC LIMIT 10");
            
            if ($tracking_data) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Referrer ID</th><th>Referred ID</th><th>Code</th><th>Status</th><th>Date Created</th></tr>";
                
                foreach ($tracking_data as $row) {
                    echo "<tr>";
                    echo "<td>{$row->id}</td>";
                    echo "<td>{$row->referrer_id}</td>";
                    echo "<td>{$row->referred_id}</td>";
                    echo "<td>{$row->referral_code}</td>";
                    echo "<td>{$row->status}</td>";
                    echo "<td>{$row->date_created}</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No tracking data found.</p>";
            }
        } else {
            echo "<p>Tracking table does not exist yet.</p>";
        }
        
    } else {
        echo "<p><strong>Error:</strong> Custom_CMS_Referral_Codes class not found. Make sure the plugin is activated.</p>";
    }
}

// Run the test if accessed directly
if (isset($_GET['test']) && $_GET['test'] == 'usage_limit') {
    echo "<!DOCTYPE html><html><head><title>Usage Limit Test</title></head><body>";
    test_usage_limit_functionality();
    echo "<hr><p><strong>IMPORTANT:</strong> Remember to delete this test file after testing!</p>";
    echo "</body></html>";
} else {
    echo "<!DOCTYPE html><html><head><title>Usage Limit Test</title></head><body>";
    echo "<h1>Referral Code Usage Limit Test</h1>";
    echo "<p>Click the link below to run the test:</p>";
    echo "<p><a href='?test=usage_limit'>Run Usage Limit Test</a></p>";
    echo "<p><strong>Note:</strong> This will test the usage limit validation functionality.</p>";
    echo "</body></html>";
}
?>
