<?php
/**
 * Plugin Name: Custom CMS Referral Plugin
 * Plugin URI: https://vedmg.com
 * Description: A standalone rewards and referral system plugin that integrates directly with MasterStudy LMS.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>
 * Author URI: https://vedmg.com
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: custom-cms-referal
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'CUSTOM_CMS_REFERAL_PLUGIN_VERSION', '1.0.0' );
define( 'CUSTOM_CMS_REFERAL_VERSION', '1.0.0' );
define( 'CUSTOM_CMS_REFERAL_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'CUSTOM_CMS_REFERAL_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );
define( 'CUSTOM_CMS_REFERAL_PLUGIN_URL', plugin_dir_url( __FILE__ ) );

// Include the activator and deactivator classes
require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'database/activator.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'database/deactivator.php';

// Include the autoloader
require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'database/autoloader.php';

// Include the core class
require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'class-core.php';

// Include admin files
require_once plugin_dir_path( __FILE__ ) . 'admin/link.php';

// Include frontend files
require_once plugin_dir_path( __FILE__ ) . 'frontend/link.php';

// Include core functionality files
require_once plugin_dir_path( __FILE__ ) . 'includes/linkincludes.php';

// Register activation and deactivation hooks
register_activation_hook( __FILE__, array( 'Custom_CMS_Referral_Activator', 'activate' ) );
register_deactivation_hook( __FILE__, array( 'Custom_CMS_Referral_Deactivator', 'deactivate' ) );

/**
 * Main function to initialize the plugin.
 * Returns the single instance of the core class.
 *
 * @return Custom_CMS_Referral_Core
 */
function custom_cms_referral_init() {
    return Custom_CMS_Referral_Core::instance();
}

// Initialize the plugin
add_action( 'plugins_loaded', 'custom_cms_referral_init', 10 );

// Check for database upgrades on admin init
add_action( 'admin_init', array( 'Custom_CMS_Referral_Activator', 'maybe_upgrade_database' ) );

// Globally accessible instance for plugin integrations
$custom_cms_referral = custom_cms_referral_init();
$GLOBALS['custom_cms_referral'] = custom_cms_referral_init();
