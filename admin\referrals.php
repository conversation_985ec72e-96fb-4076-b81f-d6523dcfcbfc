<?php
/**
 * Admin Referrals Management UI
 *
 * This file displays the referrals management page for the Custom CMS Referral Plugin.
 * It includes search, filters, and action buttons for managing referrals.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check user permissions
if ( ! current_user_can( 'manage_options' ) ) {
    return;
}

// Get real data from referral tracker
global $custom_cms_referral_tracker;

$search_query = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
$status_filter = isset($_GET['status']) ? sanitize_text_field($_GET['status']) : '';
$current_page = isset($_GET['paged']) ? absint($_GET['paged']) : 1;
$per_page = 20;

// Get referrals data
$referrals_data = array();
$total_items = 0;
$total_pages = 1;

if ( $custom_cms_referral_tracker ) {
    $result = $custom_cms_referral_tracker->get_referrals( array(
        'search'   => $search_query,
        'status'   => $status_filter,
        'page'     => $current_page,
        'per_page' => $per_page
    ) );

    $referrals_data = $result['referrals'];
    $total_items = $result['total_items'];
    $total_pages = $result['total_pages'];
}
?>

<div class="wrap custom-cms-referal-referrals">
    <h1 class="wp-heading-inline"><?php _e('Manage Referrals', 'custom-cms-referal'); ?></h1>

    <!-- Search Box -->
    <p class="search-box">
        <label class="screen-reader-text" for="referral-search"><?php _e('Search Referrals', 'custom-cms-referal'); ?></label>
        <input type="search" id="referral-search" name="s" value="<?php echo esc_attr($search_query); ?>" placeholder="<?php esc_attr_e('Search by name, code, etc', 'custom-cms-referal'); ?>">
        <input type="submit" id="search-submit" class="button" value="<?php esc_attr_e('Search', 'custom-cms-referal'); ?>">
    </p>

    <form method="get">
        <input type="hidden" name="page" value="custom-cms-referral-referrals">
        <?php if (!empty($search_query)) : ?>
            <input type="hidden" name="s" value="<?php echo esc_attr($search_query); ?>">
        <?php endif; ?>

        <!-- Top Navigation -->
        <div class="tablenav top">
            <!-- Bulk Actions -->
            <div class="alignleft actions bulkactions">
                <label for="bulk-action-selector-top" class="screen-reader-text"><?php _e('Select bulk action', 'custom-cms-referal'); ?></label>
                <select name="action" id="bulk-action-selector-top">
                    <option value="-1"><?php _e('Bulk Actions', 'custom-cms-referal'); ?></option>
                    <option value="approve"><?php _e('Approve', 'custom-cms-referal'); ?></option>
                    <option value="cancel"><?php _e('Cancel', 'custom-cms-referal'); ?></option>
                    <option value="delete"><?php _e('Delete', 'custom-cms-referal'); ?></option>
                </select>
                <input type="submit" id="doaction" class="button action" value="<?php esc_attr_e('Apply', 'custom-cms-referal'); ?>">
            </div>

            <!-- Filter Controls -->
            <div class="alignleft actions">
                <label for="filter-by-status" class="screen-reader-text"><?php _e('Filter by status', 'custom-cms-referal'); ?></label>
                <select name="status" id="filter-by-status">
                    <option value=""><?php _e('All Statuses', 'custom-cms-referal'); ?></option>
                    <option value="pending" <?php selected($status_filter, 'pending'); ?>><?php _e('Pending', 'custom-cms-referal'); ?></option>
                    <option value="completed" <?php selected($status_filter, 'completed'); ?>><?php _e('Completed', 'custom-cms-referal'); ?></option>
                    <option value="cancelled" <?php selected($status_filter, 'cancelled'); ?>><?php _e('Cancelled', 'custom-cms-referal'); ?></option>
                </select>
                <input type="submit" name="filter_action" id="post-query-submit" class="button" value="<?php esc_attr_e('Filter', 'custom-cms-referal'); ?>">
            </div>

            <!-- Pagination Placeholder (Top) -->
            <div class="tablenav-pages">
                <span class="displaying-num"><?php echo sprintf(_n('%s item', '%s items', 0, 'custom-cms-referal'), number_format_i18n(0)); ?></span>
                <span class="pagination-links">
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>
                    <span class="paging-input">
                        <label for="current-page-selector" class="screen-reader-text"><?php _e('Current Page', 'custom-cms-referal'); ?></label>
                        <input class="current-page" id="current-page-selector" type="text" name="paged" value="<?php echo esc_attr($current_page); ?>" size="1" aria-describedby="table-paging">
                        <span class="tablenav-paging-text"> <?php _e('of', 'custom-cms-referal'); ?> <span class="total-pages">1</span></span>
                    </span>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>
                    <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>
                </span>
            </div>

            <br class="clear">
        </div>

    <!-- Referrals Table -->
    <table class="wp-list-table widefat fixed striped referrals-table">
        <thead>
            <tr>
                <td id="cb" class="manage-column column-cb check-column">
                    <label class="screen-reader-text" for="cb-select-all-1"><?php _e('Select All', 'custom-cms-referal'); ?></label>
                    <input id="cb-select-all-1" type="checkbox">
                </td>
                <th scope="col" class="manage-column column-id"><?php _e('ID', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-referrer"><?php _e('Referrer', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-referred"><?php _e('Referred', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-code"><?php _e('Code', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-course"><?php _e('Course', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-original-price"><?php _e('Original Price', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-discounted-price"><?php _e('Discounted Price', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-order-id"><?php _e('Order ID', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-status"><?php _e('Status', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-date"><?php _e('Date', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-actions"><?php _e('Actions', 'custom-cms-referal'); ?></th>
            </tr>
        </thead>

        <tbody id="the-list">
            <?php if ( ! empty( $referrals_data ) ): ?>
                <?php foreach ( $referrals_data as $referral ): ?>
                    <tr>
                        <th scope="row" class="check-column">
                            <input type="checkbox" name="referral[]" value="<?php echo esc_attr( $referral->id ); ?>">
                        </th>
                        <td class="column-id"><?php echo esc_html( $referral->id ); ?></td>
                        <td class="column-referrer"><?php echo esc_html( $referral->referrer_name ?: 'N/A' ); ?></td>
                        <td class="column-referred"><?php echo esc_html( $referral->referred_name ?: 'N/A' ); ?></td>
                        <td class="column-code"><?php echo esc_html( $referral->referral_code ); ?></td>
                        <td class="column-course"><?php echo esc_html( $referral->course_name ?: 'N/A' ); ?></td>
                        <td class="column-original-price">
                            <?php
                            $original_price = isset($referral->original_price) ? floatval($referral->original_price) : 0;
                            echo $original_price > 0 ? '$' . number_format($original_price, 2) : 'N/A';
                            ?>
                        </td>
                        <td class="column-discounted-price">
                            <?php
                            $discounted_price = isset($referral->discounted_price) ? floatval($referral->discounted_price) : 0;
                            echo $discounted_price > 0 ? '$' . number_format($discounted_price, 2) : 'N/A';
                            ?>
                        </td>
                        <td class="column-order-id">
                            <?php
                            $order_id = isset($referral->order_id) ? intval($referral->order_id) : 0;
                            echo $order_id > 0 ? '#' . $order_id : 'N/A';
                            ?>
                        </td>
                        <td class="column-status">
                            <?php if ( $referral->status === 'completed' ): ?>
                                <span class="status-badge status-completed">Completed</span>
                            <?php elseif ( $referral->status === 'pending' ): ?>
                                <span class="status-badge status-pending">Pending</span>
                            <?php elseif ( $referral->status === 'cancelled' ): ?>
                                <span class="status-badge status-cancelled">Cancelled</span>
                            <?php else: ?>
                                <span class="status-badge status-pending"><?php echo esc_html( ucfirst( $referral->status ) ); ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="column-date"><?php echo esc_html( date( 'Y-m-d', strtotime( $referral->created_at ) ) ); ?></td>
                        <td class="column-actions">
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="#" class="edit-referral" data-id="<?php echo esc_attr( $referral->id ); ?>">Edit</a>
                                    <?php if ( $referral->status === 'pending' ): ?> | <?php endif; ?>
                                </span>
                                <?php if ( $referral->status === 'pending' ): ?>
                                    <span class="approve">
                                        <a href="#" class="approve-referral" data-id="<?php echo esc_attr( $referral->id ); ?>">Approve</a> |
                                    </span>
                                <?php endif; ?>
                                <span class="trash">
                                    <a href="#" class="delete-referral" data-id="<?php echo esc_attr( $referral->id ); ?>" style="color: #a00;">Delete</a>
                                </span>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr class="no-items">
                    <td class="colspanchange" colspan="12">No referrals found.</td>
                </tr>
            <?php endif; ?>
        </tbody>

        <tfoot>
            <tr>
                <td class="manage-column column-cb check-column">
                    <label class="screen-reader-text" for="cb-select-all-2"><?php _e('Select All', 'custom-cms-referal'); ?></label>
                    <input id="cb-select-all-2" type="checkbox">
                </td>
                <th scope="col" class="manage-column column-id"><?php _e('ID', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-referrer"><?php _e('Referrer', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-referred"><?php _e('Referred', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-code"><?php _e('Code', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-course"><?php _e('Course', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-original-price"><?php _e('Original Price', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-discounted-price"><?php _e('Discounted Price', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-order-id"><?php _e('Order ID', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-status"><?php _e('Status', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-date"><?php _e('Date', 'custom-cms-referal'); ?></th>
                <th scope="col" class="manage-column column-actions"><?php _e('Actions', 'custom-cms-referal'); ?></th>
            </tr>
        </tfoot>
    </table>

    <!-- Pagination (Bottom) -->
    <div class="tablenav bottom">
        <div class="alignleft actions bulkactions">
            <label for="bulk-action-selector-bottom" class="screen-reader-text"><?php _e('Select bulk action', 'custom-cms-referal'); ?></label>
            <select name="action2" id="bulk-action-selector-bottom">
                <option value="-1"><?php _e('Bulk Actions', 'custom-cms-referal'); ?></option>
                <option value="approve"><?php _e('Approve', 'custom-cms-referal'); ?></option>
                <option value="cancel"><?php _e('Cancel', 'custom-cms-referal'); ?></option>
                <option value="delete"><?php _e('Delete', 'custom-cms-referal'); ?></option>
            </select>
            <input type="submit" id="doaction2" class="button action" value="<?php esc_attr_e('Apply', 'custom-cms-referal'); ?>">
        </div>

        <!-- Pagination Placeholder (Bottom) -->
        <div class="tablenav-pages">
            <span class="displaying-num"><?php echo sprintf(_n('%s item', '%s items', 0, 'custom-cms-referal'), number_format_i18n(0)); ?></span>
            <span class="pagination-links">
                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>
                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>
                <span class="paging-input">
                    <label for="current-page-selector-bottom" class="screen-reader-text"><?php _e('Current Page', 'custom-cms-referal'); ?></label>
                    <input class="current-page" id="current-page-selector-bottom" type="text" name="paged" value="<?php echo esc_attr($current_page); ?>" size="1" aria-describedby="table-paging">
                    <span class="tablenav-paging-text"> <?php _e('of', 'custom-cms-referal'); ?> <span class="total-pages">1</span></span>
                </span>
                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>
                <span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>
            </span>
        </div>
    </div>

    <!-- Edit Referral Modal (Hidden) -->
    <div id="edit-referral-modal" class="modal-dialog" style="display:none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><?php _e('Edit Referral', 'custom-cms-referal'); ?></h3>
                <span class="close-modal dashicons dashicons-no-alt"></span>
            </div>
            <div class="modal-body">
                <form id="edit-referral-form">
                    <input type="hidden" id="referral_id" name="referral_id" value="">

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="referrer_name"><?php _e('Referrer Name', 'custom-cms-referal'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="referrer_name" name="referrer_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="referred_name"><?php _e('Referred Name', 'custom-cms-referal'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="referred_name" name="referred_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="referral_code"><?php _e('Referral Code', 'custom-cms-referal'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="referral_code" name="referral_code" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="course_name"><?php _e('Course Name', 'custom-cms-referal'); ?></label>
                            </th>
                            <td>
                                <input type="text" id="course_name" name="course_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="status"><?php _e('Status', 'custom-cms-referal'); ?></label>
                            </th>
                            <td>
                                <select id="status" name="status">
                                    <option value="pending"><?php _e('Pending', 'custom-cms-referal'); ?></option>
                                    <option value="completed"><?php _e('Completed', 'custom-cms-referal'); ?></option>
                                    <option value="cancelled"><?php _e('Cancelled', 'custom-cms-referal'); ?></option>
                                </select>
                            </td>
                        </tr>
                    </table>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="button button-secondary" id="cancel-edit"><?php _e('Cancel', 'custom-cms-referal'); ?></button>
                <button type="button" class="button button-primary" id="save-referral"><?php _e('Update Referral', 'custom-cms-referal'); ?></button>
            </div>
        </div>
    </div>
</div>

<!-- Additional CSS for referrals page -->
<style type="text/css">
    /* Fix for the search box positioning */
    .custom-cms-referal-referrals .search-box {
        float: right;
        margin: 0 0 8px;
    }

    /* Fix for tablenav spacing */
    .custom-cms-referal-referrals .tablenav {
        clear: both;
        height: auto;
        margin: 6px 0 4px;
        padding: 8px 0;
    }

    .custom-cms-referal-referrals .tablenav .actions {
        padding: 2px 0 0;
        margin-right: 8px;
    }

    /* Fix pagination alignment */
    .custom-cms-referal-referrals .tablenav-pages {
        float: right;
        margin: 0 0 9px;
    }

    /* Fix table header styling */
    .referrals-table th {
        font-weight: 600;
    }

    /* Status badges */
    .status-badge {
        display: inline-block;
        padding: 3px 10px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-pending {
        background-color: #fff8e5;
        color: #d4a000;
        border: 1px solid #ffeeba;
    }

    .status-completed {
        background-color: #edfaef;
        color: #2a9d3f;
        border: 1px solid #c3e6cb;
    }

    .status-cancelled {
        background-color: #fbeaea;
        color: #c23934;
        border: 1px solid #f5c6cb;
    }

    /* Column widths */
    .referrals-table .column-id {
        width: 4%;
    }

    .referrals-table .column-referrer,
    .referrals-table .column-referred {
        width: 12%;
    }

    .referrals-table .column-code {
        width: 8%;
    }

    .referrals-table .column-course {
        width: 15%;
    }

    .referrals-table .column-original-price,
    .referrals-table .column-discounted-price {
        width: 10%;
        text-align: right;
    }

    .referrals-table .column-order-id {
        width: 8%;
        text-align: center;
    }

    .referrals-table .column-status,
    .referrals-table .column-date {
        width: 8%;
    }

    .referrals-table .column-actions {
        width: 12%;
    }

    /* Price column styling */
    .referrals-table .column-original-price,
    .referrals-table .column-discounted-price {
        font-weight: 600;
        color: #2c3e50;
    }

    .referrals-table .column-discounted-price {
        color: #27ae60;
    }

    .referrals-table .column-order-id {
        font-family: monospace;
        font-size: 12px;
        color: #666;
    }

    /* Modal styles */
    .modal-dialog {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 99999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: #fff;
        width: 500px;
        max-width: 90%;
        border-radius: 4px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    }

    .modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .close-modal {
        cursor: pointer;
        color: #666;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #e5e5e5;
        text-align: right;
    }

    .modal-footer button {
        margin-left: 10px;
    }
</style>