<?php
/**
 * Database activator for Custom CMS Referral Plugin.
 *
 * Handles the creation of database tables when the plugin is activated.
 * Sets up all necessary tables according to the plugin schema.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class responsible for creating all plugin tables on activation.
 */
class Custom_CMS_Referral_Activator {

    /**
     * Current database version
     *
     * @var string
     */
    private static $db_version = '1.0.0';

    /**
     * The main activation method - runs when the plugin is activated.
     * Creates all necessary database tables and sets the DB version.
     *
     * @return void
     */
    public static function activate() {
        self::create_tables();
        self::upgrade_existing_tables();
        self::maybe_set_default_settings();

        // Set the current database version in the options table
        update_option('mst_referral_db_version', self::$db_version);
    }

    /**
     * Create all database tables needed for the plugin.
     * Uses dbD<PERSON>ta to create or update table schemas.
     *
     * @return void
     */
    private static function create_tables() {
        global $wpdb;

        // Include WordPress database upgrade functions
        if (!function_exists('dbDelta')) {
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        }

        // Set the default character set and collation
        $charset_collate = $wpdb->get_charset_collate();

        // Define table names with proper prefixing
        $referrals_table = $wpdb->prefix . 'mst_referrals';
        $rewards_table = $wpdb->prefix . 'mst_rewards';
        $transactions_table = $wpdb->prefix . 'mst_transactions';
        $coupons_table = $wpdb->prefix . 'mst_coupons';
        $settings_table = $wpdb->prefix . 'mst_settings';
        $logs_table = $wpdb->prefix . 'mst_logs';
        $tracking_table = $wpdb->prefix . 'custom_cms_referral_tracking';

        // Referrals table SQL
        $sql = "CREATE TABLE $referrals_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            referrer_id bigint(20) unsigned NOT NULL COMMENT 'User ID of the referrer',
            referrer_name varchar(100) NOT NULL COMMENT 'Username of the referrer',
            referred_id bigint(20) unsigned DEFAULT NULL COMMENT 'User ID of the referred user',
            referred_name varchar(100) DEFAULT NULL COMMENT 'Username or email of the referred user',
            referral_code varchar(32) NOT NULL COMMENT 'Unique referral code',
            course_id bigint(20) unsigned DEFAULT NULL COMMENT 'Course ID if applicable',
            course_name varchar(255) DEFAULT NULL COMMENT 'Course name if applicable',
            order_id bigint(20) unsigned DEFAULT NULL COMMENT 'Order ID if applicable',
            status varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status of the referral',
            amount decimal(10,2) DEFAULT '0.00' COMMENT 'Referral amount if applicable',
            points_awarded int(11) DEFAULT '0' COMMENT 'Points awarded for this referral',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY referred_id (referred_id),
            KEY referral_code (referral_code),
            KEY status (status)
        ) $charset_collate;";

        dbDelta($sql);

        // Rewards table SQL
        $sql = "CREATE TABLE $rewards_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL COMMENT 'User ID',
            points_balance int(11) NOT NULL DEFAULT '0' COMMENT 'Current points balance',
            lifetime_points int(11) NOT NULL DEFAULT '0' COMMENT 'Total points earned',
            last_activity datetime DEFAULT NULL COMMENT 'Last activity date',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY user_id (user_id)
        ) $charset_collate;";

        dbDelta($sql);

        // Transactions table SQL
        $sql = "CREATE TABLE $transactions_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL COMMENT 'User ID',
            type varchar(50) NOT NULL COMMENT 'Transaction type (earn, redeem, expire, etc.)',
            points int(11) NOT NULL DEFAULT '0' COMMENT 'Points amount (positive or negative)',
            description text COMMENT 'Transaction description',
            reference_id bigint(20) unsigned DEFAULT NULL COMMENT 'Reference ID (order, referral, etc.)',
            reference_type varchar(50) DEFAULT NULL COMMENT 'Reference type',
            status varchar(20) NOT NULL DEFAULT 'completed' COMMENT 'Transaction status',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY type (type),
            KEY status (status)
        ) $charset_collate;";

        dbDelta($sql);

        // Coupons table SQL
        $sql = "CREATE TABLE $coupons_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            user_id bigint(20) unsigned NOT NULL COMMENT 'User ID who owns the coupon',
            code varchar(32) NOT NULL COMMENT 'Coupon code',
            type varchar(50) NOT NULL DEFAULT 'fixed' COMMENT 'Coupon type (fixed, percentage)',
            amount decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT 'Coupon amount',
            points_used int(11) NOT NULL DEFAULT '0' COMMENT 'Points used to create this coupon',
            restrictions text COMMENT 'JSON encoded coupon restrictions',
            status varchar(20) NOT NULL DEFAULT 'active' COMMENT 'Coupon status',
            expires_at datetime DEFAULT NULL COMMENT 'Expiration date',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY code (code),
            KEY user_id (user_id),
            KEY status (status)
        ) $charset_collate;";

        dbDelta($sql);

        // Settings table SQL
        $sql = "CREATE TABLE $settings_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            option_name varchar(191) NOT NULL COMMENT 'Setting name',
            option_value longtext COMMENT 'Setting value (serialized if needed)',
            autoload varchar(20) NOT NULL DEFAULT 'yes' COMMENT 'Whether to load on init',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY option_name (option_name)
        ) $charset_collate;";

        dbDelta($sql);

        // Logs table SQL
        $sql = "CREATE TABLE $logs_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            level varchar(20) NOT NULL DEFAULT 'info' COMMENT 'Log level (info, warning, error)',
            message text NOT NULL COMMENT 'Log message',
            context text COMMENT 'Additional context data (JSON)',
            user_id bigint(20) unsigned DEFAULT NULL COMMENT 'Associated user if applicable',
            ip_address varchar(45) DEFAULT NULL COMMENT 'IP address',
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY level (level),
            KEY user_id (user_id)
        ) $charset_collate;";

        dbDelta($sql);

        // Temporary tracking table SQL (following custom-referal-plugin pattern)
        $sql = "CREATE TABLE $tracking_table (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            referrer_id bigint(20) unsigned NOT NULL COMMENT 'User ID of the referrer',
            referred_id bigint(20) unsigned DEFAULT 0 COMMENT 'User ID of the referred user (0 if not set)',
            referral_code varchar(32) NOT NULL COMMENT 'Referral code used',
            status varchar(20) NOT NULL DEFAULT 'pending' COMMENT 'Status: active, pending, completed',
            logged_in_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the tracking was created',
            PRIMARY KEY (id),
            KEY referrer_id (referrer_id),
            KEY referred_id (referred_id),
            KEY referral_code (referral_code),
            KEY status (status)
        ) $charset_collate;";

        dbDelta($sql);
    }

    /**
     * Upgrade existing tables to add missing columns
     *
     * @return void
     */
    private static function upgrade_existing_tables() {
        global $wpdb;

        $referrals_table = $wpdb->prefix . 'mst_referrals';

        // Check if course_name column exists
        $column_exists = $wpdb->get_results(
            $wpdb->prepare(
                "SHOW COLUMNS FROM {$referrals_table} LIKE %s",
                'course_name'
            )
        );

        // Add course_name column if it doesn't exist
        if (empty($column_exists)) {
            $wpdb->query(
                "ALTER TABLE {$referrals_table}
                ADD COLUMN course_name varchar(255) DEFAULT NULL COMMENT 'Course name if applicable'
                AFTER course_id"
            );
        }
    }

    /**
     * Set default settings if they don't exist yet.
     *
     * @return void
     */
    private static function maybe_set_default_settings() {
        global $wpdb;

        $settings_table = $wpdb->prefix . 'mst_settings';

        // Default settings to insert
        $default_settings = [
            [
                'option_name' => 'points_per_referral',
                'option_value' => '100',
                'autoload' => 'yes'
            ],
            [
                'option_name' => 'referral_discount_percentage',
                'option_value' => '10',
                'autoload' => 'yes'
            ],
            [
                'option_name' => 'points_exchange_rate',
                'option_value' => '100', // 100 points = $1
                'autoload' => 'yes'
            ],
            [
                'option_name' => 'min_points_redeem',
                'option_value' => '500',
                'autoload' => 'yes'
            ],
            [
                'option_name' => 'referral_cookie_days',
                'option_value' => '30',
                'autoload' => 'yes'
            ],
        ];

        // Insert default settings
        foreach ($default_settings as $setting) {
            // Check if the setting already exists
            $exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $settings_table WHERE option_name = %s",
                $setting['option_name']
            ));

            // Only insert if it doesn't exist
            if (!$exists) {
                $wpdb->insert(
                    $settings_table,
                    $setting,
                    ['%s', '%s', '%s']
                );
            }
        }
    }
}

