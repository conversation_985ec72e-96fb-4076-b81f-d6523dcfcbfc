<?php
/**
 * PSR-4 compatible autoloader for Custom CMS Referral Plugin.
 *
 * This autoloader will handle all classes within the plugin namespace,
 * allowing for clean code organization and on-demand loading.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class responsible for PSR-4 compatible autoloading.
 */
class Custom_CMS_Referral_Autoloader {

    /**
     * The base namespace for plugin classes.
     *
     * @var string
     */
    private $namespace = 'Custom_CMS_Referral';

    /**
     * The base directory for plugin classes.
     *
     * @var string
     */
    private $base_dir;

    /**
     * Initialize the autoloader.
     */
    public function __construct() {
        // Set the base directory for our classes
        $this->base_dir = CUSTOM_CMS_REFERAL_PLUGIN_DIR;
        
        // Register the autoloader
        spl_autoload_register( array( $this, 'autoload' ) );
    }

    /**
     * Autoload function that loads classes based on namespace and path.
     *
     * @param string $class_name The fully qualified class name.
     */
    public function autoload( $class_name ) {
        // Check if the class uses our namespace prefix
        $namespace_length = strlen( $this->namespace );
        
        if ( strncmp( $this->namespace, $class_name, $namespace_length ) !== 0 ) {
            // Not our namespace, do nothing
            return;
        }
        
        // Get the relative class name (remove namespace)
        $relative_class = substr( $class_name, $namespace_length + 1 ); // +1 for the backslash
        
        // Convert namespace separators to directory separators
        $file_path = $this->base_dir . str_replace( '_', '/', strtolower( $relative_class ) ) . '.php';
        
        // If the file exists, require it
        if ( file_exists( $file_path ) ) {
            require_once $file_path;
        }
    }

    /**
     * Initializes the autoloader as a singleton.
     *
     * @return Custom_CMS_Referral_Autoloader
     */
    public static function init() {
        static $instance = null;
        
        if ( $instance === null ) {
            $instance = new self();
        }
        
        return $instance;
    }
}

// Initialize the autoloader
Custom_CMS_Referral_Autoloader::init();
