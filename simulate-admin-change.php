<?php
/**
 * SIMULATE ADMIN INTERFACE order status changes
 * 
 * This script will:
 * 1. Simulate the exact WordPress admin interface behavior
 * 2. Trigger the same hooks that fire when admin changes order status
 * 3. Monitor for mass update behavior
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== SIMULATING ADMIN INTERFACE BEHAVIOR ===\n";
echo "Current time: " . date('Y-m-d H:i:s') . "\n\n";

global $wpdb;

// Function to get debug log content
function get_debug_log_tail($lines = 50) {
    $debug_file = 'debug.log';
    if (!file_exists($debug_file)) {
        return "Debug log file not found.";
    }
    
    $content = file_get_contents($debug_file);
    if (empty($content)) {
        return "Debug log is empty.";
    }
    
    $log_lines = explode("\n", trim($content));
    $tail_lines = array_slice($log_lines, -$lines);
    return implode("\n", $tail_lines);
}

// Function to capture referral states
function capture_referral_state() {
    global $wpdb;
    
    $referrals = $wpdb->get_results("
        SELECT id, order_id, status, referrer_id, referred_id, updated_at 
        FROM wp_mst_referrals 
        ORDER BY id ASC
    ");
    
    $state = array();
    foreach ($referrals as $ref) {
        $state[$ref->id] = array(
            'order_id' => $ref->order_id,
            'status' => $ref->status,
            'updated_at' => $ref->updated_at,
            'referrer_id' => $ref->referrer_id,
            'referred_id' => $ref->referred_id
        );
    }
    
    return $state;
}

// Function to simulate admin save (triggers WordPress hooks)
function simulate_admin_order_save($order_id, $new_status) {
    global $wpdb;
    
    echo "🔄 SIMULATING ADMIN SAVE: Order {$order_id} → '{$new_status}'\n";
    
    // Get current status
    $old_status = get_post_meta($order_id, 'status', true);
    echo "   Old status: {$old_status}\n";
    
    // Update meta field (this should trigger updated_post_meta hook)
    $result = update_post_meta($order_id, 'status', $new_status);
    
    // Also trigger save_post hook manually to simulate admin save
    do_action('save_post', $order_id, get_post($order_id), false);
    
    echo "   New status: {$new_status}\n";
    echo "   Meta update result: " . ($result ? 'SUCCESS' : 'NO CHANGE') . "\n";
    
    return $result;
}

// Function to compare referral states
function compare_referral_states($before, $after, $action) {
    echo "\n=== REFERRAL CHANGES AFTER: {$action} ===\n";
    echo str_repeat("-", 60) . "\n";
    
    $changes = array();
    
    foreach ($after as $ref_id => $after_data) {
        if (isset($before[$ref_id])) {
            $before_data = $before[$ref_id];
            
            if ($before_data['status'] !== $after_data['status'] || 
                $before_data['updated_at'] !== $after_data['updated_at']) {
                
                echo "🚨 Referral {$ref_id} (Order {$after_data['order_id']}): ";
                echo "'{$before_data['status']}' → '{$after_data['status']}' ";
                echo "(Updated: {$after_data['updated_at']})\n";
                
                $changes[] = array(
                    'referral_id' => $ref_id,
                    'order_id' => $after_data['order_id'],
                    'from' => $before_data['status'],
                    'to' => $after_data['status']
                );
            }
        }
    }
    
    if (empty($changes)) {
        echo "✅ No referral changes detected\n";
    } else {
        echo "\n📊 TOTAL CHANGES: " . count($changes) . " referrals affected\n";
        
        // Group by order to see if multiple orders were affected
        $orders_affected = array();
        foreach ($changes as $change) {
            $orders_affected[$change['order_id']] = true;
        }
        
        echo "📦 ORDERS AFFECTED: " . count($orders_affected) . " orders\n";
        
        if (count($changes) > 1 && count($orders_affected) > 1) {
            echo "🚨 MASS UPDATE BUG DETECTED! Multiple orders affected by single change!\n";
        } elseif (count($changes) > 1 && count($orders_affected) == 1) {
            echo "ℹ️  Multiple referrals for same order affected (normal behavior)\n";
        }
    }
    
    return $changes;
}

echo "STEP 1: Capturing initial referral state...\n";
$initial_referrals = capture_referral_state();

echo "Current referrals:\n";
foreach ($initial_referrals as $ref_id => $data) {
    echo "  Referral {$ref_id}: Order {$data['order_id']} → {$data['status']} (Updated: {$data['updated_at']})\n";
}

echo "\nSTEP 2: Initial debug log state...\n";
$debug_before = get_debug_log_tail(5);
echo str_repeat("-", 40) . "\n";
echo $debug_before . "\n";
echo str_repeat("-", 40) . "\n";

// Find a test order
$test_order_id = 49688; // From previous tests
$current_status = get_post_meta($test_order_id, 'status', true);

echo "\nSTEP 3: TESTING WITH ORDER {$test_order_id}\n";
echo "Current status: {$current_status}\n\n";

// Test 1: Change status
$new_status = ($current_status === 'pending') ? 'completed' : 'pending';

echo "TEST 1: Changing Order {$test_order_id} from '{$current_status}' to '{$new_status}'\n";
echo str_repeat("=", 70) . "\n";

simulate_admin_order_save($test_order_id, $new_status);

sleep(3); // Wait for hooks to process

$referrals_after_test1 = capture_referral_state();
$changes1 = compare_referral_states($initial_referrals, $referrals_after_test1, "Test 1");

echo "\nDebug log after Test 1:\n";
echo str_repeat("-", 40) . "\n";
echo get_debug_log_tail(30) . "\n";
echo str_repeat("-", 40) . "\n";

// Test 2: Change back
echo "\nTEST 2: Changing Order {$test_order_id} back to '{$current_status}'\n";
echo str_repeat("=", 70) . "\n";

simulate_admin_order_save($test_order_id, $current_status);

sleep(3); // Wait for hooks to process

$referrals_after_test2 = capture_referral_state();
$changes2 = compare_referral_states($referrals_after_test1, $referrals_after_test2, "Test 2");

echo "\nDebug log after Test 2:\n";
echo str_repeat("-", 40) . "\n";
echo get_debug_log_tail(30) . "\n";
echo str_repeat("-", 40) . "\n";

echo "\n" . str_repeat("=", 80) . "\n";
echo "FINAL ANALYSIS\n";
echo str_repeat("=", 80) . "\n";

$total_changes1 = count($changes1);
$total_changes2 = count($changes2);

echo "📊 Test 1: {$total_changes1} referral changes\n";
echo "📊 Test 2: {$total_changes2} referral changes\n\n";

if ($total_changes1 > 0 || $total_changes2 > 0) {
    echo "✅ HOOKS ARE WORKING - Referral changes detected!\n";
    
    if ($total_changes1 > 1 || $total_changes2 > 1) {
        echo "🚨 MASS UPDATE ISSUE CONFIRMED!\n";
        echo "   Multiple referrals changed from single order modification\n";
    } else {
        echo "✅ Normal behavior - Only expected referrals changed\n";
    }
} else {
    echo "❌ NO CHANGES DETECTED - Hooks may not be firing properly\n";
}

echo "\n🔍 Check the debug log above for detailed hook activity\n";
?>
