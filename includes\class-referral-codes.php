<?php
/**
 * Referral Codes Class
 *
 * Handles the generation, validation, and assignment of referral codes.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Referral Codes class
 */
class Custom_CMS_Referral_Codes {

    /**
     * Code prefix for all generated referral codes
     *
     * @var string
     */
    private $code_prefix = 'MST-';

    /**
     * Code length not including the prefix
     *
     * @var int
     */
    private $code_length = 8;

    /**
     * Constructor
     */
    public function __construct() {
        // Get prefix from admin settings first, fallback to hardcoded value for backward compatibility
        $admin_prefix = get_option('custom_cms_referral_code_prefix', '');
        if (!empty($admin_prefix)) {
            $this->code_prefix = $admin_prefix;
        }

        // Allow prefix to be filtered (maintains existing filter compatibility)
        $this->code_prefix = apply_filters('custom_cms_referral_code_prefix', $this->code_prefix);

        // Allow length to be filtered (minimum 6, maximum 12)
        $filtered_length = apply_filters('custom_cms_referral_code_length', $this->code_length);
        $this->code_length = max(6, min(12, $filtered_length));
    }

    /**
     * Initialize hooks
     */
    public function init() {
        // Generate referral code on user registration
        add_action('user_register', array($this, 'assign_referral_code_to_user'));

        // Generate referral code on MasterStudy LMS course purchase
        add_action('stm_lms_order_created', array($this, 'check_and_assign_code_on_purchase'), 10, 2);

        // Add AJAX handlers for code validation
        add_action('wp_ajax_validate_referral_code', array($this, 'ajax_validate_referral_code'));
        add_action('wp_ajax_nopriv_validate_referral_code', array($this, 'ajax_validate_referral_code'));

        // CRITICAL FIX: Ensure existing users have referral codes
        add_action('init', array($this, 'ensure_existing_users_have_codes'), 20);
    }

    /**
     * Generate a unique referral code
     *
     * @param int $user_id User ID to base the code on
     * @return string Generated referral code
     */
    public function generate_referral_code($user_id) {
        // Get user data
        $user = get_user_by('id', $user_id);

        if (!$user) {
            // Fallback if user not found
            return $this->generate_random_code();
        }

        // Start with username (or email if no username)
        $username = $user->user_login ? $user->user_login : $user->user_email;

        // Remove special characters and convert to uppercase
        $username = preg_replace('/[^a-zA-Z0-9]/', '', $username);
        $username = strtoupper($username);

        // Truncate or pad to desired length
        if (strlen($username) > $this->code_length) {
            // Truncate if too long
            $base_code = substr($username, 0, $this->code_length - 2);

            // Add last two digits of user ID for uniqueness
            $user_id_part = sprintf('%02d', $user_id % 100);
            $base_code = $base_code . $user_id_part;
        } else {
            // Pad with random characters if too short
            $base_code = $username;
            while (strlen($base_code) < $this->code_length) {
                $base_code .= strtoupper(substr(md5(mt_rand()), 0, 1));
            }
        }

        // Add prefix
        $referral_code = $this->code_prefix . $base_code;

        // Ensure it's unique by checking the database
        if ($this->code_exists($referral_code)) {
            // If code exists, add a random suffix and try again
            $random_suffix = strtoupper(substr(md5(mt_rand()), 0, 2));
            $referral_code = $this->code_prefix . substr($base_code, 0, $this->code_length - 2) . $random_suffix;

            // If still exists (very unlikely), generate a completely random code
            if ($this->code_exists($referral_code)) {
                $referral_code = $this->generate_random_code();
            }
        }

        return $referral_code;
    }

    /**
     * Generate a completely random referral code
     *
     * @return string Random referral code
     */
    public function generate_random_code() {
        $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Removed similar looking characters I,O,1,0
        $random_code = '';

        for ($i = 0; $i < $this->code_length; $i++) {
            $random_code .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Add prefix
        $referral_code = $this->code_prefix . $random_code;

        // Ensure it's unique
        if ($this->code_exists($referral_code)) {
            // Try again if code exists (recursive, but very unlikely to recurse more than once)
            return $this->generate_random_code();
        }

        return $referral_code;
    }

    /**
     * Check if a referral code already exists in the database
     *
     * @param string $code Referral code to check
     * @return bool True if code exists, false otherwise
     */
    public function code_exists($code) {
        global $wpdb;

        // Check if the code exists in user meta
        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code' AND meta_value = %s",
            $code
        ));

        return $exists > 0;
    }

    /**
     * Validate a referral code
     *
     * @param string $code Referral code to validate
     * @return bool|int False if invalid, user ID if valid
     */
    public function validate_referral_code($code) {
        // Clean up the code
        $code = trim(strtoupper($code));

        // Basic format validation - check if code is not empty
        if (empty($code)) {
            error_log("CMS Referral: Validation failed - empty code");
            return false;
        }

        // Debug log the current prefix being used
        error_log("CMS Referral: Validating code '$code' with prefix '{$this->code_prefix}'");

        // CRITICAL FIX: Check for codes with different prefixes that might exist in the database
        // This handles cases where codes were generated with different prefixes
        global $wpdb;

        // First, try direct lookup without prefix validation
        $user_id = $wpdb->get_var($wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code' AND meta_value = %s",
            $code
        ));

        if ($user_id) {
            error_log("CMS Referral: Code '$code' found for user ID $user_id (direct lookup)");
        } else {
            error_log("CMS Referral: Code '$code' not found in database (direct lookup)");

            // Check format (must start with current prefix and be reasonable length)
            if (strpos($code, $this->code_prefix) !== 0) {
                error_log("CMS Referral: Code '$code' doesn't start with expected prefix '{$this->code_prefix}'");

                // CRITICAL FIX: Try to find codes with any prefix pattern
                // Look for codes that might have been generated with different prefixes
                $all_codes = $wpdb->get_results(
                    "SELECT user_id, meta_value FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code'"
                );

                error_log("CMS Referral: Found " . count($all_codes) . " total referral codes in database");

                // Check if the code exists with any prefix
                foreach ($all_codes as $existing_code) {
                    if ($existing_code->meta_value === $code) {
                        error_log("CMS Referral: Found matching code '$code' for user ID {$existing_code->user_id}");
                        $user_id = $existing_code->user_id;
                        break;
                    }
                }

                if (!$user_id) {
                    return false;
                }
            } else {
                // Check minimum length (prefix + at least 4 characters)
                if (strlen($code) < strlen($this->code_prefix) + 4) {
                    error_log("CMS Referral: Code '$code' too short (minimum: " . (strlen($this->code_prefix) + 4) . ")");
                    return false;
                }

                // CRITICAL FIX: If code not found, try to generate codes for users who don't have them
                // This handles the case where a user should have a code but it wasn't generated yet
                $this->force_generate_missing_codes();

                // Try the lookup again after generating missing codes
                $user_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code' AND meta_value = %s",
                    $code
                ));

                if (!$user_id) {
                    error_log("CMS Referral: Code '$code' still not found after generating missing codes");
                    return false;
                }
            }
        }

        // Prevent self-referrals
        if (get_current_user_id() == $user_id) {
            // error_log('Referral Error: Self-referral detected for code ' . $code);
            return false;
        }

        // Check usage limit
        $usage_limit = (int) get_option('custom_cms_referral_usage_limit', 0);

        if ($usage_limit > 0) {
            $table_name = $wpdb->prefix . 'custom_cms_referral_tracking';
            $completed_uses_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM {$table_name} WHERE referrer_id = %d AND status = %s",
                $user_id, // User ID of the referrer who owns the code
                'complete'
            ));

            if ($completed_uses_count >= $usage_limit) {
                // error_log('Referral Error: Usage limit reached for code ' . $code . ' (Owner: ' . $user_id . ', Limit: ' . $usage_limit . ', Uses: ' . $completed_uses_count . ')');
                return false; // Usage limit reached
            }
        }

        return $user_id;
    }

    /**
     * Assign a referral code to a user
     *
     * @param int $user_id User ID to assign code to
     * @return string|bool Referral code or false if user already has one
     */
    public function assign_referral_code_to_user($user_id) {
        // Validate user ID
        if (!$user_id || !is_numeric($user_id)) {
            return false;
        }

        // Check if user already has a referral code
        $existing_code = get_user_meta($user_id, 'custom_cms_referral_code', true);

        if (!empty($existing_code)) {
            return $existing_code; // Return existing code instead of false
        }

        // Generate a new code
        $referral_code = $this->generate_referral_code($user_id);

        if (!$referral_code) {
            return false;
        }

        // Save to user meta
        $result = update_user_meta($user_id, 'custom_cms_referral_code', $referral_code);

        if ($result) {
            return $referral_code;
        }

        return false;
    }

    /**
     * Check if user needs a referral code after purchase and assign one
     *
     * @param int $order_id Order ID
     * @param array $order_data Order data
     */
    public function check_and_assign_code_on_purchase($order_id, $order_data) {
        if (empty($order_data['user_id'])) {
            return;
        }

        $user_id = $order_data['user_id'];

        // Check if user already has a referral code
        $existing_code = get_user_meta($user_id, 'custom_cms_referral_code', true);

        if (empty($existing_code)) {
            // User doesn't have a code, generate and assign one
            $this->assign_referral_code_to_user($user_id);
        }
    }

    /**
     * AJAX handler for validating a referral code
     */
    public function ajax_validate_referral_code() {
        // Check nonce for security - support multiple nonce parameter names for compatibility
        $nonce = '';
        if (isset($_POST['nonce'])) {
            $nonce = $_POST['nonce'];
            $nonce_action = 'custom-cms-referral-nonce';
        } elseif (isset($_POST['security'])) {
            $nonce = $_POST['security'];
            $nonce_action = 'custom-cms-referral-form';
        }

        if (empty($nonce) || !wp_verify_nonce($nonce, $nonce_action)) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'custom-cms-referal')
            ));
            return;
        }

        $referral_code = isset($_POST['referral_code']) ? sanitize_text_field($_POST['referral_code']) : '';

        if (empty($referral_code)) {
            wp_send_json_error(array('message' => __('Please enter a referral code.', 'custom-cms-referal')));
            return;
        }

        $referring_user_id = $this->validate_referral_code($referral_code);

        if (!$referring_user_id) {
            wp_send_json_error(array('message' => __('Invalid referral code. Please check and try again.', 'custom-cms-referal')));
            return;
        }

        // Set a cookie with the referral code (get duration from admin settings)
        $cookie_duration_days = get_option('custom_cms_referral_cookie_duration', 30);
        $cookie_expiration = apply_filters('custom_cms_referral_cookie_expiration', $cookie_duration_days * DAY_IN_SECONDS);
        setcookie('custom_cms_referral_code', $referral_code, time() + $cookie_expiration, COOKIEPATH, COOKIE_DOMAIN);

        // Get the discount amount for this code
        $discount = $this->get_referral_discount($referral_code);

        wp_send_json_success(array(
            'message' => sprintf(__('Referral code applied successfully! You will receive a {%s} discount on your purchase.', 'custom-cms-referal'), $discount),
            'referring_user_id' => $referring_user_id,
            'discount' => $discount
        ));
    }

    /**
     * Get user's referral code
     *
     * @param int $user_id User ID
     * @return string|bool Referral code or false if not found
     */
    public function get_user_referral_code($user_id = 0) {
        // If no user ID specified, get current user
        if (!$user_id) {
            $user_id = get_current_user_id();
        }

        // If still no user ID, return false
        if (!$user_id) {
            return false;
        }

        // Get user referral code from user meta
        $referral_code = get_user_meta($user_id, 'custom_cms_referral_code', true);

        // If no code found, generate one automatically
        if (empty($referral_code)) {
            $referral_code = $this->assign_referral_code_to_user($user_id);

            // Log for debugging
            if ($referral_code) {
                error_log("CMS Referral: Auto-generated code '$referral_code' for user $user_id");
            }
        }

        return $referral_code;
    }

    /**
     * CRITICAL FIX: Ensure existing users have referral codes
     * This method runs once to generate codes for users who don't have them
     */
    public function ensure_existing_users_have_codes() {
        // Force run on first load or if it's been more than 1 hour since last run
        $last_run = get_option('custom_cms_referral_last_code_generation', 0);
        $current_time = time();

        // Run if it's been more than 1 hour since last run, or if it's never been run
        // Changed from 24 hours to 1 hour for more frequent checks during setup
        // TEMPORARY: Force run by resetting timestamp for immediate code generation
        if (($current_time - $last_run) < HOUR_IN_SECONDS && $last_run > 0 && false) {
            return;
        }

        global $wpdb;

        // Get users who don't have referral codes (limit to 20 at a time for better coverage)
        $users_without_codes = $wpdb->get_results("
            SELECT u.ID, u.user_login
            FROM {$wpdb->users} u
            LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'custom_cms_referral_code'
            WHERE um.meta_value IS NULL
            ORDER BY u.ID ASC
            LIMIT 20
        ");

        if (!empty($users_without_codes)) {
            $generated_count = 0;
            foreach ($users_without_codes as $user) {
                $code = $this->assign_referral_code_to_user($user->ID);
                if ($code) {
                    $generated_count++;
                    error_log("CMS Referral: Generated code '$code' for existing user {$user->user_login} (ID: {$user->ID})");
                }
            }

            if ($generated_count > 0) {
                error_log("CMS Referral: Generated $generated_count referral codes for existing users");
            }
        }

        // Update the last run timestamp
        update_option('custom_cms_referral_last_code_generation', $current_time);
    }

    /**
     * Force generate missing codes immediately (used during validation)
     * This is a more aggressive version that runs immediately when needed
     */
    public function force_generate_missing_codes() {
        global $wpdb;

        // Get up to 5 users who don't have referral codes
        $users_without_codes = $wpdb->get_results("
            SELECT u.ID, u.user_login
            FROM {$wpdb->users} u
            LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'custom_cms_referral_code'
            WHERE um.meta_value IS NULL
            ORDER BY u.ID ASC
            LIMIT 5
        ");

        if (!empty($users_without_codes)) {
            foreach ($users_without_codes as $user) {
                $code = $this->assign_referral_code_to_user($user->ID);
                if ($code) {
                    error_log("CMS Referral: Force-generated code '$code' for user {$user->user_login} (ID: {$user->ID})");
                }
            }
        }
    }

    /**
     * Track referral code usage
     *
     * @param string $code Referral code
     * @return bool Success or failure of tracking
     */
    public function get_referral_discount($code) {
        // Validate the code first
        $referrer_id = $this->validate_referral_code($code);

        if (!$referrer_id) {
            return false; // Invalid code
        }

        // Track this referral for the referrer
        $tracking_id = $this->track_referral_attempt($code, $referrer_id);

        // Return success or failure of tracking
        return $tracking_id ? true : false;
    }

    /**
     * Track referral attempt in the database
     *
     * @param string $code Referral code used
     * @param int $referrer_id User ID of the referrer
     * @return int|bool ID of the new record or false on failure
     */
    private function track_referral_attempt($code, $referrer_id) {
        global $wpdb;

        // Get current user info
        $current_user_id = get_current_user_id();
        $user_ip = $_SERVER['REMOTE_ADDR'];

        // Create referral tracking table if it doesn't exist yet
        $this->maybe_create_tracking_table();

        // Insert tracking record
        $table_name = $wpdb->prefix . 'custom_cms_referral_tracking';

        // Check if this combination already exists to prevent duplicates
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$table_name} WHERE referrer_id = %d AND referred_id = %d AND status = 'pending'",
            $referrer_id,
            $current_user_id
        ));

        if ($existing) {
            // Already tracked, just return the existing ID
            return $existing;
        }

        // Insert new tracking record
        $result = $wpdb->insert(
            $table_name,
            array(
                'referrer_id' => $referrer_id,
                'referred_id' => $current_user_id,
                'referral_code' => $code,
                'user_ip' => $user_ip,
                'date_created' => current_time('mysql'),
                'status' => 'pending' // Will be updated to 'complete' when purchase is made
            ),
            array('%d', '%d', '%s', '%s', '%s', '%s')
        );

        if ($result) {
            return $wpdb->insert_id;
        }

        return false;
    }

    /**
     * Create referral tracking table if it doesn't exist
     */
    private function maybe_create_tracking_table() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'custom_cms_referral_tracking';

        // Check if the table already exists
        if ($wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") != $table_name) {
            // Table doesn't exist, create it
            $charset_collate = $wpdb->get_charset_collate();

            $sql = "CREATE TABLE {$table_name} (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                referrer_id bigint(20) NOT NULL,
                referred_id bigint(20) NOT NULL,
                referral_code varchar(20) NOT NULL,
                user_ip varchar(100) NOT NULL,
                date_created datetime NOT NULL,
                date_completed datetime DEFAULT NULL,
                status varchar(20) NOT NULL DEFAULT 'pending',
                notes text DEFAULT NULL,
                PRIMARY KEY  (id),
                KEY referrer_id (referrer_id),
                KEY referred_id (referred_id),
                KEY status (status)
            ) $charset_collate;";

            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            dbDelta($sql);
        }
    }
}
