/**
 * Custom CMS Referral Plugin - Admin JavaScript
 *
 * Handles all JS functionality for the admin interface including:
 * - Modal handling
 * - Form validation and submission
 * - AJAX operations for referrals management
 * - Settings page tabs and form handling
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Main admin object
    var CustomReferralAdmin = {

        /**
         * Initialize admin functionality
         */
        init: function() {
            this.initGeneral();
            this.initReferralsPage();
            this.initSettingsPage();
        },

        /**
         * Initialize general admin functionality
         */
        initGeneral: function() {
            // Handle notice dismissal
            $(document).on('click', '.custom-cms-referral-notice .notice-dismiss', function() {
                var $notice = $(this).closest('.custom-cms-referral-notice');
                var noticeId = $notice.data('notice-id');

                if (noticeId) {
                    $.ajax({
                        url: custom_cms_referral_vars.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'custom_cms_referral_dismiss_notice',
                            notice_id: noticeId,
                            nonce: custom_cms_referral_vars.nonce
                        }
                    });
                }
            });
        },

        /**
         * Initialize referrals page functionality
         */
        initReferralsPage: function() {
            // Only run on the referrals page
            if (!$('.custom-cms-referal-referrals').length) {
                return;
            }

            // Reference to this object
            var self = this;

            // Handle modal opening
            $(document).on('click', '.edit-referral', function(e) {
                e.preventDefault();
                var referralId = $(this).data('id');
                self.openEditModal(referralId);
            });

            // Handle modal closing
            $(document).on('click', '.close-modal, #cancel-edit', function() {
                $('#edit-referral-modal').hide();
            });

            // Close modal when clicking outside
            $(document).on('click', '#edit-referral-modal', function(e) {
                if ($(e.target).is('#edit-referral-modal')) {
                    $('#edit-referral-modal').hide();
                }
            });

            // Handle referral saving
            $(document).on('click', '#save-referral', function() {
                self.saveReferral();
            });

            // Handle referral approval
            $(document).on('click', '.approve-referral', function(e) {
                e.preventDefault();
                var referralId = $(this).data('id');
                self.approveReferral(referralId);
            });

            // Handle referral deletion
            $(document).on('click', '.delete-referral', function(e) {
                e.preventDefault();
                var referralId = $(this).data('id');

                if (confirm(custom_cms_referral_vars.i18n.confirm_delete)) {
                    self.deleteReferral(referralId);
                }
            });

            // Handle bulk actions
            $(document).on('click', '#doaction, #doaction2', function(e) {
                var bulkAction = $(this).prev('select').val();

                if (bulkAction === '-1') {
                    return;
                }

                var selectedIds = [];
                $('input[name="referral[]"]:checked').each(function() {
                    selectedIds.push($(this).val());
                });

                if (selectedIds.length === 0) {
                    alert('Please select at least one referral.');
                    e.preventDefault();
                    return;
                }

                if (!confirm(custom_cms_referral_vars.i18n.confirm_bulk_action)) {
                    e.preventDefault();
                }
            });

            // Handle select all
            $(document).on('change', '#cb-select-all-1, #cb-select-all-2', function() {
                var isChecked = $(this).prop('checked');
                $('input[name="referral[]"]').prop('checked', isChecked);
            });
        },

        /**
         * Initialize settings page functionality
         */
        initSettingsPage: function() {
            // Only run on the settings page
            if (!$('.custom-cms-referal-settings').length) {
                return;
            }

            var self = this;

            // Handle tab switching
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();
                var href = $(this).attr('href');
                if (href && href.indexOf('tab=') !== -1) {
                    window.location.href = href;
                }
            });

            // Handle discount type change (avoid duplicate handlers)
            $('#discount_type').off('change.cms-referral').on('change.cms-referral', function() {
                var type = $(this).val();
                var symbol = type === 'percentage' ? '%' : '₹';
                $('#discount-symbol').text(symbol);
            });

            // Handle discount settings form submission (avoid duplicate handlers)
            $('#save_discount_settings').off('click.cms-referral').on('click.cms-referral', function(e) {
                e.preventDefault();

                // Validate form before submission
                if (!self.validateDiscountSettings()) {
                    return;
                }

                var discountType = $('#discount_type').val();
                var discountAmount = $('#discount_amount').val();
                var usageLimit = $('#usage_limit').val();

                $.ajax({
                    url: custom_cms_referral_vars.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'custom_cms_referral_save_discount_settings',
                        discount_type: discountType,
                        discount_amount: discountAmount,
                        usage_limit: usageLimit,
                        nonce: custom_cms_referral_vars.nonce
                    },
                    beforeSend: function() {
                        $('#save_discount_settings').prop('disabled', true);
                        $('#discount-settings-spinner').addClass('is-active');
                        self.hideSettingsMessage();
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            self.showSettingsMessage(response.data.message, 'success');
                        } else {
                            // Show error message
                            self.showSettingsMessage(response.data.message || custom_cms_referral_vars.i18n.error, 'error');
                        }
                    },
                    error: function() {
                        // Show error message
                        self.showSettingsMessage(custom_cms_referral_vars.i18n.error, 'error');
                    },
                    complete: function() {
                        $('#save_discount_settings').prop('disabled', false);
                        $('#discount-settings-spinner').removeClass('is-active');

                        // Auto-dismiss notice after 3 seconds
                        setTimeout(function() {
                            self.hideSettingsMessage();
                        }, 3000);
                    }
                });
            });

            // Handle general settings form submission (avoid duplicate handlers)
            $('#submit').off('click.cms-referral').on('click.cms-referral', function(e) {
                e.preventDefault();

                // Validate general settings before submission
                if (!self.validateGeneralSettings()) {
                    return;
                }

                var form = $(this).closest('form');
                var formData = form.serialize();

                $.ajax({
                    url: custom_cms_referral_vars.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'custom_cms_referral_save_general_settings',
                        form_data: formData,
                        nonce: custom_cms_referral_vars.nonce
                    },
                    beforeSend: function() {
                        $('#submit').prop('disabled', true).val('Saving...');
                        self.removeNotices();
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            self.showNotice(response.data.message, 'success');
                        } else {
                            // Show error message
                            self.showNotice(response.data.message || custom_cms_referral_vars.i18n.error, 'error');
                        }
                    },
                    error: function() {
                        // Show error message
                        self.showNotice(custom_cms_referral_vars.i18n.error, 'error');
                    },
                    complete: function() {
                        $('#submit').prop('disabled', false).val('Save Changes');

                        // Auto-dismiss notice after 3 seconds
                        setTimeout(function() {
                            self.removeNotices();
                        }, 3000);
                    }
                });
            });

            // Handle email settings save button (if exists)
            $('#save_email_settings').off('click.cms-referral').on('click.cms-referral', function(e) {
                e.preventDefault();

                // Show loading state
                var $button = $(this);
                var originalText = $button.text();
                $button.text('Saving...').prop('disabled', true);

                // Serialize form data
                var formData = $('#custom-cms-referral-settings-form').serialize();

                // Send AJAX request
                $.ajax({
                    url: custom_cms_referral_vars.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'custom_cms_referral_save_general_settings',
                        nonce: custom_cms_referral_vars.nonce,
                        form_data: formData
                    },
                    success: function(response) {
                        if (response.success) {
                            self.showNotice(response.data.message, 'success');
                        } else {
                            self.showNotice(response.data.message || custom_cms_referral_vars.i18n.error, 'error');
                        }
                    },
                    error: function() {
                        self.showNotice(custom_cms_referral_vars.i18n.error, 'error');
                    },
                    complete: function() {
                        // Reset button state
                        $button.text(originalText).prop('disabled', false);
                    }
                });
            });

            // Handle email template preview
            $('#preview_sharing_template').off('click.cms-referral').on('click.cms-referral', function(e) {
                e.preventDefault();
                self.previewEmailTemplate();
            });
        },

        /**
         * Open the edit referral modal and populate with data
         */
        openEditModal: function(referralId) {
            var self = this;

            // Fetch referral data via AJAX
            $.ajax({
                url: custom_cms_referral_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_get_referral',
                    referral_id: referralId,
                    nonce: custom_cms_referral_vars.nonce
                },
                beforeSend: function() {
                    // Show loading state
                    $('#edit-referral-modal .modal-body').html('<p>Loading...</p>');
                    $('#edit-referral-modal').show();
                },
                success: function(response) {
                    if (response.success && response.data) {
                        var data = response.data;

                        // Restore modal content
                        self.restoreModalContent();

                        // Populate form fields
                        $('#referral_id').val(data.id);
                        $('#referrer_name').val(data.referrer_name || '');
                        $('#referred_name').val(data.referred_name || '');
                        $('#referral_code').val(data.referral_code || '');
                        $('#course_name').val(data.course_name || '');
                        $('#status').val(data.status || 'pending');
                    } else {
                        alert('Failed to load referral data: ' + (response.data ? response.data.message : 'Unknown error'));
                        $('#edit-referral-modal').hide();
                    }
                },
                error: function() {
                    alert('Error loading referral data');
                    $('#edit-referral-modal').hide();
                }
            });
        },

        /**
         * Restore modal content after loading
         */
        restoreModalContent: function() {
            var modalContent = `
                <form id="edit-referral-form">
                    <input type="hidden" id="referral_id" name="referral_id" value="">
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="referrer_name">Referrer Name</label>
                            </th>
                            <td>
                                <input type="text" id="referrer_name" name="referrer_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="referred_name">Referred Name</label>
                            </th>
                            <td>
                                <input type="text" id="referred_name" name="referred_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="referral_code">Referral Code</label>
                            </th>
                            <td>
                                <input type="text" id="referral_code" name="referral_code" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="course_name">Course Name</label>
                            </th>
                            <td>
                                <input type="text" id="course_name" name="course_name" class="regular-text">
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="status">Status</label>
                            </th>
                            <td>
                                <select id="status" name="status">
                                    <option value="pending">Pending</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </td>
                        </tr>
                    </table>
                </form>
            `;
            $('#edit-referral-modal .modal-body').html(modalContent);
        },

        /**
         * Save referral data
         */
        saveReferral: function() {
            var self = this;
            var formData = {
                action: 'custom_cms_referral_update_referral',
                referral_id: $('#referral_id').val(),
                referrer_name: $('#referrer_name').val(),
                referred_name: $('#referred_name').val(),
                referral_code: $('#referral_code').val(),
                course_name: $('#course_name').val(),
                status: $('#status').val(),
                nonce: custom_cms_referral_vars.nonce
            };

            $.ajax({
                url: custom_cms_referral_vars.ajax_url,
                type: 'POST',
                data: formData,
                beforeSend: function() {
                    $('#save-referral').prop('disabled', true).text('Saving...');
                },
                success: function(response) {
                    if (response.success) {
                        alert('Referral updated successfully!');
                        $('#edit-referral-modal').hide();
                        // Reload the page to show updated data
                        window.location.reload();
                    } else {
                        alert('Failed to update referral: ' + (response.data ? response.data.message : 'Unknown error'));
                    }
                },
                error: function() {
                    alert('Error updating referral');
                },
                complete: function() {
                    $('#save-referral').prop('disabled', false).text('Update Referral');
                }
            });
        },

        /**
         * Approve a referral
         */
        approveReferral: function(referralId) {
            $.ajax({
                url: custom_cms_referral_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_update_referral',
                    referral_id: referralId,
                    status: 'completed',
                    nonce: custom_cms_referral_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        alert('Referral approved successfully!');
                        // Reload the page to show updated data
                        window.location.reload();
                    } else {
                        alert('Failed to approve referral: ' + (response.data ? response.data.message : 'Unknown error'));
                    }
                },
                error: function() {
                    alert('Error approving referral');
                }
            });
        },

        /**
         * Delete a referral
         */
        deleteReferral: function(referralId) {
            $.ajax({
                url: custom_cms_referral_vars.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_delete_referral',
                    referral_id: referralId,
                    nonce: custom_cms_referral_vars.nonce
                },
                success: function(response) {
                    if (response.success) {
                        alert('Referral deleted successfully!');
                        // Reload the page to show updated data
                        window.location.reload();
                    } else {
                        alert('Failed to delete referral: ' + (response.data ? response.data.message : 'Unknown error'));
                    }
                },
                error: function() {
                    alert('Error deleting referral');
                }
            });
        },

        /**
         * Validate discount settings form
         */
        validateDiscountSettings: function() {
            var discountAmount = parseFloat($('#discount_amount').val());
            var usageLimit = parseInt($('#usage_limit').val());
            var discountType = $('#discount_type').val();

            // Clear previous error styling
            $('.form-table input, .form-table select').removeClass('error');

            // Validate discount amount
            if (isNaN(discountAmount) || discountAmount <= 0) {
                $('#discount_amount').addClass('error').focus();
                this.showSettingsMessage('Please enter a valid discount amount greater than 0.', 'error');
                return false;
            }

            // Validate percentage range
            if (discountType === 'percentage' && discountAmount > 100) {
                $('#discount_amount').addClass('error').focus();
                this.showSettingsMessage('Percentage discount cannot exceed 100%.', 'error');
                return false;
            }

            // Validate usage limit
            if (isNaN(usageLimit) || usageLimit < 0) {
                $('#usage_limit').addClass('error').focus();
                this.showSettingsMessage('Please enter a valid usage limit (0 or greater).', 'error');
                return false;
            }

            return true;
        },

        /**
         * Validate general settings form
         */
        validateGeneralSettings: function() {
            var cookieDuration = parseInt($('#cookie_duration').val());
            var codePrefix = $('#code_prefix').val().trim();

            // Clear previous error styling
            $('.form-table input').removeClass('error');

            // Validate cookie duration
            if (isNaN(cookieDuration) || cookieDuration < 1 || cookieDuration > 365) {
                $('#cookie_duration').addClass('error').focus();
                this.showNotice('Cookie duration must be between 1 and 365 days.', 'error');
                return false;
            }

            // Validate code prefix (optional but if provided, should be reasonable)
            if (codePrefix.length > 10) {
                $('#code_prefix').addClass('error').focus();
                this.showNotice('Code prefix should not exceed 10 characters.', 'error');
                return false;
            }

            return true;
        },

        /**
         * Show settings message in the settings section
         */
        showSettingsMessage: function(message, type) {
            var $messageDiv = $('#settings-message');
            $messageDiv.removeClass('notice-success notice-error')
                      .addClass('notice-' + type)
                      .html('<p>' + message + '</p>')
                      .show();
        },

        /**
         * Hide settings message
         */
        hideSettingsMessage: function() {
            $('#settings-message').fadeOut();
        },

        /**
         * Show notice at the top of the page
         */
        showNotice: function(message, type) {
            var html = '<div class="notice notice-' + type + ' is-dismissible cms-referral-notice"><p>' +
                      message + '</p></div>';
            $('.custom-cms-referal-settings > h1').after(html);
        },

        /**
         * Remove all notices
         */
        removeNotices: function() {
            $('.cms-referral-notice').fadeOut(function() {
                $(this).remove();
            });
        },

        /**
         * Preview email template with sample data
         */
        previewEmailTemplate: function() {
            var subject = $('#sharing_subject').val() || 'Check out this course discount!';
            var template = $('#sharing_template').val() || 'Hi there!\n\nI wanted to share this great opportunity with you. Use my referral code {referral_code} to get a discount on courses.\n\nClick here to get started: {referral_link}\n\nThanks!\n{user_name}';

            // Sample data for preview
            var sampleData = {
                '{user_name}': 'John Doe',
                '{referral_code}': 'REF-ABC123',
                '{referral_link}': 'https://yoursite.com/courses?ref=REF-ABC123',
                '{site_name}': 'Your Learning Site'
            };

            // Replace variables with sample data
            var previewSubject = subject;
            var previewBody = template;

            for (var variable in sampleData) {
                var regex = new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                previewSubject = previewSubject.replace(regex, sampleData[variable]);
                previewBody = previewBody.replace(regex, sampleData[variable]);
            }

            // Format the preview content
            var previewHtml = '<div style="border: 1px solid #ccc; padding: 15px; background: white; margin-top: 10px;">';
            previewHtml += '<h4 style="margin-top: 0; color: #333;">Subject: ' + this.escapeHtml(previewSubject) + '</h4>';
            previewHtml += '<div style="border-top: 1px solid #eee; padding-top: 10px; white-space: pre-wrap; line-height: 1.5;">';
            previewHtml += this.escapeHtml(previewBody);
            previewHtml += '</div></div>';

            // Show the preview
            $('#sharing_preview_content').html(previewHtml);
            $('#sharing_template_preview').slideDown();
        },

        /**
         * Escape HTML to prevent XSS
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        CustomReferralAdmin.init();
    });

})(jQuery);
