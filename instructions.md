# MasterStudy Rewards System - Independent Plugin Instructions

## Reference Plugins
1. **Echo Rewards** (`/echo-rewards/`)
   - Reference for reward system implementation
   - UI/UX patterns for rewards and referrals
   - Integration points with payment systems

2. **Custom Referral Plugin** (`/custom-referal-plugin/`)
   - Existing referral code implementation
   - Database structure for referrals
   - User tracking and validation logic

3. **MasterStudy LMS** (`/masterstudy-lms-learning-management-system/`)
   - Core integration target
   - Order processing hooks
   - User management system

## Project Overview
Create a standalone rewards and referral system plugin that integrates directly with MasterStudy LMS. This plugin will provide a complete referral and rewards management system with its own database structure and processing logic, primarily managed through an admin panel with a single frontend shortcode.

### Key Differences from References
- **vs Echo Rewards**: No WooCommerce dependency, simplified architecture
- **vs Custom Referral Plugin**: More robust reward system, better UI/UX, comprehensive admin interface
- **Integration**: Direct MasterStudy LMS integration without intermediate layers

## Core Requirements

### 1. Database Structure
- Custom tables for referrals, rewards, and transactions
- Independent tracking system
- No reliance on WooCommerce tables
- Efficient data relationships

### 2. Core Features
1. Admin Panel (PRIORITY)
   - Comprehensive dashboard
   - Referral management system
   - Settings configuration
   - Reports and analytics

2. Frontend Integration (PRIORITY)
   - Single [mst_referral] shortcode implementation
   - Social sharing capabilities (Email, WhatsApp, etc.)
   - Responsive design for all devices
   - User-friendly interface

3. Referral System
   - Registration of referrers
   - Unique referral code generation
   - Referral tracking
   - Referral validation
   - Referral statistics

4. Rewards Management
   - Points system
   - Reward calculations
   - Reward redemption
   - Transaction history

5. Order Processing
   - Direct MasterStudy LMS integration
   - Custom payment processing
   - Discount application
   - Order tracking

4. User Interface
   - Comprehensive admin panel
   - Single shortcode for frontend
   - Customizable displays
   - User notifications

### 3. Technical Requirements
1. Must work independently of Echo Rewards
2. Must integrate directly with MasterStudy LMS
3. Must handle all payment processing
4. Must maintain data integrity
5. Must provide comprehensive logging
6. Must be highly customizable
7. Must include robust error handling

## File Structure

```
custom-cms-referal-plugin/
├── assets/
│   ├── css/
│   │   ├── admin.css         # Admin styles
│   │   └── public.css        # Frontend styles
│   └── js/
│       ├── admin.js          # Admin scripts
│       └── public.js         # Frontend scripts
│
├── templates/
│   ├── admin-dashboard.php   # Admin dashboard markup
│   ├── admin-referrals.php   # Referrals management markup
│   ├── admin-settings.php    # Settings page markup
│   └── referral-form.php     # Frontend referral form markup
│
├── database/                # Handles plugin database operations
├── activator.php            # Handles plugin activation
├── deactivator.php          # Handles plugin deactivation
└── custom-cms-referal.php   # Main plugin file (ALL core logic, admin, frontend, AJAX, shortcodes, etc.)
```

### Key Points
- All CSS/JS assets are in the `assets/` folder
- All admin/frontend templates are in the `templates/` folder
- All database-related files are in the `database/` folder
- `activator.php` and `deactivator.php` handle plugin setup/cleanup
- `custom-cms-referal.php` contains ALL main logic and code (admin, frontend, AJAX, shortcodes, etc.)


### Key Points of This Structure
- **All major logic is in `custom-cms-referal.php`** (referral, rewards, admin, frontend, AJAX, shortcodes, hooks, etc.)
- **Only assets (CSS/JS), activation/deactivation/uninstall handlers, and templates are separate**
- **No separate PHP include files for logic**
- **Templates are for markup only, not logic**

This approach makes debugging and maintenance centralized and straightforward, as all code logic lives in the main plugin file.

## Implementation Details (Based on Reference Analysis)

### Key Learning from References
1. **From Custom Referral Plugin**:
   - Simple but effective referral tracking
   - Good database structure for basic referrals
   - Needs enhancement for scalability

2. **From Echo Rewards**:
   - Robust reward system design
   - Complex but flexible architecture
   - Strong admin interface

3. **From MasterStudy LMS**:
   - Clean integration points
   - Well-documented hooks and filters
   - Modern codebase structure

### Database Structure
```sql
-- Core Tables (Expanded from Custom Referral Plugin)
mst_referrals
  - id, referrer_id, referrer_name, referred_id, referred_name
  - referral_code, course_name, order_id, status
  - created_at, updated_at

mst_rewards
  - id, user_id, points_balance, lifetime_points
  - last_updated, expiry_date

mst_transactions
  - id, user_id, points, transaction_type
  - reference_id, description, created_at

mst_coupons
  - id, code, discount_type, amount
  - usage_limit, usage_count, expiry_date
  - created_by, created_at

mst_settings
  - option_name, option_value, autoload

mst_logs
  - id, log_level, message, context
  - created_at, user_id
```

### Core Components

1. **Main Plugin File** (`custom-cms-referal.php`)
   - Contains ALL plugin logic and core functionality:
     - Referral and rewards logic
     - Admin panel logic
     - Frontend logic
     - Shortcodes and AJAX handlers
     - Database operations
     - Hooks and filters
     - Initialization and lifecycle

2. **Assets** (`assets/`)
   - CSS and JS files for admin and frontend

3. **Templates** (`templates/`)
   - Markup for admin dashboard, referrals, settings, and frontend forms

4. **Activation/Deactivation/Uninstall**
   - `activator.php`, `deactivator.php`, `uninstall.php` handle setup, cleanup, and removal

5. **Languages**
   - Translation files for localization

**Summary:**
- All major code is in the main plugin file for easy debugging and a centralized approach.
- Only assets, templates, and setup/cleanup handlers are outside the main file.
1. **MST_Rewards_Core** (Enhanced from Custom Referral Plugin)
   - Plugin initialization and lifecycle management
   - Component registration and dependency injection
   - Hook and filter registration
   - Error handling and logging system

2. **MST_Rewards_Referral** (Inspired by Custom Referral Plugin)
   - Referral code generation and validation
   - Referral tracking and attribution
   - Integration with user registration/checkout
   - Referral statistics and reporting

3. **MST_Rewards_Order** (Inspired by Echo Rewards)
   - Order processing and validation
   - Points calculation and distribution
   - Commission tracking
   - Refund handling
   - Payment handling
   - Discount application

4. MST_Rewards_User
   - User management
   - Points tracking
   - Reward distribution

5. MST_Rewards_Admin
   - Admin interface
   - Settings management
   - Reporting tools

### Shortcode System
Single shortcode: [mst_referral]
- Displays user's referral code
- Provides referral code entry form
- Shows validation feedback
- Customizable appearance

### Admin Panel
1. Dashboard
   - System overview
   - Quick statistics
   - Recent activity
   - Performance metrics

2. Referral Management
   - View all referrals
   - Filter and search
   - Export functionality
   - Referral analytics

3. Reward Settings
   - Point values
   - Reward rules
   - Distribution settings
   - Customization options

4. User Management
   - User rewards
   - Point adjustments
   - History tracking
   - User statistics

5. System Settings
   - General configuration
   - Display options
   - Integration settings
   - Security settings

6. Reports
   - Referral statistics
   - Reward distribution
   - Transaction history
   - Performance metrics

### User Experience
1. Frontend
   - Simple shortcode implementation
   - Clear validation feedback
   - Instant updates
   - Responsive design

2. Admin Interface
   - Intuitive dashboard
   - Comprehensive management tools
   - Detailed reporting
   - Easy configuration

## Development Guidelines

### Code Standards
1. Follow WordPress coding standards
2. Use clear, documented functions
3. Implement proper security measures
4. Maintain modular structure
5. Include comprehensive error handling

### Security Measures (Enhanced from References)
1. **Input Validation & Sanitization**
   - Strict type checking
   - Data format validation
   - HTML/JS sanitization

2. **Authentication & Authorization**
   - Nonce verification for all AJAX calls
   - Capability checks for all admin functions
   - Role-based access control

3. **Data Protection**
   - Prepared SQL statements
   - Data encryption for sensitive information
   - Secure session handling

4. **Audit & Monitoring**
   - Comprehensive activity logging
   - Security event monitoring
   - Regular security audits

### Performance Considerations
1. Optimize database queries
2. Implement caching where appropriate
3. Minimize external dependencies
4. Efficient data processing
5. Regular maintenance routines

## Future Considerations
1. API development
2. Additional payment gateways
3. Enhanced reporting
4. Advanced analytics
5. Mobile optimization
6. Multi-language support
7. Advanced customization options 