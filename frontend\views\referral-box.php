<?php
/**
 * Referral Box Template
 *
 * Displays the user's referral code with copy and share buttons.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>
<div class="custom-cms-referral-box" data-theme="<?php echo esc_attr( $atts['theme'] ); ?>">
    <div class="referral-box-header">
        <h3><?php _e( 'Share Your Referral Code', 'custom-cms-referal' ); ?></h3>
    </div>

    <div class="referral-box-content">
        <div class="referral-code-display">
            <span class="referral-code-label"><?php _e( 'Your Referral Code:', 'custom-cms-referal' ); ?></span>
            <div class="referral-code-container">
                <input type="text" class="referral-code" value="<?php echo esc_attr( $referral_code ); ?>" readonly />
                <button type="button" class="copy-code-btn" data-clipboard-text="<?php echo esc_attr( $referral_code ); ?>">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>

        <div class="referral-link-display">
            <span class="referral-link-label"><?php _e( 'Your Referral Link:', 'custom-cms-referal' ); ?></span>
            <div class="referral-link-container">
                <input type="text" class="referral-link" value="<?php echo esc_url( $referral_url ); ?>" readonly />
                <button type="button" class="copy-link-btn" data-clipboard-text="<?php echo esc_url( $referral_url ); ?>">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="referral-box-footer">
        <div class="referral-share-text">
            <?php _e( 'Share With:', 'custom-cms-referal' ); ?>
        </div>
        <div class="referral-share-buttons">
            <?php
            // Include email templates class if not already loaded
            if ( ! class_exists( 'Custom_CMS_Referral_Email_Templates' ) ) {
                require_once CUSTOM_CMS_REFERAL_PLUGIN_PATH . 'includes/class-email-templates.php';
            }

            // Get current user name for email template
            $current_user = wp_get_current_user();
            $user_name = $current_user->display_name ? $current_user->display_name : $current_user->user_login;

            // Generate email sharing URL using template
            $email_url = Custom_CMS_Referral_Email_Templates::get_sharing_mailto_url(
                $referral_code,
                $referral_url,
                $user_name
            );
            ?>
            <a href="<?php echo esc_url( $email_url ); ?>" class="share-btn email-share">
                <i class="fas fa-envelope"></i>
            </a>

            <a href="https://api.whatsapp.com/send?text=<?php echo urlencode( sprintf( __( 'Use my referral code %s to get a discount on courses. Click here: %s', 'custom-cms-referal' ), $referral_code, $referral_url ) ); ?>" target="_blank" class="share-btn whatsapp-share">
                <i class="fab fa-whatsapp"></i>
            </a>

            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode( $referral_url ); ?>" target="_blank" class="share-btn facebook-share">
                <i class="fab fa-facebook-f"></i>
            </a>

            <a href="https://twitter.com/intent/tweet?text=<?php echo urlencode( sprintf( __( 'Use my referral code %s to get a discount on courses.', 'custom-cms-referal' ), $referral_code ) ); ?>&url=<?php echo urlencode( $referral_url ); ?>" target="_blank" class="share-btn twitter-share">
                <i class="fab fa-twitter"></i>
            </a>
        </div>
    </div>
</div>

<div class="copy-success-message" style="display: none;">
    <?php _e( 'Copied to clipboard!', 'custom-cms-referal' ); ?>
</div>
