[2025-05-31 11:09:51] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:09:51] FRONTEND: Referral code: user11
[2025-05-31 11:09:51] FRONTEND: User ID: 63
[2025-05-31 11:09:51] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:09:51] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:09:51] VALIDATION: Input code: 'user11'
[2025-05-31 11:09:51] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:09:51] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:09:51] VALIDATION: Usage limit setting: 0
[2025-05-31 11:09:51] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:09:51] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:09:51] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:09:51] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:09:51] VALIDATION: Input code: 'user11'
[2025-05-31 11:09:51] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:09:51] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:09:51] VALIDATION: Usage limit setting: 0
[2025-05-31 11:09:51] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:09:51] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:09:51] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Order 49677 completed, triggering discount cleanup
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Order 49677 - User ID: 63
[2025-05-31 11:09:58] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:09:58] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:09:58] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:09:58] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:09:58] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:09:58] TRACKER: Order ID: 49677
[2025-05-31 11:09:58] TRACKER: Old Status: new
[2025-05-31 11:09:58] TRACKER: New Status: publish
[2025-05-31 11:09:58] TRACKER: No referral records found for order 49677
[2025-05-31 11:09:58] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:09:58] TRACKER: User ID: 63
[2025-05-31 11:09:58] TRACKER: Order ID: 49677
[2025-05-31 11:09:58] TRACKER: Payment Code: cash
[2025-05-31 11:09:58] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:09:58] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:09:58] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:09:58] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:09:51
        )

)

[2025-05-31 11:09:58] TRACKER: Found referral record: stdClass Object
(
    [id] => 8
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:09:51
)

[2025-05-31 11:09:58] TRACKER: Attempting to get user data...
[2025-05-31 11:09:58] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:09:58] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 11:09:58] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 11:09:58] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:09:58] TRACKER: Course name: full stack bundle
[2025-05-31 11:09:58] TRACKER: Original price total: 500
[2025-05-31 11:09:58] TRACKER: Discounted price total: 250
[2025-05-31 11:09:58] TRACKER: Total amount: 250
[2025-05-31 11:09:58] TRACKER: Found order status in posts table: publish
[2025-05-31 11:09:58] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:09:58] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:09:58] TRACKER: Calling insert_referral method...
[2025-05-31 11:09:58] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49677
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:09:58
    [updated_at] => 2025-05-31 11:09:58
)

[2025-05-31 11:09:58] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49677
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:09:58
    [updated_at] => 2025-05-31 11:09:58
)

[2025-05-31 11:09:58] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:09:58] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:09:58] TRACKER: Insert referral result: SUCCESS (ID: 15)
[2025-05-31 11:09:58] TRACKER: Referral record inserted successfully!
[2025-05-31 11:09:58] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:09:59] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:09:59] TRACKER: Awarding points to referrer...
[2025-05-31 11:09:59] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:09:59] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:09:59] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:09:59] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:28:11] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:28:11] FRONTEND: Referral code: user11
[2025-05-31 11:28:11] FRONTEND: User ID: 63
[2025-05-31 11:28:11] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:28:11] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:28:11] VALIDATION: Input code: 'user11'
[2025-05-31 11:28:11] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:28:11] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:28:11] VALIDATION: Usage limit setting: 0
[2025-05-31 11:28:11] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:28:11] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:28:11] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:28:11] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:28:11] VALIDATION: Input code: 'user11'
[2025-05-31 11:28:11] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:28:11] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:28:11] VALIDATION: Usage limit setting: 0
[2025-05-31 11:28:11] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:28:11] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:28:11] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Order 49678 completed, triggering discount cleanup
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Order 49678 - User ID: 63
[2025-05-31 11:28:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:28:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:28:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:28:17] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:28:17] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:28:17] TRACKER: Order ID: 49678
[2025-05-31 11:28:17] TRACKER: Old Status: new
[2025-05-31 11:28:17] TRACKER: New Status: publish
[2025-05-31 11:28:17] TRACKER: No referral records found for order 49678
[2025-05-31 11:28:17] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:28:17] TRACKER: User ID: 63
[2025-05-31 11:28:17] TRACKER: Order ID: 49678
[2025-05-31 11:28:17] TRACKER: Payment Code: cash
[2025-05-31 11:28:17] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49438
            [price] => 249.5
        )

)

[2025-05-31 11:28:17] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:28:17] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:28:17] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:28:11
        )

)

[2025-05-31 11:28:17] TRACKER: Found referral record: stdClass Object
(
    [id] => 9
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:28:11
)

[2025-05-31 11:28:17] TRACKER: Attempting to get user data...
[2025-05-31 11:28:17] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:28:17] TRACKER: Found stored original price for item 49438: 499
[2025-05-31 11:28:17] TRACKER: Item 49438 - Original: 499, Current (discounted): 249.5
[2025-05-31 11:28:17] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:28:17] TRACKER: Course name: HTML
[2025-05-31 11:28:17] TRACKER: Original price total: 499
[2025-05-31 11:28:17] TRACKER: Discounted price total: 249.5
[2025-05-31 11:28:17] TRACKER: Total amount: 249.5
[2025-05-31 11:28:17] TRACKER: Found order status in posts table: publish
[2025-05-31 11:28:17] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:28:17] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:28:17] TRACKER: Calling insert_referral method...
[2025-05-31 11:28:17] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49438
    [course_name] => HTML
    [order_id] => 49678
    [status] => completed
    [amount] => 249.5
    [original_price] => 499
    [discounted_price] => 249.5
    [points_awarded] => 24
    [created_at] => 2025-05-31 11:28:17
    [updated_at] => 2025-05-31 11:28:17
)

[2025-05-31 11:28:17] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49438
    [course_name] => HTML
    [order_id] => 49678
    [status] => completed
    [amount] => 249.5
    [original_price] => 499
    [discounted_price] => 249.5
    [points_awarded] => 24
    [created_at] => 2025-05-31 11:28:17
    [updated_at] => 2025-05-31 11:28:17
)

[2025-05-31 11:28:17] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:28:17] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:28:17] TRACKER: Insert referral result: SUCCESS (ID: 16)
[2025-05-31 11:28:17] TRACKER: Referral record inserted successfully!
[2025-05-31 11:28:17] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:28:17] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:28:17] TRACKER: Awarding points to referrer...
[2025-05-31 11:28:17] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:28:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:28:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:28:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:28:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:35:08] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:35:08] FRONTEND: Referral code: user11
[2025-05-31 11:35:08] FRONTEND: User ID: 63
[2025-05-31 11:35:08] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:35:08] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:35:08] VALIDATION: Input code: 'user11'
[2025-05-31 11:35:08] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:35:08] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:35:08] VALIDATION: Usage limit setting: 0
[2025-05-31 11:35:08] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:35:08] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:35:08] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:35:08] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:35:08] VALIDATION: Input code: 'user11'
[2025-05-31 11:35:08] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:35:08] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:35:08] VALIDATION: Usage limit setting: 0
[2025-05-31 11:35:08] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:35:08] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:35:08] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Order 49679 completed, triggering discount cleanup
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Order 49679 - User ID: 63
[2025-05-31 11:35:14] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:35:14] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:35:14] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:35:14] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:35:14] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:35:14] TRACKER: Order ID: 49679
[2025-05-31 11:35:14] TRACKER: Old Status: new
[2025-05-31 11:35:14] TRACKER: New Status: publish
[2025-05-31 11:35:14] TRACKER: No referral records found for order 49679
[2025-05-31 11:35:14] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:35:14] TRACKER: User ID: 63
[2025-05-31 11:35:14] TRACKER: Order ID: 49679
[2025-05-31 11:35:14] TRACKER: Payment Code: cash
[2025-05-31 11:35:14] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:35:14] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:35:14] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:35:14] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:35:08
        )

)

[2025-05-31 11:35:14] TRACKER: Found referral record: stdClass Object
(
    [id] => 10
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:35:08
)

[2025-05-31 11:35:14] TRACKER: Attempting to get user data...
[2025-05-31 11:35:14] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:35:14] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 11:35:14] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 11:35:14] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:35:14] TRACKER: Course name: full stack bundle
[2025-05-31 11:35:14] TRACKER: Original price total: 500
[2025-05-31 11:35:14] TRACKER: Discounted price total: 250
[2025-05-31 11:35:14] TRACKER: Total amount: 250
[2025-05-31 11:35:14] TRACKER: Found order status in posts table: publish
[2025-05-31 11:35:14] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:35:14] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:35:14] TRACKER: Calling insert_referral method...
[2025-05-31 11:35:14] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49679
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:35:14
    [updated_at] => 2025-05-31 11:35:14
)

[2025-05-31 11:35:14] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49679
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:35:14
    [updated_at] => 2025-05-31 11:35:14
)

[2025-05-31 11:35:14] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:35:14] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:35:14] TRACKER: Insert referral result: SUCCESS (ID: 17)
[2025-05-31 11:35:14] TRACKER: Referral record inserted successfully!
[2025-05-31 11:35:14] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:35:14] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:35:14] TRACKER: Awarding points to referrer...
[2025-05-31 11:35:14] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:35:14] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:35:14] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:35:14] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:35:14] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:40:03] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:40:03] FRONTEND: Referral code: user11
[2025-05-31 11:40:03] FRONTEND: User ID: 63
[2025-05-31 11:40:03] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:40:03] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:40:03] VALIDATION: Input code: 'user11'
[2025-05-31 11:40:03] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:40:03] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:40:03] VALIDATION: Usage limit setting: 0
[2025-05-31 11:40:03] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:40:03] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:40:03] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:40:03] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:40:03] VALIDATION: Input code: 'user11'
[2025-05-31 11:40:03] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:40:03] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:40:03] VALIDATION: Usage limit setting: 0
[2025-05-31 11:40:03] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:40:03] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:40:03] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Order 49680 completed, triggering discount cleanup
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Order 49680 - User ID: 63
[2025-05-31 11:40:10] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:40:10] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:40:10] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:40:10] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:40:10] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:40:10] TRACKER: Order ID: 49680
[2025-05-31 11:40:10] TRACKER: Old Status: new
[2025-05-31 11:40:10] TRACKER: New Status: publish
[2025-05-31 11:40:10] TRACKER: No referral records found for order 49680
[2025-05-31 11:40:10] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:40:10] TRACKER: User ID: 63
[2025-05-31 11:40:10] TRACKER: Order ID: 49680
[2025-05-31 11:40:10] TRACKER: Payment Code: cash
[2025-05-31 11:40:10] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:40:10] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:40:10] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:40:10] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:40:03
        )

)

[2025-05-31 11:40:10] TRACKER: Found referral record: stdClass Object
(
    [id] => 11
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:40:03
)

[2025-05-31 11:40:10] TRACKER: Attempting to get user data...
[2025-05-31 11:40:10] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:40:10] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 11:40:10] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 11:40:10] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:40:10] TRACKER: Course name: full stack bundle
[2025-05-31 11:40:10] TRACKER: Original price total: 500
[2025-05-31 11:40:10] TRACKER: Discounted price total: 250
[2025-05-31 11:40:10] TRACKER: Total amount: 250
[2025-05-31 11:40:10] TRACKER: Found order status in posts table: publish
[2025-05-31 11:40:10] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:40:10] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:40:10] TRACKER: Calling insert_referral method...
[2025-05-31 11:40:10] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49680
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:40:10
    [updated_at] => 2025-05-31 11:40:10
)

[2025-05-31 11:40:10] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49680
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:40:10
    [updated_at] => 2025-05-31 11:40:10
)

[2025-05-31 11:40:10] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:40:10] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:40:10] TRACKER: Insert referral result: SUCCESS (ID: 18)
[2025-05-31 11:40:10] TRACKER: Referral record inserted successfully!
[2025-05-31 11:40:10] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:40:10] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:40:10] TRACKER: Awarding points to referrer...
[2025-05-31 11:40:10] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:40:10] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:40:10] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:40:10] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:40:10] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:47:00] TRACKER: Fixed 6 records with invalid dates
[2025-05-31 11:48:25] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 1
    [referrer_name] => Test Referrer
    [referred_id] => 2
    [referred_name] => Test Referred
    [referral_code] => TEST123
    [course_id] => 100
    [course_name] => Test Course
    [order_id] => 999
    [status] => completed
    [amount] => 50
    [original_price] => 100
    [discounted_price] => 50
    [points_awarded] => 10
)

[2025-05-31 11:48:25] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 1
    [referrer_name] => Test Referrer
    [referred_id] => 2
    [referred_name] => Test Referred
    [referral_code] => TEST123
    [course_id] => 100
    [course_name] => Test Course
    [order_id] => 999
    [status] => completed
    [amount] => 50
    [original_price] => 100
    [discounted_price] => 50
    [points_awarded] => 10
    [created_at] => 2025-05-31 11:48:25
    [updated_at] => 2025-05-31 11:48:25
)

[2025-05-31 11:48:25] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:48:25] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:50:24] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:50:24] FRONTEND: Referral code: user11
[2025-05-31 11:50:24] FRONTEND: User ID: 63
[2025-05-31 11:50:24] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:50:24] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:50:24] VALIDATION: Input code: 'user11'
[2025-05-31 11:50:24] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:50:24] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:50:24] VALIDATION: Usage limit setting: 0
[2025-05-31 11:50:24] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:50:24] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:50:24] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:50:24] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:50:24] VALIDATION: Input code: 'user11'
[2025-05-31 11:50:24] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:50:24] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:50:24] VALIDATION: Usage limit setting: 0
[2025-05-31 11:50:24] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:50:24] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:50:24] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Order 49681 completed, triggering discount cleanup
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Order 49681 - User ID: 63
[2025-05-31 11:50:31] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:50:31] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:50:31] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:50:31] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:50:31] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:50:31] TRACKER: Order ID: 49681
[2025-05-31 11:50:31] TRACKER: Old Status: new
[2025-05-31 11:50:31] TRACKER: New Status: publish
[2025-05-31 11:50:31] TRACKER: No referral records found for order 49681
[2025-05-31 11:50:31] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:50:31] TRACKER: User ID: 63
[2025-05-31 11:50:31] TRACKER: Order ID: 49681
[2025-05-31 11:50:31] TRACKER: Payment Code: cash
[2025-05-31 11:50:31] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:50:31] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:50:31] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:50:31] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:40:03
        )

    [11] => stdClass Object
        (
            [id] => 12
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:50:24
        )

)

[2025-05-31 11:50:31] TRACKER: Found referral record: stdClass Object
(
    [id] => 12
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:50:24
)

[2025-05-31 11:50:31] TRACKER: Attempting to get user data...
[2025-05-31 11:50:31] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:50:31] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 11:50:31] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 11:50:31] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:50:31] TRACKER: Course name: full stack bundle
[2025-05-31 11:50:31] TRACKER: Original price total: 500
[2025-05-31 11:50:31] TRACKER: Discounted price total: 250
[2025-05-31 11:50:31] TRACKER: Total amount: 250
[2025-05-31 11:50:31] TRACKER: Found order status in posts table: publish
[2025-05-31 11:50:31] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:50:31] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:50:31] TRACKER: Calling insert_referral method...
[2025-05-31 11:50:31] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49681
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:50:31
    [updated_at] => 2025-05-31 11:50:31
)

[2025-05-31 11:50:31] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49681
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:50:31
    [updated_at] => 2025-05-31 11:50:31
)

[2025-05-31 11:50:31] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:50:31] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:50:31] TRACKER: Insert referral result: SUCCESS (ID: 20)
[2025-05-31 11:50:31] TRACKER: Referral record inserted successfully!
[2025-05-31 11:50:31] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:50:31] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:50:31] TRACKER: Awarding points to referrer...
[2025-05-31 11:50:31] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:50:31] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:50:31] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:50:31] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:50:31] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:33:10] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:33:10] DISCOUNT CLEANUP: Cleanup triggered with user_id: 1, cart_items: 1 items
[2025-05-31 12:33:10] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:33:10] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:33:10] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:33:10] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:33:10] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:36:23] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:36:23] DISCOUNT CLEANUP: Cleanup triggered with user_id: 1, cart_items: 1 items
[2025-05-31 12:36:23] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:36:23] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:36:23] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:36:23] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:36:23] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:42:07] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:42:08] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:42:35] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:42:54] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:42:55] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 12:42:55] FRONTEND: Referral code: user11
[2025-05-31 12:42:55] FRONTEND: User ID: 63
[2025-05-31 12:42:55] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 12:42:55] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 12:42:55] VALIDATION: Input code: 'user11'
[2025-05-31 12:42:55] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 12:42:55] VALIDATION: Current prefix: 'MST-'
[2025-05-31 12:42:55] VALIDATION: Usage limit setting: 0
[2025-05-31 12:42:55] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 12:42:55] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 12:42:55] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 12:42:55] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 12:42:55] VALIDATION: Input code: 'user11'
[2025-05-31 12:42:55] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 12:42:55] VALIDATION: Current prefix: 'MST-'
[2025-05-31 12:42:55] VALIDATION: Usage limit setting: 0
[2025-05-31 12:42:55] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 12:42:55] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 12:42:55] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 12:43:02] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Order 49682 completed, triggering discount cleanup
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Order 49682 - User ID: 63
[2025-05-31 12:43:03] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:43:03] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:43:03] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:43:03] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 12:43:03] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 12:43:03] TRACKER: Order ID: 49682
[2025-05-31 12:43:03] TRACKER: Old Status: new
[2025-05-31 12:43:03] TRACKER: New Status: publish
[2025-05-31 12:43:03] TRACKER: No referral records found for order 49682
[2025-05-31 12:43:03] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 12:43:03] TRACKER: User ID: 63
[2025-05-31 12:43:03] TRACKER: Order ID: 49682
[2025-05-31 12:43:03] TRACKER: Payment Code: cash
[2025-05-31 12:43:03] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 12:43:03] TRACKER: Looking for tracking record for user 63
[2025-05-31 12:43:03] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 12:43:03] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:40:03
        )

    [11] => stdClass Object
        (
            [id] => 12
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:50:24
        )

    [12] => stdClass Object
        (
            [id] => 13
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 12:42:55
        )

)

[2025-05-31 12:43:03] TRACKER: Found referral record: stdClass Object
(
    [id] => 13
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 12:42:55
)

[2025-05-31 12:43:03] TRACKER: Attempting to get user data...
[2025-05-31 12:43:03] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 12:43:03] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 12:43:03] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 12:43:03] TRACKER: Preparing referral data for insertion...
[2025-05-31 12:43:03] TRACKER: Course name: full stack bundle
[2025-05-31 12:43:03] TRACKER: Original price total: 500
[2025-05-31 12:43:03] TRACKER: Discounted price total: 250
[2025-05-31 12:43:03] TRACKER: Total amount: 250
[2025-05-31 12:43:03] TRACKER: Found order status in posts table: publish
[2025-05-31 12:43:03] TRACKER: Actual WordPress order status: publish
[2025-05-31 12:43:03] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 12:43:03] TRACKER: Calling insert_referral method...
[2025-05-31 12:43:03] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49682
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 12:43:03
    [updated_at] => 2025-05-31 12:43:03
)

[2025-05-31 12:43:03] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49682
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 12:43:03
    [updated_at] => 2025-05-31 12:43:03
)

[2025-05-31 12:43:03] TRACKER: Target table: wp_mst_referrals
[2025-05-31 12:43:03] TRACKER: Database insert result: SUCCESS
[2025-05-31 12:43:03] TRACKER: Insert referral result: SUCCESS (ID: 21)
[2025-05-31 12:43:03] TRACKER: Referral record inserted successfully!
[2025-05-31 12:43:03] TRACKER: Order is completed - updating temp record status...
[2025-05-31 12:43:03] TRACKER: Temp record update result: SUCCESS
[2025-05-31 12:43:03] TRACKER: Awarding points to referrer...
[2025-05-31 12:43:03] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 12:43:03] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:43:03] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:43:03] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:43:03] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:43:10] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:44:14] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:45:15] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:47:16] TRACKER: MasterStudy LMS order status synchronization hooks registered
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Order 49683 completed, triggering discount cleanup
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Order 49683 - User ID: 1
[2025-05-31 12:48:12] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Cleanup triggered with user_id: 1, cart_items: NULL
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:48:12] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:48:12] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:48:12] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:49:17] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:51:18] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Order 49684 completed, triggering discount cleanup
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Order 49684 - User ID: 1
[2025-05-31 12:51:33] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Cleanup triggered with user_id: 1, cart_items: NULL
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:51:33] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:51:33] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:51:33] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:52:38] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:52:39] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:52:39] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:52:46] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:52:56] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:52:56] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 12:52:56] FRONTEND: Referral code: user11
[2025-05-31 12:52:56] FRONTEND: User ID: 63
[2025-05-31 12:52:56] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 12:52:56] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 12:52:56] VALIDATION: Input code: 'user11'
[2025-05-31 12:52:56] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 12:52:56] VALIDATION: Current prefix: 'MST-'
[2025-05-31 12:52:56] VALIDATION: Usage limit setting: 0
[2025-05-31 12:52:56] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 12:52:56] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 12:52:56] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 12:52:56] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 12:52:56] VALIDATION: Input code: 'user11'
[2025-05-31 12:52:56] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 12:52:56] VALIDATION: Current prefix: 'MST-'
[2025-05-31 12:52:56] VALIDATION: Usage limit setting: 0
[2025-05-31 12:52:56] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 12:52:56] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 12:52:56] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 12:53:04] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Order 49685 completed, triggering discount cleanup
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Order 49685 - User ID: 63
[2025-05-31 12:53:05] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:53:05] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:53:05] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 12:53:05] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 12:53:05] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 12:53:05] TRACKER: Order ID: 49685
[2025-05-31 12:53:05] TRACKER: Old Status: new
[2025-05-31 12:53:05] TRACKER: New Status: publish
[2025-05-31 12:53:05] TRACKER: No referral records found for order 49685
[2025-05-31 12:53:05] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 12:53:05] TRACKER: User ID: 63
[2025-05-31 12:53:05] TRACKER: Order ID: 49685
[2025-05-31 12:53:05] TRACKER: Payment Code: cash
[2025-05-31 12:53:05] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 12:53:05] TRACKER: Looking for tracking record for user 63
[2025-05-31 12:53:05] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 12:53:05] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:40:03
        )

    [11] => stdClass Object
        (
            [id] => 12
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:50:24
        )

    [12] => stdClass Object
        (
            [id] => 13
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 12:42:55
        )

    [13] => stdClass Object
        (
            [id] => 14
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 12:52:56
        )

)

[2025-05-31 12:53:05] TRACKER: Found referral record: stdClass Object
(
    [id] => 14
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 12:52:56
)

[2025-05-31 12:53:05] TRACKER: Attempting to get user data...
[2025-05-31 12:53:05] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 12:53:05] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 12:53:05] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 12:53:05] TRACKER: Preparing referral data for insertion...
[2025-05-31 12:53:05] TRACKER: Course name: full stack bundle
[2025-05-31 12:53:05] TRACKER: Original price total: 500
[2025-05-31 12:53:05] TRACKER: Discounted price total: 250
[2025-05-31 12:53:05] TRACKER: Total amount: 250
[2025-05-31 12:53:05] TRACKER: Found order status in posts table: publish
[2025-05-31 12:53:05] TRACKER: Actual WordPress order status: publish
[2025-05-31 12:53:05] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 12:53:05] TRACKER: Calling insert_referral method...
[2025-05-31 12:53:05] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49685
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 12:53:05
    [updated_at] => 2025-05-31 12:53:05
)

[2025-05-31 12:53:05] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49685
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 12:53:05
    [updated_at] => 2025-05-31 12:53:05
)

[2025-05-31 12:53:05] TRACKER: Target table: wp_mst_referrals
[2025-05-31 12:53:05] TRACKER: Database insert result: SUCCESS
[2025-05-31 12:53:05] TRACKER: Insert referral result: SUCCESS (ID: 22)
[2025-05-31 12:53:05] TRACKER: Referral record inserted successfully!
[2025-05-31 12:53:05] TRACKER: Order is completed - updating temp record status...
[2025-05-31 12:53:05] TRACKER: Temp record update result: SUCCESS
[2025-05-31 12:53:05] TRACKER: Awarding points to referrer...
[2025-05-31 12:53:05] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 12:53:05] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 12:53:05] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 12:53:05] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 12:53:05] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
