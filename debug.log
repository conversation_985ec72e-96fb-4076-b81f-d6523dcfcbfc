[2025-05-31 11:09:51] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:09:51] FRONTEND: Referral code: user11
[2025-05-31 11:09:51] FRONTEND: User ID: 63
[2025-05-31 11:09:51] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:09:51] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:09:51] VALIDATION: Input code: 'user11'
[2025-05-31 11:09:51] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:09:51] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:09:51] VALIDATION: Usage limit setting: 0
[2025-05-31 11:09:51] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:09:51] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:09:51] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:09:51] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:09:51] VALIDATION: Input code: 'user11'
[2025-05-31 11:09:51] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:09:51] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:09:51] VALIDATION: Usage limit setting: 0
[2025-05-31 11:09:51] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:09:51] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:09:51] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Order 49677 completed, triggering discount cleanup
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Order 49677 - User ID: 63
[2025-05-31 11:09:58] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:09:58] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:09:58] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:09:58] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:09:58] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:09:58] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:09:58] TRACKER: Order ID: 49677
[2025-05-31 11:09:58] TRACKER: Old Status: new
[2025-05-31 11:09:58] TRACKER: New Status: publish
[2025-05-31 11:09:58] TRACKER: No referral records found for order 49677
[2025-05-31 11:09:58] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:09:58] TRACKER: User ID: 63
[2025-05-31 11:09:58] TRACKER: Order ID: 49677
[2025-05-31 11:09:58] TRACKER: Payment Code: cash
[2025-05-31 11:09:58] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:09:58] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:09:58] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:09:58] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:09:51
        )

)

[2025-05-31 11:09:58] TRACKER: Found referral record: stdClass Object
(
    [id] => 8
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:09:51
)

[2025-05-31 11:09:58] TRACKER: Attempting to get user data...
[2025-05-31 11:09:58] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:09:58] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 11:09:58] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 11:09:58] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:09:58] TRACKER: Course name: full stack bundle
[2025-05-31 11:09:58] TRACKER: Original price total: 500
[2025-05-31 11:09:58] TRACKER: Discounted price total: 250
[2025-05-31 11:09:58] TRACKER: Total amount: 250
[2025-05-31 11:09:58] TRACKER: Found order status in posts table: publish
[2025-05-31 11:09:58] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:09:58] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:09:58] TRACKER: Calling insert_referral method...
[2025-05-31 11:09:58] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49677
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:09:58
    [updated_at] => 2025-05-31 11:09:58
)

[2025-05-31 11:09:58] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49677
    [status] => completed
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:09:58
    [updated_at] => 2025-05-31 11:09:58
)

[2025-05-31 11:09:58] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:09:58] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:09:58] TRACKER: Insert referral result: SUCCESS (ID: 15)
[2025-05-31 11:09:58] TRACKER: Referral record inserted successfully!
[2025-05-31 11:09:58] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:09:59] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:09:59] TRACKER: Awarding points to referrer...
[2025-05-31 11:09:59] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:09:59] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:09:59] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:09:59] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:09:59] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
