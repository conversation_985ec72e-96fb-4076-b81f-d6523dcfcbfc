=== CMS REFERRAL DEBUG LOG ===
Started: 2024-12-19 12:00:00

This file will track all referral system activities for debugging purposes.

=== DEBUG LOGGING SYSTEM INITIALIZED ===
- Frontend logging: ENABLED
- Tracker logging: ENABLED
- All referral code applications will be logged
- All order completion hooks will be logged
- All tracking record operations will be logged

=== READY FOR TESTING ===
[2025-05-31 09:32:42] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 09:32:42] FRONTEND: Referral code: user11
[2025-05-31 09:32:42] FRONTEND: User ID: 63
[2025-05-31 09:32:42] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Order 49669 completed, triggering discount cleanup
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Order 49669 - User ID: 63
[2025-05-31 09:32:48] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 09:32:48] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 09:32:48] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 09:32:48] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 09:32:48] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 09:32:48] TRACKER: Order ID: 49669
[2025-05-31 09:32:48] TRACKER: Old Status: new
[2025-05-31 09:32:48] TRACKER: New Status: publish
[2025-05-31 09:32:48] TRACKER: No referral records found for order 49669
[2025-05-31 09:32:48] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 09:32:48] TRACKER: User ID: 63
[2025-05-31 09:32:48] TRACKER: Order ID: 49669
[2025-05-31 09:32:48] TRACKER: Payment Code: cash
[2025-05-31 09:32:48] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 09:32:48] TRACKER: Looking for tracking record for user 63
[2025-05-31 09:32:48] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 09:32:48] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 09:32:42
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

)

[2025-05-31 09:32:48] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 09:32:42
)

[2025-05-31 09:32:48] TRACKER: Attempting to get user data...
[2025-05-31 09:32:48] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 09:32:48] TRACKER: Preparing referral data for insertion...
[2025-05-31 09:32:48] TRACKER: Course name: full stack bundle
[2025-05-31 09:32:48] TRACKER: Total amount: 250
[2025-05-31 09:32:48] TRACKER: Found order status in posts table: publish
[2025-05-31 09:32:48] TRACKER: Actual WordPress order status: publish
[2025-05-31 09:32:48] TRACKER: Setting referral status to: pending
[2025-05-31 09:32:48] TRACKER: Calling insert_referral method...
[2025-05-31 09:32:48] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49669
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 09:32:48
    [updated_at] => 2025-05-31 09:32:48
)

[2025-05-31 09:32:48] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49669
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 09:32:48
    [updated_at] => 2025-05-31 09:32:48
)

[2025-05-31 09:32:48] TRACKER: Target table: wp_mst_referrals
[2025-05-31 09:32:48] TRACKER: Database insert result: SUCCESS
[2025-05-31 09:32:48] TRACKER: Insert referral result: SUCCESS (ID: 7)
[2025-05-31 09:32:48] TRACKER: Referral record inserted successfully!
[2025-05-31 09:32:48] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 09:32:48] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 09:32:48] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 09:32:48] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 09:32:48] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 09:32:48] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:12:04] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:12:04] FRONTEND: Referral code: user11
[2025-05-31 10:12:04] FRONTEND: User ID: 63
[2025-05-31 10:12:04] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 10:12:57] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:12:57] FRONTEND: Referral code: user11
[2025-05-31 10:12:57] FRONTEND: User ID: 63
[2025-05-31 10:12:57] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Order 49670 completed, triggering discount cleanup
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Order 49670 - User ID: 63
[2025-05-31 10:13:05] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 10:13:05] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:13:05] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:13:05] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:13:05] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 10:13:05] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 10:13:05] TRACKER: Order ID: 49670
[2025-05-31 10:13:05] TRACKER: Old Status: new
[2025-05-31 10:13:05] TRACKER: New Status: publish
[2025-05-31 10:13:05] TRACKER: No referral records found for order 49670
[2025-05-31 10:13:05] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 10:13:05] TRACKER: User ID: 63
[2025-05-31 10:13:05] TRACKER: Order ID: 49670
[2025-05-31 10:13:05] TRACKER: Payment Code: cash
[2025-05-31 10:13:05] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 10:13:05] TRACKER: Looking for tracking record for user 63
[2025-05-31 10:13:05] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 10:13:05] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 10:12:57
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

)

[2025-05-31 10:13:05] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 10:12:57
)

[2025-05-31 10:13:05] TRACKER: Attempting to get user data...
[2025-05-31 10:13:05] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 10:13:05] TRACKER: Preparing referral data for insertion...
[2025-05-31 10:13:05] TRACKER: Course name: full stack bundle
[2025-05-31 10:13:05] TRACKER: Total amount: 250
[2025-05-31 10:13:05] TRACKER: Found order status in posts table: publish
[2025-05-31 10:13:05] TRACKER: Actual WordPress order status: publish
[2025-05-31 10:13:05] TRACKER: Setting referral status to: pending
[2025-05-31 10:13:05] TRACKER: Calling insert_referral method...
[2025-05-31 10:13:05] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49670
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:13:05
    [updated_at] => 2025-05-31 10:13:05
)

[2025-05-31 10:13:05] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49670
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:13:05
    [updated_at] => 2025-05-31 10:13:05
)

[2025-05-31 10:13:05] TRACKER: Target table: wp_mst_referrals
[2025-05-31 10:13:05] TRACKER: Database insert result: SUCCESS
[2025-05-31 10:13:05] TRACKER: Insert referral result: SUCCESS (ID: 8)
[2025-05-31 10:13:05] TRACKER: Referral record inserted successfully!
[2025-05-31 10:13:05] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 10:13:05] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 10:13:06] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:13:06] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 10:13:06] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 10:13:06] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:13:06] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:13:06] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:13:06] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
