[2025-05-31 11:01:33] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 11:01:33] FRONTEND: Referral code: user11
[2025-05-31 11:01:33] FRONTEND: User ID: 63
[2025-05-31 11:01:33] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 11:01:33] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:01:33] VALIDATION: Input code: 'user11'
[2025-05-31 11:01:33] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:01:33] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:01:33] VALIDATION: Usage limit setting: 0
[2025-05-31 11:01:33] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:01:33] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 11:01:33] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 11:01:33] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 11:01:33] VALIDATION: Input code: 'user11'
[2025-05-31 11:01:33] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 11:01:33] VALIDATION: Current prefix: 'MST-'
[2025-05-31 11:01:33] VALIDATION: Usage limit setting: 0
[2025-05-31 11:01:33] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 11:01:33] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 11:01:33] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Order 49676 completed, triggering discount cleanup
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Order 49676 - User ID: 63
[2025-05-31 11:01:41] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:01:41] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:01:41] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 11:01:41] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 11:01:41] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 11:01:41] TRACKER: Order ID: 49676
[2025-05-31 11:01:41] TRACKER: Old Status: new
[2025-05-31 11:01:41] TRACKER: New Status: publish
[2025-05-31 11:01:41] TRACKER: No referral records found for order 49676
[2025-05-31 11:01:41] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 11:01:41] TRACKER: User ID: 63
[2025-05-31 11:01:41] TRACKER: Order ID: 49676
[2025-05-31 11:01:41] TRACKER: Payment Code: cash
[2025-05-31 11:01:41] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 11:01:41] TRACKER: Looking for tracking record for user 63
[2025-05-31 11:01:41] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 11:01:41] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 11:01:33
        )

)

[2025-05-31 11:01:41] TRACKER: Found referral record: stdClass Object
(
    [id] => 7
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 11:01:33
)

[2025-05-31 11:01:41] TRACKER: Attempting to get user data...
[2025-05-31 11:01:41] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 11:01:41] TRACKER: Price calculation - Original: 250, Discount: 50%, Discounted: 125
[2025-05-31 11:01:41] TRACKER: Preparing referral data for insertion...
[2025-05-31 11:01:41] TRACKER: Course name: full stack bundle
[2025-05-31 11:01:41] TRACKER: Original price total: 250
[2025-05-31 11:01:41] TRACKER: Discounted price total: 125
[2025-05-31 11:01:41] TRACKER: Total amount: 250
[2025-05-31 11:01:41] TRACKER: Found order status in posts table: publish
[2025-05-31 11:01:41] TRACKER: Actual WordPress order status: publish
[2025-05-31 11:01:41] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 11:01:41] TRACKER: Calling insert_referral method...
[2025-05-31 11:01:41] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49676
    [status] => completed
    [amount] => 250
    [original_price] => 250
    [discounted_price] => 125
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:01:41
    [updated_at] => 2025-05-31 11:01:41
)

[2025-05-31 11:01:41] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49676
    [status] => completed
    [amount] => 250
    [original_price] => 250
    [discounted_price] => 125
    [points_awarded] => 25
    [created_at] => 2025-05-31 11:01:41
    [updated_at] => 2025-05-31 11:01:41
)

[2025-05-31 11:01:41] TRACKER: Target table: wp_mst_referrals
[2025-05-31 11:01:41] TRACKER: Database insert result: SUCCESS
[2025-05-31 11:01:41] TRACKER: Insert referral result: SUCCESS (ID: 14)
[2025-05-31 11:01:41] TRACKER: Referral record inserted successfully!
[2025-05-31 11:01:41] TRACKER: Order is completed - updating temp record status...
[2025-05-31 11:01:41] TRACKER: Temp record update result: SUCCESS
[2025-05-31 11:01:41] TRACKER: Awarding points to referrer...
[2025-05-31 11:01:41] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 11:01:41] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 11:01:41] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 11:01:41] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 11:01:41] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
