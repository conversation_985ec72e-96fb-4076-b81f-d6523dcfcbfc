=== CMS REFERRAL DEBUG LOG ===
Started: 2024-12-19 12:00:00

This file will track all referral system activities for debugging purposes.

=== DEBUG LOGGING SYSTEM INITIALIZED ===
- Frontend logging: ENABLED
- Tracker logging: ENABLED
- All referral code applications will be logged
- All order completion hooks will be logged
- All tracking record operations will be logged

=== READY FOR TESTING USAGE LIMIT FUNCTIONALITY ===
[2025-05-31 10:22:11] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:22:11] FRONTEND: Referral code: user11
[2025-05-31 10:22:11] FRONTEND: User ID: 63
[2025-05-31 10:22:11] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:22:11] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:22:11] VALIDATION: Input code: 'user11'
[2025-05-31 10:22:11] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:22:11] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:22:11] VALIDATION: Usage limit setting: 1
[2025-05-31 10:22:11] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:22:11] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:22:11] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:22:11] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 10:22:11] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 10:22:11] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:22:11] VALIDATION: Input code: 'user11'
[2025-05-31 10:22:11] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:22:11] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:22:11] VALIDATION: Usage limit setting: 1
[2025-05-31 10:22:11] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:22:11] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:22:11] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:22:11] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Order 49671 completed, triggering discount cleanup
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Order 49671 - User ID: 63
[2025-05-31 10:22:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:22:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:22:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:22:17] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 10:22:17] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 10:22:17] TRACKER: Order ID: 49671
[2025-05-31 10:22:17] TRACKER: Old Status: new
[2025-05-31 10:22:17] TRACKER: New Status: publish
[2025-05-31 10:22:17] TRACKER: No referral records found for order 49671
[2025-05-31 10:22:17] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 10:22:17] TRACKER: User ID: 63
[2025-05-31 10:22:17] TRACKER: Order ID: 49671
[2025-05-31 10:22:17] TRACKER: Payment Code: cash
[2025-05-31 10:22:17] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 10:22:17] TRACKER: Looking for tracking record for user 63
[2025-05-31 10:22:17] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 10:22:17] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 10:22:11
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

)

[2025-05-31 10:22:17] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 10:22:11
)

[2025-05-31 10:22:17] TRACKER: Attempting to get user data...
[2025-05-31 10:22:17] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 10:22:17] TRACKER: Preparing referral data for insertion...
[2025-05-31 10:22:17] TRACKER: Course name: full stack bundle
[2025-05-31 10:22:17] TRACKER: Total amount: 250
[2025-05-31 10:22:17] TRACKER: Found order status in posts table: publish
[2025-05-31 10:22:17] TRACKER: Actual WordPress order status: publish
[2025-05-31 10:22:17] TRACKER: Setting referral status to: pending
[2025-05-31 10:22:17] TRACKER: Calling insert_referral method...
[2025-05-31 10:22:17] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49671
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:22:17
    [updated_at] => 2025-05-31 10:22:17
)

[2025-05-31 10:22:17] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49671
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:22:17
    [updated_at] => 2025-05-31 10:22:17
)

[2025-05-31 10:22:17] TRACKER: Target table: wp_mst_referrals
[2025-05-31 10:22:17] TRACKER: Database insert result: SUCCESS
[2025-05-31 10:22:17] TRACKER: Insert referral result: SUCCESS (ID: 9)
[2025-05-31 10:22:17] TRACKER: Referral record inserted successfully!
[2025-05-31 10:22:17] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 10:22:17] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 10:22:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:22:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:22:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:22:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:23:10] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:23:10] FRONTEND: Referral code: user11
[2025-05-31 10:23:10] FRONTEND: User ID: 63
[2025-05-31 10:23:10] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:23:10] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:23:10] VALIDATION: Input code: 'user11'
[2025-05-31 10:23:10] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:23:10] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:23:10] VALIDATION: Usage limit setting: 1
[2025-05-31 10:23:10] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:23:10] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:23:10] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:23:10] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 10:23:10] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 10:23:10] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:23:10] VALIDATION: Input code: 'user11'
[2025-05-31 10:23:10] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:23:10] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:23:10] VALIDATION: Usage limit setting: 1
[2025-05-31 10:23:10] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:23:10] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:23:10] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:23:10] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Order 49672 completed, triggering discount cleanup
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Order 49672 - User ID: 63
[2025-05-31 10:23:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:23:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:23:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:23:17] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 10:23:17] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 10:23:17] TRACKER: Order ID: 49672
[2025-05-31 10:23:17] TRACKER: Old Status: new
[2025-05-31 10:23:17] TRACKER: New Status: publish
[2025-05-31 10:23:17] TRACKER: No referral records found for order 49672
[2025-05-31 10:23:17] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 10:23:17] TRACKER: User ID: 63
[2025-05-31 10:23:17] TRACKER: Order ID: 49672
[2025-05-31 10:23:17] TRACKER: Payment Code: cash
[2025-05-31 10:23:17] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 10:23:17] TRACKER: Looking for tracking record for user 63
[2025-05-31 10:23:17] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 10:23:17] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 10:23:10
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

)

[2025-05-31 10:23:17] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 10:23:10
)

[2025-05-31 10:23:17] TRACKER: Attempting to get user data...
[2025-05-31 10:23:17] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 10:23:17] TRACKER: Preparing referral data for insertion...
[2025-05-31 10:23:17] TRACKER: Course name: full stack bundle
[2025-05-31 10:23:17] TRACKER: Total amount: 250
[2025-05-31 10:23:17] TRACKER: Found order status in posts table: publish
[2025-05-31 10:23:17] TRACKER: Actual WordPress order status: publish
[2025-05-31 10:23:17] TRACKER: Setting referral status to: pending
[2025-05-31 10:23:17] TRACKER: Calling insert_referral method...
[2025-05-31 10:23:17] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49672
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:23:17
    [updated_at] => 2025-05-31 10:23:17
)

[2025-05-31 10:23:17] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49672
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:23:17
    [updated_at] => 2025-05-31 10:23:17
)

[2025-05-31 10:23:17] TRACKER: Target table: wp_mst_referrals
[2025-05-31 10:23:17] TRACKER: Database insert result: SUCCESS
[2025-05-31 10:23:17] TRACKER: Insert referral result: SUCCESS (ID: 10)
[2025-05-31 10:23:17] TRACKER: Referral record inserted successfully!
[2025-05-31 10:23:17] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 10:23:17] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 10:23:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:23:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:23:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:23:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
