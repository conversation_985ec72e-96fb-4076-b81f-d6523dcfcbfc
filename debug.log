=== CMS REFERRAL DEBUG LOG ===
Started: 2024-12-19 12:00:00

This file will track all referral system activities for debugging purposes.

=== DEBUG LOGGING SYSTEM INITIALIZED ===
- Frontend logging: ENABLED
- Tracker logging: ENABLED
- All referral code applications will be logged
- All order completion hooks will be logged
- All tracking record operations will be logged

=== TESTING USAGE LIMIT FIX ===
Fixed: MasterStudy LMS 'publish' status now correctly maps to 'completed' referral status
[2025-05-31 10:29:28] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:29:28] FRONTEND: Referral code: user11
[2025-05-31 10:29:28] FRONTEND: User ID: 63
[2025-05-31 10:29:28] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:29:28] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:29:28] VALIDATION: Input code: 'user11'
[2025-05-31 10:29:28] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:29:28] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:29:28] VALIDATION: Usage limit setting: 1
[2025-05-31 10:29:28] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:29:28] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:29:28] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:29:28] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 10:29:28] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 10:29:28] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:29:28] VALIDATION: Input code: 'user11'
[2025-05-31 10:29:28] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:29:28] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:29:28] VALIDATION: Usage limit setting: 1
[2025-05-31 10:29:28] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:29:28] VALIDATION: Current usage count: 0 out of 1
[2025-05-31 10:29:28] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 0/1
[2025-05-31 10:29:28] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Order 49673 completed, triggering discount cleanup
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Order 49673 - User ID: 63
[2025-05-31 10:29:34] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:29:34] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:29:34] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:29:34] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 10:29:34] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 10:29:34] TRACKER: Order ID: 49673
[2025-05-31 10:29:34] TRACKER: Old Status: new
[2025-05-31 10:29:34] TRACKER: New Status: publish
[2025-05-31 10:29:34] TRACKER: No referral records found for order 49673
[2025-05-31 10:29:34] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 10:29:34] TRACKER: User ID: 63
[2025-05-31 10:29:34] TRACKER: Order ID: 49673
[2025-05-31 10:29:34] TRACKER: Payment Code: cash
[2025-05-31 10:29:34] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 10:29:34] TRACKER: Looking for tracking record for user 63
[2025-05-31 10:29:34] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 10:29:34] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

)

[2025-05-31 10:29:34] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 10:29:28
)

[2025-05-31 10:29:34] TRACKER: Attempting to get user data...
[2025-05-31 10:29:34] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 10:29:34] TRACKER: Preparing referral data for insertion...
[2025-05-31 10:29:34] TRACKER: Course name: full stack bundle
[2025-05-31 10:29:34] TRACKER: Total amount: 250
[2025-05-31 10:29:34] TRACKER: Found order status in posts table: publish
[2025-05-31 10:29:34] TRACKER: Actual WordPress order status: publish
[2025-05-31 10:29:34] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 10:29:34] TRACKER: Calling insert_referral method...
[2025-05-31 10:29:34] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49673
    [status] => completed
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:29:34
    [updated_at] => 2025-05-31 10:29:34
)

[2025-05-31 10:29:34] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49673
    [status] => completed
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:29:34
    [updated_at] => 2025-05-31 10:29:34
)

[2025-05-31 10:29:34] TRACKER: Target table: wp_mst_referrals
[2025-05-31 10:29:34] TRACKER: Database insert result: SUCCESS
[2025-05-31 10:29:34] TRACKER: Insert referral result: SUCCESS (ID: 11)
[2025-05-31 10:29:34] TRACKER: Referral record inserted successfully!
[2025-05-31 10:29:34] TRACKER: Order is completed - updating temp record status...
[2025-05-31 10:29:34] TRACKER: Temp record update result: SUCCESS
[2025-05-31 10:29:34] TRACKER: Awarding points to referrer...
[2025-05-31 10:29:34] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 10:29:34] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:29:34] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:29:34] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:29:34] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:29:55] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:29:55] FRONTEND: Referral code: user11
[2025-05-31 10:29:55] FRONTEND: User ID: 63
[2025-05-31 10:29:55] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:29:55] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:29:55] VALIDATION: Input code: 'user11'
[2025-05-31 10:29:55] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:29:55] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:29:55] VALIDATION: Usage limit setting: 1
[2025-05-31 10:29:55] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:29:55] VALIDATION: Current usage count: 1 out of 1
[2025-05-31 10:29:55] VALIDATION: USAGE LIMIT EXCEEDED! Code: 'USER11', Owner: 64, Limit: 1, Uses: 1
[2025-05-31 10:29:55] FRONTEND: Validation result: Array
(
    [valid] => 
    [user_id] => 
    [error_code] => USAGE_LIMIT_EXCEEDED
    [message] => This referral code has reached its usage limit (1 uses). Please try a different code.
)

[2025-05-31 10:29:55] FRONTEND: VALIDATION FAILED - Code: user11, Error: USAGE_LIMIT_EXCEEDED, Message: This referral code has reached its usage limit (1 uses). Please try a different code.
[2025-05-31 10:30:26] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:30:26] FRONTEND: Referral code: user11
[2025-05-31 10:30:26] FRONTEND: User ID: 63
[2025-05-31 10:30:26] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:30:26] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:30:26] VALIDATION: Input code: 'user11'
[2025-05-31 10:30:26] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:30:26] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:30:26] VALIDATION: Usage limit setting: 2
[2025-05-31 10:30:26] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:30:26] VALIDATION: Current usage count: 1 out of 2
[2025-05-31 10:30:26] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 1/2
[2025-05-31 10:30:26] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 10:30:26] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 10:30:26] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:30:26] VALIDATION: Input code: 'user11'
[2025-05-31 10:30:26] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:30:26] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:30:26] VALIDATION: Usage limit setting: 2
[2025-05-31 10:30:26] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:30:26] VALIDATION: Current usage count: 1 out of 2
[2025-05-31 10:30:26] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Uses: 1/2
[2025-05-31 10:30:26] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 10:30:26] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Order 49674 completed, triggering discount cleanup
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Order 49674 - User ID: 63
[2025-05-31 10:30:32] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:30:32] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:30:32] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:30:32] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 10:30:32] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 10:30:32] TRACKER: Order ID: 49674
[2025-05-31 10:30:32] TRACKER: Old Status: new
[2025-05-31 10:30:32] TRACKER: New Status: publish
[2025-05-31 10:30:32] TRACKER: No referral records found for order 49674
[2025-05-31 10:30:32] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 10:30:32] TRACKER: User ID: 63
[2025-05-31 10:30:32] TRACKER: Order ID: 49674
[2025-05-31 10:30:32] TRACKER: Payment Code: cash
[2025-05-31 10:30:32] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 10:30:32] TRACKER: Looking for tracking record for user 63
[2025-05-31 10:30:32] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 10:30:32] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 10:30:26
        )

)

[2025-05-31 10:30:32] TRACKER: Found referral record: stdClass Object
(
    [id] => 5
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 10:30:26
)

[2025-05-31 10:30:32] TRACKER: Attempting to get user data...
[2025-05-31 10:30:32] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 10:30:32] TRACKER: Preparing referral data for insertion...
[2025-05-31 10:30:32] TRACKER: Course name: full stack bundle
[2025-05-31 10:30:32] TRACKER: Total amount: 250
[2025-05-31 10:30:32] TRACKER: Found order status in posts table: publish
[2025-05-31 10:30:32] TRACKER: Actual WordPress order status: publish
[2025-05-31 10:30:32] TRACKER: Setting referral status to: completed (order status: publish)
[2025-05-31 10:30:32] TRACKER: Calling insert_referral method...
[2025-05-31 10:30:32] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49674
    [status] => completed
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:30:32
    [updated_at] => 2025-05-31 10:30:32
)

[2025-05-31 10:30:32] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49674
    [status] => completed
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 10:30:32
    [updated_at] => 2025-05-31 10:30:32
)

[2025-05-31 10:30:32] TRACKER: Target table: wp_mst_referrals
[2025-05-31 10:30:32] TRACKER: Database insert result: SUCCESS
[2025-05-31 10:30:32] TRACKER: Insert referral result: SUCCESS (ID: 12)
[2025-05-31 10:30:32] TRACKER: Referral record inserted successfully!
[2025-05-31 10:30:32] TRACKER: Order is completed - updating temp record status...
[2025-05-31 10:30:32] TRACKER: Temp record update result: SUCCESS
[2025-05-31 10:30:32] TRACKER: Awarding points to referrer...
[2025-05-31 10:30:32] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 10:30:32] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 10:30:32] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 10:30:32] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 10:30:32] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 10:30:53] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 10:30:53] FRONTEND: Referral code: user11
[2025-05-31 10:30:53] FRONTEND: User ID: 63
[2025-05-31 10:30:53] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 10:30:53] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 10:30:53] VALIDATION: Input code: 'user11'
[2025-05-31 10:30:53] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 10:30:53] VALIDATION: Current prefix: 'MST-'
[2025-05-31 10:30:53] VALIDATION: Usage limit setting: 2
[2025-05-31 10:30:53] VALIDATION: Checking usage limit for referrer_id: 64
[2025-05-31 10:30:53] VALIDATION: Current usage count: 2 out of 2
[2025-05-31 10:30:53] VALIDATION: USAGE LIMIT EXCEEDED! Code: 'USER11', Owner: 64, Limit: 2, Uses: 2
[2025-05-31 10:30:53] FRONTEND: Validation result: Array
(
    [valid] => 
    [user_id] => 
    [error_code] => USAGE_LIMIT_EXCEEDED
    [message] => This referral code has reached its usage limit (2 uses). Please try a different code.
)

[2025-05-31 10:30:53] FRONTEND: VALIDATION FAILED - Code: user11, Error: USAGE_LIMIT_EXCEEDED, Message: This referral code has reached its usage limit (2 uses). Please try a different code.
