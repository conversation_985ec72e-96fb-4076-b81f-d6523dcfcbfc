[2025-05-31 13:02:12] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:02:27] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:02:36] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:02:37] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 13:02:37] FRONTEND: Referral code: user11
[2025-05-31 13:02:37] FRONTEND: User ID: 63
[2025-05-31 13:02:37] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 13:02:37] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 13:02:37] VALIDATION: Input code: 'user11'
[2025-05-31 13:02:37] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 13:02:37] VALIDATION: Current prefix: 'MST-'
[2025-05-31 13:02:37] VALIDATION: Usage limit setting: 0
[2025-05-31 13:02:37] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 13:02:37] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 13:02:37] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 13:02:37] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 13:02:37] VALIDATION: Input code: 'user11'
[2025-05-31 13:02:37] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 13:02:37] VALIDATION: Current prefix: 'MST-'
[2025-05-31 13:02:37] VALIDATION: Usage limit setting: 0
[2025-05-31 13:02:37] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 13:02:37] FRONTEND: INFO: Creating new tracking record for user with completed referrals
[2025-05-31 13:02:37] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 13:02:41] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Order 49687 completed, triggering discount cleanup
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Order 49687 - User ID: 63
[2025-05-31 13:02:42] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:02:42] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:02:42] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:02:42] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 13:02:42] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 13:02:42] TRACKER: Order ID: 49687
[2025-05-31 13:02:42] TRACKER: Old Status: new
[2025-05-31 13:02:42] TRACKER: New Status: publish
[2025-05-31 13:02:42] TRACKER: No referral records found for order 49687
[2025-05-31 13:02:42] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 13:02:42] TRACKER: User ID: 63
[2025-05-31 13:02:42] TRACKER: Order ID: 49687
[2025-05-31 13:02:42] TRACKER: Payment Code: cash
[2025-05-31 13:02:42] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 13:02:42] TRACKER: Looking for tracking record for user 63
[2025-05-31 13:02:42] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 13:02:42] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:40:03
        )

    [11] => stdClass Object
        (
            [id] => 12
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:50:24
        )

    [12] => stdClass Object
        (
            [id] => 13
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 12:42:55
        )

    [13] => stdClass Object
        (
            [id] => 14
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 12:52:56
        )

    [14] => stdClass Object
        (
            [id] => 15
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 13:02:37
        )

)

[2025-05-31 13:02:42] TRACKER: Found referral record: stdClass Object
(
    [id] => 15
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 13:02:37
)

[2025-05-31 13:02:42] TRACKER: Attempting to get user data...
[2025-05-31 13:02:42] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 13:02:42] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 13:02:42] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 13:02:42] TRACKER: Preparing referral data for insertion...
[2025-05-31 13:02:42] TRACKER: Course name: full stack bundle
[2025-05-31 13:02:42] TRACKER: Original price total: 500
[2025-05-31 13:02:42] TRACKER: Discounted price total: 250
[2025-05-31 13:02:42] TRACKER: Total amount: 250
[2025-05-31 13:02:42] TRACKER: Found MasterStudy LMS order status in meta: pending
[2025-05-31 13:02:42] TRACKER: Actual order status: pending
[2025-05-31 13:02:42] TRACKER: Setting referral status to: pending (order status: pending)
[2025-05-31 13:02:42] TRACKER: Calling insert_referral method...
[2025-05-31 13:02:42] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49687
    [status] => pending
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 13:02:42
    [updated_at] => 2025-05-31 13:02:42
)

[2025-05-31 13:02:42] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49687
    [status] => pending
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 13:02:42
    [updated_at] => 2025-05-31 13:02:42
)

[2025-05-31 13:02:42] TRACKER: Target table: wp_mst_referrals
[2025-05-31 13:02:42] TRACKER: Database insert result: SUCCESS
[2025-05-31 13:02:42] TRACKER: Insert referral result: SUCCESS (ID: 23)
[2025-05-31 13:02:42] TRACKER: Referral record inserted successfully!
[2025-05-31 13:02:42] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 13:02:42] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 13:02:42] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:02:42] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:02:42] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:02:42] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:02:48] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:02:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:02] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:05] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:09] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:10] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:16] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Order 49687 completed, triggering discount cleanup
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Order 49687 - User ID: 63
[2025-05-31 13:03:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Clearing active discount - Code: user1, Amount: 50%
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:03:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:03:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:03:17] TRACKER: MasterStudy order meta update detected - Post ID: 49687, Status: completed
[2025-05-31 13:03:17] TRACKER: Syncing status to 'completed' for user 63 and courses: 49537
[2025-05-31 13:03:17] TRACKER: Updated referral 1 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 2 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 3 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 4 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 5 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 6 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 7 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 8 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 9 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 10 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 11 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 12 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 13 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 14 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 15 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 17 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 18 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 20 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 21 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Referral 22 already has status 'completed'
[2025-05-31 13:03:17] TRACKER: Updated referral 23 from 'pending' to 'completed'
[2025-05-31 13:03:17] TRACKER: Status sync completed: 11 referrals updated to 'completed'
[2025-05-31 13:03:17] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:03:17] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:03:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:03:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:03:17] TRACKER: === MASTERSTUDY ORDER STATUS CHANGE HOOK ===
[2025-05-31 13:03:17] TRACKER: User ID: 63
[2025-05-31 13:03:17] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 13:03:17] TRACKER: No recent orders found for user 63 in MasterStudy hook
[2025-05-31 13:03:17] TRACKER: MasterStudy admin order save detected - Post ID: 49687
[2025-05-31 13:03:17] TRACKER: Order status change: completed → completed for user 63
[2025-05-31 13:03:17] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:03:17] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:19] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:19] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:21] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:24] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:29] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:34] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Order 49687 completed, triggering discount cleanup
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Order 49687 - User ID: 63
[2025-05-31 13:03:35] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Clearing active discount - Code: tester141, Amount: 50%
[2025-05-31 13:03:35] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:03:35] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:03:35] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:03:35] TRACKER: MasterStudy order meta update detected - Post ID: 49687, Status: pending
[2025-05-31 13:03:35] TRACKER: Syncing status to 'pending' for user 63 and courses: 49537
[2025-05-31 13:03:35] TRACKER: Updated referral 1 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 2 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 3 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 4 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 5 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 6 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 7 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 8 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 9 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 10 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 11 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 12 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 13 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 14 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 15 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 17 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 18 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 20 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 21 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 22 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Updated referral 23 from 'completed' to 'pending'
[2025-05-31 13:03:35] TRACKER: Status sync completed: 21 referrals updated to 'pending'
[2025-05-31 13:03:35] TRACKER: MasterStudy admin order save detected - Post ID: 49687
[2025-05-31 13:03:35] TRACKER: Order status change: pending → pending for user 63
[2025-05-31 13:03:35] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:03:36] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:37] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:37] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:38] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:40] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:48] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:03:58] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:04:42] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:05:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:06:04] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:46] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:47] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:51] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:52] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:52] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:54] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:56] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:57] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:07:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:01] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:03] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:04] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:06] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:07] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:16] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:17] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:19] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:20] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:26] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Order 49685 completed, triggering discount cleanup
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Order 49685 - User ID: 63
[2025-05-31 13:08:27] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Clearing active discount - Code: tester141, Amount: 50%
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:08:27] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:08:27] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:08:27] TRACKER: MasterStudy order meta update detected - Post ID: 49685, Status: completed
[2025-05-31 13:08:27] TRACKER: Syncing status to 'completed' for user 63 and courses: 49537
[2025-05-31 13:08:27] TRACKER: Updated referral 1 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 2 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 3 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 4 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 5 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 6 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 7 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 8 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 9 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 10 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 11 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 12 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 13 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 14 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 15 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 17 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 18 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 20 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 21 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 22 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Updated referral 23 from 'pending' to 'completed'
[2025-05-31 13:08:27] TRACKER: Status sync completed: 21 referrals updated to 'completed'
[2025-05-31 13:08:27] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:08:27] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:08:27] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:08:27] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:08:27] TRACKER: === MASTERSTUDY ORDER STATUS CHANGE HOOK ===
[2025-05-31 13:08:27] TRACKER: User ID: 63
[2025-05-31 13:08:27] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 13:08:27] TRACKER: No recent orders found for user 63 in MasterStudy hook
[2025-05-31 13:08:27] TRACKER: MasterStudy admin order save detected - Post ID: 49685
[2025-05-31 13:08:27] TRACKER: Order status change: completed → completed for user 63
[2025-05-31 13:08:27] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:08:27] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:29] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:29] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:31] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:32] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:39] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:46] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Order 49685 completed, triggering discount cleanup
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Order 49685 - User ID: 63
[2025-05-31 13:08:47] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Clearing active discount - Code: tester141, Amount: 50%
[2025-05-31 13:08:47] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:08:47] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:08:47] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:08:47] TRACKER: MasterStudy order meta update detected - Post ID: 49685, Status: pending
[2025-05-31 13:08:47] TRACKER: Syncing status to 'pending' for user 63 and courses: 49537
[2025-05-31 13:08:47] TRACKER: Updated referral 1 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 2 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 3 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 4 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 5 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 6 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 7 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 8 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 9 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 10 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 11 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 12 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 13 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 14 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 15 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 17 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 18 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 20 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 21 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 22 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Updated referral 23 from 'completed' to 'pending'
[2025-05-31 13:08:47] TRACKER: Status sync completed: 21 referrals updated to 'pending'
[2025-05-31 13:08:47] TRACKER: MasterStudy admin order save detected - Post ID: 49685
[2025-05-31 13:08:47] TRACKER: Order status change: pending → pending for user 63
[2025-05-31 13:08:47] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:08:47] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:49] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:49] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:50] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:51] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:08:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:09:54] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:11:00] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:11:54] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:13:01] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:13:55] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:09] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:19] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:20] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 13:14:20] FRONTEND: Referral code: user11
[2025-05-31 13:14:20] FRONTEND: User ID: 63
[2025-05-31 13:14:20] FRONTEND: About to call validate_referral_code_detailed for code: user11
[2025-05-31 13:14:20] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 13:14:20] VALIDATION: Input code: 'user11'
[2025-05-31 13:14:20] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 13:14:20] VALIDATION: Current prefix: 'MST-'
[2025-05-31 13:14:20] VALIDATION: Usage limit setting: 0
[2025-05-31 13:14:20] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 13:14:20] FRONTEND: Validation result: Array
(
    [valid] => 1
    [user_id] => 64
    [error_code] => 
    [message] => Referral code is valid.
)

[2025-05-31 13:14:20] FRONTEND: VALIDATION SUCCESSFUL - Code: user11, User ID: 64
[2025-05-31 13:14:20] VALIDATION: === DETAILED VALIDATION STARTED ===
[2025-05-31 13:14:20] VALIDATION: Input code: 'user11'
[2025-05-31 13:14:20] VALIDATION: Cleaned code: 'USER11'
[2025-05-31 13:14:20] VALIDATION: Current prefix: 'MST-'
[2025-05-31 13:14:20] VALIDATION: Usage limit setting: 0
[2025-05-31 13:14:20] VALIDATION: VALIDATION SUCCESSFUL: Code 'USER11', Owner: 64, Unlimited usage
[2025-05-31 13:14:20] FRONTEND: INFO: Updating existing pending tracking record for user 63
[2025-05-31 13:14:25] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Order 49688 completed, triggering discount cleanup
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Order 49688 - User ID: 63
[2025-05-31 13:14:25] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Clearing active discount - Code: user11, Amount: 50%
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:14:25] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:14:25] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:14:25] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 13:14:25] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 13:14:25] TRACKER: Order ID: 49688
[2025-05-31 13:14:25] TRACKER: Old Status: new
[2025-05-31 13:14:25] TRACKER: New Status: publish
[2025-05-31 13:14:25] TRACKER: No referral records found for order 49688
[2025-05-31 13:14:25] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 13:14:25] TRACKER: User ID: 63
[2025-05-31 13:14:25] TRACKER: Order ID: 49688
[2025-05-31 13:14:25] TRACKER: Payment Code: cash
[2025-05-31 13:14:25] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 13:14:25] TRACKER: Looking for tracking record for user 63
[2025-05-31 13:14:25] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 13:14:25] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:29:28
        )

    [3] => stdClass Object
        (
            [id] => 4
            [referrer_id] => 63
            [referred_id] => 64
            [referral_code] => user1
            [status] => pending
            [logged_in_at] => 2025-05-31 08:48:52
        )

    [4] => stdClass Object
        (
            [id] => 5
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:30:26
        )

    [5] => stdClass Object
        (
            [id] => 6
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 10:59:54
        )

    [6] => stdClass Object
        (
            [id] => 7
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:01:33
        )

    [7] => stdClass Object
        (
            [id] => 8
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:09:51
        )

    [8] => stdClass Object
        (
            [id] => 9
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:28:11
        )

    [9] => stdClass Object
        (
            [id] => 10
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:35:08
        )

    [10] => stdClass Object
        (
            [id] => 11
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:40:03
        )

    [11] => stdClass Object
        (
            [id] => 12
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 11:50:24
        )

    [12] => stdClass Object
        (
            [id] => 13
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 12:42:55
        )

    [13] => stdClass Object
        (
            [id] => 14
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => completed
            [logged_in_at] => 2025-05-31 12:52:56
        )

    [14] => stdClass Object
        (
            [id] => 15
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 13:14:20
        )

)

[2025-05-31 13:14:25] TRACKER: Found referral record: stdClass Object
(
    [id] => 15
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 13:14:20
)

[2025-05-31 13:14:25] TRACKER: Attempting to get user data...
[2025-05-31 13:14:25] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 13:14:25] TRACKER: Found stored original price for item 49537: 500
[2025-05-31 13:14:25] TRACKER: Item 49537 - Original: 500, Current (discounted): 250
[2025-05-31 13:14:25] TRACKER: Preparing referral data for insertion...
[2025-05-31 13:14:25] TRACKER: Course name: full stack bundle
[2025-05-31 13:14:25] TRACKER: Original price total: 500
[2025-05-31 13:14:25] TRACKER: Discounted price total: 250
[2025-05-31 13:14:25] TRACKER: Total amount: 250
[2025-05-31 13:14:25] TRACKER: Found MasterStudy LMS order status in meta: pending
[2025-05-31 13:14:25] TRACKER: Actual order status: pending
[2025-05-31 13:14:25] TRACKER: Setting referral status to: pending (order status: pending)
[2025-05-31 13:14:25] TRACKER: Calling insert_referral method...
[2025-05-31 13:14:25] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49688
    [status] => pending
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 13:14:25
    [updated_at] => 2025-05-31 13:14:25
)

[2025-05-31 13:14:25] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49688
    [status] => pending
    [amount] => 250
    [original_price] => 500
    [discounted_price] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 13:14:25
    [updated_at] => 2025-05-31 13:14:25
)

[2025-05-31 13:14:25] TRACKER: Target table: wp_mst_referrals
[2025-05-31 13:14:25] TRACKER: Database insert result: SUCCESS
[2025-05-31 13:14:25] TRACKER: Insert referral result: SUCCESS (ID: 24)
[2025-05-31 13:14:25] TRACKER: Referral record inserted successfully!
[2025-05-31 13:14:25] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 13:14:25] TRACKER: REFERRAL TRACKING SUCCESSFUL!
[2025-05-31 13:14:25] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:14:25] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:14:25] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:14:25] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:14:33] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:43] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:45] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:47] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:47] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:14:57] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:03] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:05] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:06] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:15] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:16] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Order 49688 completed, triggering discount cleanup
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Order 49688 - User ID: 63
[2025-05-31 13:15:16] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Clearing active discount - Code: tester141, Amount: 50%
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:15:16] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:15:16] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:15:16] TRACKER: MasterStudy order meta update detected - Post ID: 49688, Status: completed
[2025-05-31 13:15:16] TRACKER: Syncing status to 'completed' for user 63 and courses: 49537
[2025-05-31 13:15:16] TRACKER: Updated referral 1 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 2 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 3 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 4 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 5 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 6 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 7 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 8 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 9 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 10 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 11 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 12 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 13 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 14 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 15 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 17 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 18 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 20 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 21 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 22 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 23 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Updated referral 24 from 'pending' to 'completed'
[2025-05-31 13:15:16] TRACKER: Status sync completed: 22 referrals updated to 'completed'
[2025-05-31 13:15:16] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: 1 items
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Before cleanup - Active discount: NO, Flag: 0
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:15:16] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:15:17] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:15:17] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:15:17] TRACKER: === MASTERSTUDY ORDER STATUS CHANGE HOOK ===
[2025-05-31 13:15:17] TRACKER: User ID: 63
[2025-05-31 13:15:17] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 13:15:17] TRACKER: No recent orders found for user 63 in MasterStudy hook
[2025-05-31 13:15:17] TRACKER: MasterStudy admin order save detected - Post ID: 49688
[2025-05-31 13:15:17] TRACKER: Order status change: completed → completed for user 63
[2025-05-31 13:15:17] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:15:17] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:18] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:18] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:20] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:21] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:28] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:32] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Order 49688 completed, triggering discount cleanup
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Order 49688 - User ID: 63
[2025-05-31 13:15:33] DISCOUNT CLEANUP: *** STARTING COMPREHENSIVE DISCOUNT CLEANUP ***
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Cleanup triggered with user_id: 63, cart_items: NULL
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Before cleanup - Active discount: YES, Flag: 0
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Cleared processing flag
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Clearing active discount - Code: tester141, Amount: 50%
[2025-05-31 13:15:33] DISCOUNT CLEANUP: Cleared static caches - Price cache: 0 items, Discounted items: 0 items
[2025-05-31 13:15:33] DISCOUNT CLEANUP: *** COMPREHENSIVE DISCOUNT CLEANUP COMPLETE ***
[2025-05-31 13:15:33] DISCOUNT CLEANUP: User should now need to re-enter referral code for future purchases
[2025-05-31 13:15:33] TRACKER: MasterStudy order meta update detected - Post ID: 49688, Status: pending
[2025-05-31 13:15:33] TRACKER: Syncing status to 'pending' for user 63 and courses: 49537
[2025-05-31 13:15:33] TRACKER: Updated referral 1 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 2 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 3 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 4 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 5 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 6 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 7 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 8 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 9 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 10 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 11 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 12 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 13 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 14 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 15 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 17 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 18 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 20 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 21 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 22 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 23 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Updated referral 24 from 'completed' to 'pending'
[2025-05-31 13:15:33] TRACKER: Status sync completed: 22 referrals updated to 'pending'
[2025-05-31 13:15:33] TRACKER: MasterStudy admin order save detected - Post ID: 49688
[2025-05-31 13:15:33] TRACKER: Order status change: pending → pending for user 63
[2025-05-31 13:15:33] TRACKER: No status change or missing data - skipping sync
[2025-05-31 13:15:33] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:35] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:35] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:37] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:46] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:51] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:15:57] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:16:53] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:17:58] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:18:53] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:19:59] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
[2025-05-31 13:20:54] TRACKER: MasterStudy LMS ADMIN order status synchronization hooks registered
