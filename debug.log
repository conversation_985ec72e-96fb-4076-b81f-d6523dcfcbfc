=== CMS REFERRAL DEBUG LOG ===
Started: 2024-12-19 12:00:00

This file will track all referral system activities for debugging purposes.

=== DEBUG LOGGING SYSTEM INITIALIZED ===
- Frontend logging: ENABLED
- Tracker logging: ENABLED
- All referral code applications will be logged
- All order completion hooks will be logged
- All tracking record operations will be logged

=== READY FOR TESTING ===
[2025-05-31 08:36:13] FRONTEND: === REFERRAL CODE APPLICATION STARTED ===
[2025-05-31 08:36:13] FRONTEND: Referral code: user11
[2025-05-31 08:36:13] FRONTEND: User ID: 63
[2025-05-31 08:36:13] FRONTEND: SUCCESS: Created tracking record - Referrer: user1 (ID: 64), Referred: user (ID: 63), Code: user11
[2025-05-31 08:36:21] TRACKER: Detected MasterStudy order post type: stm-orders
[2025-05-31 08:36:21] TRACKER: === ORDER STATUS CHANGE DETECTED ===
[2025-05-31 08:36:21] TRACKER: Order ID: 49663
[2025-05-31 08:36:21] TRACKER: Old Status: new
[2025-05-31 08:36:21] TRACKER: New Status: publish
[2025-05-31 08:36:21] TRACKER: No referral records found for order 49663
[2025-05-31 08:36:21] TRACKER: === ORDER COMPLETION HOOK FIRED ===
[2025-05-31 08:36:21] TRACKER: User ID: 63
[2025-05-31 08:36:21] TRACKER: Order ID: 49663
[2025-05-31 08:36:21] TRACKER: Payment Code: cash
[2025-05-31 08:36:21] TRACKER: Cart Items: Array
(
    [0] => Array
        (
            [item_id] => 49537
            [price] => 250
        )

)

[2025-05-31 08:36:21] TRACKER: Looking for tracking record for user 63
[2025-05-31 08:36:21] TRACKER: Tracking table: wp_custom_cms_referral_tracking
[2025-05-31 08:36:21] TRACKER: All tracking records: Array
(
    [0] => stdClass Object
        (
            [id] => 1
            [referrer_id] => 63
            [referred_id] => 0
            [referral_code] => user1
            [status] => active
            [logged_in_at] => 2025-05-31 08:30:33
        )

    [1] => stdClass Object
        (
            [id] => 2
            [referrer_id] => 64
            [referred_id] => 0
            [referral_code] => user11
            [status] => active
            [logged_in_at] => 2025-05-31 08:35:06
        )

    [2] => stdClass Object
        (
            [id] => 3
            [referrer_id] => 64
            [referred_id] => 63
            [referral_code] => user11
            [status] => pending
            [logged_in_at] => 2025-05-31 08:36:13
        )

)

[2025-05-31 08:36:21] TRACKER: Found referral record: stdClass Object
(
    [id] => 3
    [referrer_id] => 64
    [referred_id] => 63
    [referral_code] => user11
    [status] => pending
    [logged_in_at] => 2025-05-31 08:36:13
)

[2025-05-31 08:36:21] TRACKER: Attempting to get user data...
[2025-05-31 08:36:21] TRACKER: User data retrieved - Referrer: user1, Referred: user
[2025-05-31 08:36:21] TRACKER: Preparing referral data for insertion...
[2025-05-31 08:36:21] TRACKER: Course name: full stack bundle
[2025-05-31 08:36:21] TRACKER: Total amount: 250
[2025-05-31 08:36:21] TRACKER: Found order status in posts table: publish
[2025-05-31 08:36:21] TRACKER: Actual WordPress order status: publish
[2025-05-31 08:36:21] TRACKER: Setting referral status to: pending
[2025-05-31 08:36:21] TRACKER: Calling insert_referral method...
[2025-05-31 08:36:21] TRACKER: insert_referral called with data: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49663
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 08:36:21
    [updated_at] => 2025-05-31 08:36:21
)

[2025-05-31 08:36:21] TRACKER: Merged data for insertion: Array
(
    [referrer_id] => 64
    [referrer_name] => user1
    [referred_id] => 63
    [referred_name] => user
    [referral_code] => user11
    [course_id] => 49537
    [course_name] => full stack bundle
    [order_id] => 49663
    [status] => pending
    [amount] => 250
    [points_awarded] => 25
    [created_at] => 2025-05-31 08:36:21
    [updated_at] => 2025-05-31 08:36:21
)

[2025-05-31 08:36:21] TRACKER: Target table: wp_mst_referrals
[2025-05-31 08:36:21] TRACKER: Database insert result: SUCCESS
[2025-05-31 08:36:21] TRACKER: Insert referral result: SUCCESS (ID: 1)
[2025-05-31 08:36:21] TRACKER: Referral record inserted successfully!
[2025-05-31 08:36:21] TRACKER: Order is pending - keeping temp record as pending for potential reuse
[2025-05-31 08:36:21] TRACKER: REFERRAL TRACKING SUCCESSFUL!
