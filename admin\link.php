<?php
/**
 * Admin Link File
 *
 * This file connects the admin functionality to the main plugin.
 * It handles including all the necessary admin files.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Define admin directory constant
 */
define( 'CUSTOM_CMS_REFERAL_PLUGIN_ADMIN_DIR', CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'admin/' );

/**
 * Load admin files
 */
require_once CUSTOM_CMS_REFERAL_PLUGIN_ADMIN_DIR . 'helpers.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_ADMIN_DIR . 'admin.php';
require_once CUSTOM_CMS_REFERAL_PLUGIN_ADMIN_DIR . 'class-referral-tracker.php';

/**
 * AJAX handlers are now registered in admin/admin.php constructor
 * This file only loads the admin files
 */
