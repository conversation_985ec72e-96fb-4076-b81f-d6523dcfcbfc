<?php
/**
 * ENHANCED Investigation script to monitor manual order status changes
 *
 * This script will:
 * 1. Show current state of orders and referrals
 * 2. Wait for you to make a manual change
 * 3. Show what changed after the manual update
 * 4. Identify which referrals were affected
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== ENHANCED HOOK BEHAVIOR INVESTIGATION ===\n";
echo "Current time: " . current_time('mysql') . "\n\n";

// Get some recent MasterStudy orders to analyze
global $wpdb;

echo "1. CHECKING RECENT ORDERS AND THEIR REFERRALS:\n";
echo str_repeat("-", 50) . "\n";

$orders = $wpdb->get_results("
    SELECT p.ID, p.post_status, p.post_author, p.post_date
    FROM {$wpdb->posts} p 
    WHERE p.post_type = 'stm-orders' 
    ORDER BY p.post_date DESC 
    LIMIT 10
");

foreach ($orders as $order) {
    echo "Order ID: {$order->ID} | Status: {$order->post_status} | Date: {$order->post_date}\n";
    
    // Check referrals for this order
    $referrals = $wpdb->get_results($wpdb->prepare("
        SELECT id, referrer_id, referred_id, status, order_id 
        FROM wp_mst_referrals 
        WHERE order_id = %d
    ", $order->ID));
    
    if (!empty($referrals)) {
        foreach ($referrals as $ref) {
            echo "  └─ Referral ID: {$ref->id} | Status: {$ref->status} | Referrer: {$ref->referrer_id} | Referred: {$ref->referred_id}\n";
        }
    } else {
        echo "  └─ No referrals found\n";
    }
    echo "\n";
}

echo "\n2. CHECKING FOR POTENTIAL ISSUES:\n";
echo str_repeat("-", 50) . "\n";

// Check if there are multiple referrals with same order_id
echo "A. Orders with multiple referrals:\n";
$multiple_referrals = $wpdb->get_results("
    SELECT order_id, COUNT(*) as referral_count 
    FROM wp_mst_referrals 
    GROUP BY order_id 
    HAVING COUNT(*) > 1
    ORDER BY referral_count DESC
");

if (!empty($multiple_referrals)) {
    foreach ($multiple_referrals as $mr) {
        echo "  Order {$mr->order_id} has {$mr->referral_count} referrals\n";
    }
} else {
    echo "  No orders with multiple referrals found\n";
}

echo "\nB. Checking for referrals without valid order_id:\n";
$invalid_orders = $wpdb->get_results("
    SELECT r.id, r.order_id, r.status 
    FROM wp_mst_referrals r 
    LEFT JOIN {$wpdb->posts} p ON r.order_id = p.ID 
    WHERE p.ID IS NULL
");

if (!empty($invalid_orders)) {
    foreach ($invalid_orders as $io) {
        echo "  Referral {$io->id} references non-existent order {$io->order_id}\n";
    }
} else {
    echo "  All referrals have valid order_id references\n";
}

echo "\nC. Checking recent referral status changes:\n";
$recent_changes = $wpdb->get_results("
    SELECT id, order_id, status, updated_at 
    FROM wp_mst_referrals 
    WHERE updated_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ORDER BY updated_at DESC
");

if (!empty($recent_changes)) {
    foreach ($recent_changes as $rc) {
        echo "  Referral {$rc->id} (Order {$rc->order_id}) | Status: {$rc->status} | Updated: {$rc->updated_at}\n";
    }
} else {
    echo "  No recent referral status changes found\n";
}

echo "\n3. TESTING HOOK BEHAVIOR SIMULATION:\n";
echo str_repeat("-", 50) . "\n";

// Simulate what happens when we change one order status
if (!empty($orders)) {
    $test_order = $orders[0];
    echo "Testing with Order ID: {$test_order->ID}\n";
    
    // Check current MasterStudy status
    $current_status = get_post_meta($test_order->ID, 'status', true);
    echo "Current MasterStudy status: " . ($current_status ?: 'EMPTY') . "\n";
    echo "WordPress post_status: {$test_order->post_status}\n";
    
    // Find referrals for this specific order
    $test_referrals = $wpdb->get_results($wpdb->prepare("
        SELECT * FROM wp_mst_referrals WHERE order_id = %d
    ", $test_order->ID));
    
    echo "Referrals found for this order: " . count($test_referrals) . "\n";
    
    if (!empty($test_referrals)) {
        foreach ($test_referrals as $tr) {
            echo "  Referral {$tr->id}: Status={$tr->status}, Referrer={$tr->referrer_id}\n";
        }
    }
}

echo "\n4. POTENTIAL ROOT CAUSES:\n";
echo str_repeat("-", 50) . "\n";
echo "A. Hook firing multiple times for same order\n";
echo "B. Query returning wrong referrals (missing WHERE clause)\n";
echo "C. Global variable contamination\n";
echo "D. WordPress post_status vs MasterStudy meta status mismatch\n";
echo "E. Race condition with multiple simultaneous updates\n";

echo "\n5. RECOMMENDATIONS FOR DEBUGGING:\n";
echo str_repeat("-", 50) . "\n";
echo "1. Add more specific logging to track which order triggers which referral updates\n";
echo "2. Check if transition_post_status fires multiple times\n";
echo "3. Verify the SQL query in handle_order_status_change_delayed\n";
echo "4. Add order_id validation before processing referrals\n";
echo "5. Consider adding a lock mechanism to prevent concurrent updates\n";
?>
