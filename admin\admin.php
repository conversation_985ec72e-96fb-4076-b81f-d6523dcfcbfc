<?php
/**
 * Admin Class
 *
 * This file contains the main admin class for the Custom CMS Referral Plugin.
 * It handles menu creation, page rendering, and assets enqueuing.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Custom_CMS_Referral_Admin Class
 *
 * Handles the admin functionality of the Custom CMS Referral Plugin.
 *
 * @since 1.0.0
 */
class Custom_CMS_Referral_Admin {

    /**
     * Instance of this class.
     *
     * @since 1.0.0
     * @var object
     */
    protected static $instance = null;

    /**
     * Constructor for the admin class.
     *
     * @since 1.0.0
     */
    public function __construct() {
        // Add admin menu items
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );

        // Enqueue admin scripts and styles
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_assets' ) );

        // Register AJAX handlers for saving settings
        add_action( 'wp_ajax_custom_cms_referral_save_settings', array( $this, 'save_settings_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_save_general_settings', array( $this, 'save_general_settings_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_save_discount_settings', array( $this, 'save_discount_settings_ajax' ) );

        // Register AJAX handlers for referral management
        add_action( 'wp_ajax_custom_cms_referral_approve', array( $this, 'approve_referral_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_dismiss_notice', array( $this, 'dismiss_notice_ajax' ) );

        // Register settings
        $this->register_settings();
    }

    /**
     * Register plugin settings
     */
    public function register_settings() {
        // Register existing settings (maintain backward compatibility)
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_usage_limit' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_discount_type' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_discount_amount' );

        // Register new general settings
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_enable_system' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_cookie_duration' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_code_prefix' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_enable_emails' );

        // Register email sharing template settings
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_sharing_subject' );
        register_setting( 'custom_cms_referral_settings', 'custom_cms_referral_sharing_template' );

        // Log settings when they change - for debugging
        add_action( 'update_option_custom_cms_referral_usage_limit', function( $old_value, $new_value ) {
            error_log( 'Usage limit updated from ' . $old_value . ' to ' . $new_value );
        }, 10, 2 );

        add_action( 'update_option_custom_cms_referral_discount_type', function( $old_value, $new_value ) {
            error_log( 'Discount type updated from ' . $old_value . ' to ' . $new_value );
        }, 10, 2 );

        add_action( 'update_option_custom_cms_referral_discount_amount', function( $old_value, $new_value ) {
            error_log( 'Discount amount updated from ' . $old_value . ' to ' . $new_value );
        }, 10, 2 );

        // Log new settings changes
        add_action( 'update_option_custom_cms_referral_code_prefix', function( $old_value, $new_value ) {
            error_log( 'Code prefix updated from ' . $old_value . ' to ' . $new_value );
        }, 10, 2 );

        add_action( 'update_option_custom_cms_referral_cookie_duration', function( $old_value, $new_value ) {
            error_log( 'Cookie duration updated from ' . $old_value . ' to ' . $new_value );
        }, 10, 2 );
    }

    /**
     * Return an instance of this class.
     *
     * @since 1.0.0
     * @return object A single instance of this class.
     */
    public static function get_instance() {
        if ( null == self::$instance ) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * Register the admin menu and submenu items.
     *
     * @since 1.0.0
     */
    public function add_admin_menu() {
        // Add main menu item
        add_menu_page(
            __('Custom CMS Referrals', 'custom-cms-referal'),
            __('Custom CMS Referrals', 'custom-cms-referal'),
            'manage_options',
            'custom-cms-referral',
            array( $this, 'render_dashboard_page' ),
            'dashicons-share',
            25  // Position after Pages (20) but before Comments (26)
        );

        // Add submenu items
        add_submenu_page(
            'custom-cms-referral',
            __('Dashboard', 'custom-cms-referal'),
            __('Dashboard', 'custom-cms-referal'),
            'manage_options',
            'custom-cms-referral',
            array( $this, 'render_dashboard_page' )
        );

        add_submenu_page(
            'custom-cms-referral',
            __('Referrals', 'custom-cms-referal'),
            __('Referrals', 'custom-cms-referal'),
            'manage_options',
            'custom-cms-referral-referrals',
            array( $this, 'render_referrals_page' )
        );

        add_submenu_page(
            'custom-cms-referral',
            __('Settings', 'custom-cms-referal'),
            __('Settings', 'custom-cms-referal'),
            'manage_options',
            'custom-cms-referral-settings',
            array( $this, 'render_settings_page' )
        );
    }

    /**
     * Enqueue admin scripts and styles.
     *
     * @since 1.0.0
     * @param string $hook The current admin page.
     */
    public function enqueue_admin_assets( $hook ) {
        // Only load these assets on our plugin pages
        if ( strpos( $hook, 'custom-cms-referral' ) === false ) {
            return;
        }

        // Enqueue admin CSS
        wp_enqueue_style(
            'custom-cms-referral-admin-css',
            plugin_dir_url( __FILE__ ) . 'css/style.css',
            array(),
            CUSTOM_CMS_REFERAL_PLUGIN_VERSION
        );

        // Enqueue admin JS
        wp_enqueue_script(
            'custom-cms-referral-admin-js',
            plugin_dir_url( __FILE__ ) . 'js/script.js',
            array( 'jquery' ),
            CUSTOM_CMS_REFERAL_PLUGIN_VERSION,
            true
        );

        // Localize script for AJAX use
        wp_localize_script(
            'custom-cms-referral-admin-js',
            'custom_cms_referral_vars',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'custom-cms-referral-nonce' ),
                'i18n'     => array(
                    'confirm_delete' => __( 'Are you sure you want to delete this referral?', 'custom-cms-referal' ),
                    'confirm_bulk_action' => __( 'Are you sure you want to perform this action on the selected referrals?', 'custom-cms-referal' ),
                    'saved' => __( 'Settings saved successfully!', 'custom-cms-referal' ),
                    'error' => __( 'An error occurred. Please try again.', 'custom-cms-referal' ),
                ),
            )
        );
    }

    /**
     * Render the dashboard page.
     *
     * @since 1.0.0
     */
    public function render_dashboard_page() {
        include CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'admin/views/dashboard.php';
    }

    /**
     * Render the referrals page.
     *
     * @since 1.0.0
     */
    public function render_referrals_page() {
        include CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'admin/referrals.php';
    }

    /**
     * Render the settings page.
     *
     * @since 1.0.0
     */
    public function render_settings_page() {
        include CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'admin/settings.php';
    }

    /**
     * Process referral approval AJAX request.
     *
     * @since 1.0.0
     */
    public function approve_referral() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get referral ID
        $referral_id = isset( $_POST['referral_id'] ) ? absint( $_POST['referral_id'] ) : 0;

        if ( ! $referral_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid referral ID.', 'custom-cms-referal' ) ) );
        }

        // In a real implementation, we would update the referral status here
        // For demo purposes, we'll just return success
        wp_send_json_success( array(
            'message' => __( 'Referral approved successfully!', 'custom-cms-referal' ),
            'referral_id' => $referral_id
        ) );
    }

    /**
     * Process save settings AJAX request.
     *
     * @since 1.0.0
     */
    public function save_settings() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get settings data
        $settings = isset( $_POST['settings'] ) ? $_POST['settings'] : array();

        // In a real implementation, we would sanitize and save the settings here
        // For demo purposes, we'll just return success
        wp_send_json_success( array(
            'message' => __( 'Settings saved successfully!', 'custom-cms-referal' )
        ) );
    }

    /**
     * Save general settings via AJAX
     *
     * @return void
     * @since 1.0.0
     */
    public function save_general_settings_ajax() {
        // Check nonce for security
        check_ajax_referer('custom-cms-referral-nonce', 'nonce');

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'custom-cms-referal')));
            return;
        }

        // Parse form data
        parse_str($_POST['form_data'], $form_data);

        // Get and sanitize the settings
        $enable_referral_system = isset($form_data['enable_referral_system']) ? 1 : 0;
        $cookie_duration = isset($form_data['cookie_duration']) ? intval($form_data['cookie_duration']) : 30;
        $code_prefix = isset($form_data['code_prefix']) ? sanitize_text_field($form_data['code_prefix']) : 'MST-';
        $enable_emails = isset($form_data['enable_emails']) ? 1 : 0;

        // Get email sharing template settings
        $sharing_subject = isset($form_data['sharing_subject']) ? sanitize_text_field($form_data['sharing_subject']) : '';
        $sharing_template = isset($form_data['sharing_template']) ? sanitize_textarea_field($form_data['sharing_template']) : '';

        // Validate settings
        if ($cookie_duration < 1 || $cookie_duration > 365) {
            wp_send_json_error(array('message' => __('Cookie duration must be between 1 and 365 days.', 'custom-cms-referal')));
            return;
        }

        if (strlen($code_prefix) > 10) {
            wp_send_json_error(array('message' => __('Code prefix should not exceed 10 characters.', 'custom-cms-referal')));
            return;
        }

        // Save the settings
        update_option('custom_cms_referral_enable_system', $enable_referral_system);
        update_option('custom_cms_referral_cookie_duration', $cookie_duration);
        update_option('custom_cms_referral_code_prefix', $code_prefix);
        update_option('custom_cms_referral_enable_emails', $enable_emails);

        // Save email sharing template settings
        update_option('custom_cms_referral_sharing_subject', $sharing_subject);
        update_option('custom_cms_referral_sharing_template', $sharing_template);

        // Debug log for troubleshooting
        error_log(sprintf('Saving general settings: System=%s, Duration=%s, Prefix=%s, Emails=%s',
            $enable_referral_system, $cookie_duration, $code_prefix, $enable_emails));

        // Return success message
        wp_send_json_success(array(
            'message' => __('General settings saved successfully!', 'custom-cms-referal'),
            'settings' => array(
                'enable_referral_system' => $enable_referral_system,
                'cookie_duration' => $cookie_duration,
                'code_prefix' => $code_prefix,
                'enable_emails' => $enable_emails
            )
        ));
    }

    /**
     * Save settings via AJAX (legacy method)
     *
     * @return void
     * @since 1.0.0
     */
    public function save_settings_ajax() {
        // Check nonce for security
        check_ajax_referer('custom-cms-referral-nonce', 'nonce');

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'custom-cms-referal')));
            return;
        }

        // Get the usage limit from the request
        $usage_limit = isset($_POST['usage_limit']) ? intval($_POST['usage_limit']) : 1;

        // Save the settings
        update_option('custom_cms_referral_usage_limit', $usage_limit);

        // Debug log for troubleshooting
        error_log('Saving settings: Usage Limit=' . $usage_limit);

        // Return success message
        wp_send_json_success(array(
            'message' => __('Settings saved successfully!', 'custom-cms-referal'),
            'usage_limit' => $usage_limit
        ));
    }

    /**
     * Save discount settings via AJAX
     *
     * @return void
     * @since 1.0.0
     */
    public function save_discount_settings_ajax() {
        // Check nonce for security
        check_ajax_referer('custom-cms-referral-nonce', 'nonce');

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('Permission denied.', 'custom-cms-referal')));
            return;
        }

        // Get the settings from the request
        $discount_type = isset($_POST['discount_type']) ? sanitize_text_field($_POST['discount_type']) : 'percentage';
        $discount_amount = isset($_POST['discount_amount']) ? floatval($_POST['discount_amount']) : 10;
        $usage_limit = isset($_POST['usage_limit']) ? intval($_POST['usage_limit']) : 1;

        // Validate discount type
        if (!in_array($discount_type, array('percentage', 'fixed'))) {
            $discount_type = 'percentage';
        }

        // Validate discount amount
        if ($discount_amount <= 0) {
            $discount_amount = 10;
        }

        // Save the settings
        update_option('custom_cms_referral_discount_type', $discount_type);
        update_option('custom_cms_referral_discount_amount', $discount_amount);
        update_option('custom_cms_referral_usage_limit', $usage_limit);

        // Debug log for troubleshooting
        error_log(sprintf('Saving discount settings: Type=%s, Amount=%s, Usage Limit=%s',
            $discount_type, $discount_amount, $usage_limit));

        // Format the discount for display
        $formatted_discount = $discount_type === 'percentage' ? $discount_amount . '%' : '₹' . number_format($discount_amount, 2);

        // Return success message
        wp_send_json_success(array(
            'message' => __('Discount settings saved successfully!', 'custom-cms-referal'),
            'discount_type' => $discount_type,
            'discount_amount' => $discount_amount,
            'formatted_discount' => $formatted_discount,
            'usage_limit' => $usage_limit
        ));
    }

    /**
     * Dismiss admin notice.
     *
     * @since 1.0.0
     */
    public function dismiss_notice() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get notice ID
        $notice_id = isset( $_POST['notice_id'] ) ? sanitize_text_field( $_POST['notice_id'] ) : '';

        if ( ! $notice_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid notice ID.', 'custom-cms-referal' ) ) );
        }

        // In a real implementation, we would save this to user meta
        // For demo purposes, we'll just return success
        wp_send_json_success( array(
            'message' => __( 'Notice dismissed.', 'custom-cms-referal' )
        ) );
    }

    /**
     * AJAX handler for approving referrals
     *
     * @return void
     * @since 1.0.0
     */
    public function approve_referral_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get referral ID
        $referral_id = isset( $_POST['referral_id'] ) ? absint( $_POST['referral_id'] ) : 0;

        if ( ! $referral_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid referral ID.', 'custom-cms-referal' ) ) );
        }

        // In a real implementation, we would update the referral status here
        // For demo purposes, we'll just return success
        wp_send_json_success( array(
            'message' => __( 'Referral approved successfully!', 'custom-cms-referal' ),
            'referral_id' => $referral_id
        ) );
    }

    /**
     * AJAX handler for dismissing notices
     *
     * @return void
     * @since 1.0.0
     */
    public function dismiss_notice_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get notice ID
        $notice_id = isset( $_POST['notice_id'] ) ? sanitize_text_field( $_POST['notice_id'] ) : '';

        if ( ! $notice_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid notice ID.', 'custom-cms-referal' ) ) );
        }

        // In a real implementation, we would save this to user meta
        // For demo purposes, we'll just return success
        wp_send_json_success( array(
            'message' => __( 'Notice dismissed.', 'custom-cms-referal' )
        ) );
    }
}

// Initialize the admin class
Custom_CMS_Referral_Admin::get_instance();
