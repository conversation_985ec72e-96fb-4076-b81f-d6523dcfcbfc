<?php
/**
 * Frontend Class
 *
 * Handles frontend functionality including scripts, styles, and templates.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Frontend class
 */
class Custom_CMS_Referral_Frontend {

    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here
    }

    /**
     * Initialize the frontend
     */
    public function init() {
        // Register styles and scripts
        add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );

        // Add hooks for checkout integration if needed
        add_action( 'woocommerce_before_checkout_form', array( $this, 'add_referral_field_to_checkout' ), 10 );

        // Add hooks for MasterStudy LMS integration
        add_action( 'stm_lms_before_checkout', array( $this, 'add_referral_field_to_lms_checkout' ), 10 );

        // Register AJAX handlers
        add_action( 'wp_ajax_custom_cms_referral_apply_discount', array( $this, 'process_referral_code' ) );
        add_action( 'wp_ajax_nopriv_custom_cms_referral_apply_discount', array( $this, 'process_referral_code' ) );

        // Register additional hooks for the discount handler
        add_action( 'wp_ajax_apply_referral_discount', array( $this, 'process_referral_code' ) );
        add_action( 'wp_ajax_nopriv_apply_referral_discount', array( $this, 'process_referral_code' ) );
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_scripts() {
        // Register styles
        wp_enqueue_style(
            'custom-cms-referral-frontend',
            CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_URL . 'css/frontend.css',
            array(),
            CUSTOM_CMS_REFERAL_PLUGIN_VERSION
        );

        // Enqueue Font Awesome for icons
        wp_enqueue_style(
            'font-awesome',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4'
        );

        // Load clipboard.js library first
        wp_enqueue_script(
            'clipboard-js',
            'https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js',
            array('jquery'),
            '2.0.11',
            true
        );

        // Then register our custom scripts
        wp_enqueue_script(
            'custom-cms-referral-frontend',
            CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_URL . 'js/frontend.js',
            array( 'jquery', 'clipboard-js' ),
            CUSTOM_CMS_REFERAL_PLUGIN_VERSION,
            true
        );

        // Localize script with AJAX URL and nonce
        wp_localize_script(
            'custom-cms-referral-frontend',
            'custom_cms_referral_params',
            array(
                'ajax_url' => admin_url( 'admin-ajax.php' ),
                'nonce'    => wp_create_nonce( 'custom-cms-referral-nonce' ),
                'i18n'     => array(
                    'copy_success' => __( 'Copied to clipboard!', 'custom-cms-referal' ),
                    'copy_failed'  => __( 'Failed to copy. Please try manually.', 'custom-cms-referal' ),
                    'form_error'   => __( 'Please enter a valid referral code.', 'custom-cms-referal' ),
                )
            )
        );
    }

    /**
     * Add referral field to WooCommerce checkout
     */
    public function add_referral_field_to_checkout() {
        // Check if we need to display the field (based on settings)
        $show_field = apply_filters( 'custom_cms_referral_show_checkout_field', true );

        if ( ! $show_field ) {
            return;
        }

        // Get referral code from cookie if exists
        $referral_code = isset( $_COOKIE['custom_cms_referral_code'] ) ? sanitize_text_field( $_COOKIE['custom_cms_referral_code'] ) : '';

        // Display the field
        include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/checkout-field.php';
    }

    /**
     * Add referral field to MasterStudy LMS checkout
     */
    public function add_referral_field_to_lms_checkout() {
        // Similar to WooCommerce checkout but for MasterStudy LMS
        $show_field = apply_filters( 'custom_cms_referral_show_lms_checkout_field', true );

        if ( ! $show_field ) {
            return;
        }

        // Get referral code from cookie if exists
        $referral_code = isset( $_COOKIE['custom_cms_referral_code'] ) ? sanitize_text_field( $_COOKIE['custom_cms_referral_code'] ) : '';

        // Display the field
        include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/lms-checkout-field.php';
    }

    /**
     * Get user referral code
     *
     * @param int $user_id User ID
     * @return string|false Referral code or false if not found
     */
    public function get_user_referral_code( $user_id = 0 ) {
        // Get the referral codes class
        global $custom_cms_referral_codes;

        // If the class is available, use it
        if (isset($custom_cms_referral_codes) && is_object($custom_cms_referral_codes)) {
            return $custom_cms_referral_codes->get_user_referral_code($user_id);
        }

        // Fallback to direct method if the class isn't available
        // If no user ID specified, get current user
        if ( ! $user_id ) {
            $user_id = get_current_user_id();
        }

        // If still no user ID, return false
        if ( ! $user_id ) {
            return false;
        }

        // Get user referral code from user meta
        $referral_code = get_user_meta( $user_id, 'custom_cms_referral_code', true );

        // If no code found, return false
        if ( empty( $referral_code ) ) {
            return false;
        }

        return $referral_code;
    }

    /**
     * Get referral statistics for a user
     *
     * @param int $user_id User ID
     * @return array Referral statistics
     */
    public function get_user_referral_stats( $user_id = 0 ) {
        // Note: Full implementation will be in Block 2 of Task 5

        // If no user ID specified, get current user
        if ( ! $user_id ) {
            $user_id = get_current_user_id();
        }

        // If still no user ID, return empty stats
        if ( ! $user_id ) {
            return array(
                'total_referrals' => 0,
                'successful_referrals' => 0,
                'pending_referrals' => 0,
                'total_earnings' => 0,
                'current_points' => 0
            );
        }

        // Placeholder implementation until Block 2 is complete
        // In Block 2, this will query the database for real stats
        return array(
            'total_referrals' => 0,
            'successful_referrals' => 0,
            'pending_referrals' => 0,
            'total_earnings' => 0,
            'current_points' => 0
        );
    }

    /**
     * Process referral code from AJAX request
     */
    public function process_referral_code() {
        // Check nonce - support both naming conventions for backward compatibility
        $nonce = isset($_POST['nonce']) ? $_POST['nonce'] : (isset($_POST['security']) ? $_POST['security'] : '');

        if (empty($nonce) || !wp_verify_nonce($nonce, 'custom-cms-referral-nonce')) {
            wp_send_json_error(array(
                'message' => __('Security check failed', 'custom-cms-referal')
            ));
            return;
        }

        // Check if code is set - support both naming conventions
        $code_param = isset($_POST['referral_code']) && !empty($_POST['referral_code']) ? 'referral_code' : 'code';

        if (!isset($_POST[$code_param]) || empty($_POST[$code_param])) {
            wp_send_json_error(array(
                'message' => __('No referral code provided', 'custom-cms-referal')
            ));
            return;
        }

        $referral_code = trim(sanitize_text_field($_POST[$code_param]));

        $this->debug_log("=== REFERRAL CODE APPLICATION STARTED ===");
        $this->debug_log("Referral code: " . $referral_code);
        $this->debug_log("User ID: " . get_current_user_id());

        // Get the referral codes class and discount handler
        global $custom_cms_referral_codes, $custom_cms_referral_discount_handler;

        // CRITICAL FIX: Ensure referral codes class is available
        if ( ! isset( $custom_cms_referral_codes ) || ! is_object( $custom_cms_referral_codes ) ) {
            // Try to initialize it if not available
            if ( class_exists( 'Custom_CMS_Referral_Codes' ) ) {
                $custom_cms_referral_codes = new Custom_CMS_Referral_Codes();
                error_log( 'CMS Referral: Initialized referral codes class in AJAX handler' );
            } else {
                error_log( 'CMS Referral: Referral codes class not available' );
                wp_send_json_error( array(
                    'message' => __( 'System error: Referral system is not available', 'custom-cms-referal' )
                ) );
                return;
            }
        }

        // CRITICAL FIX: Ensure discount handler is available
        if ( ! isset( $custom_cms_referral_discount_handler ) || ! is_object( $custom_cms_referral_discount_handler ) ) {
            // Try to initialize it if not available
            if ( class_exists( 'Custom_CMS_Referral_Discount_Handler' ) ) {
                $custom_cms_referral_discount_handler = new Custom_CMS_Referral_Discount_Handler();
                error_log( 'CMS Referral: Initialized discount handler class in AJAX handler' );
            } else {
                error_log( 'CMS Referral: Discount handler class not available' );
                wp_send_json_error( array(
                    'message' => __( 'System error: Discount system is not available', 'custom-cms-referal' )
                ) );
                return;
            }
        }

        // Get existing code from cookie if any
        $existing_code = isset($_COOKIE['custom_cms_referral_code']) ? sanitize_text_field($_COOKIE['custom_cms_referral_code']) : '';

        // If the same code is already applied, return success with discount info
        if ( $existing_code === $referral_code ) {
            $discount_info = $custom_cms_referral_discount_handler->get_active_discount();
            wp_send_json_success( array(
                'message' => sprintf(
                    __( 'Referral code %s is already applied. You will receive a %s discount.', 'custom-cms-referal' ),
                    '<strong>' . esc_html($referral_code) . '</strong>',
                    '<strong>' . esc_html($discount_info['formatted']) . '</strong>'
                ),
                'refresh' => false,
                'discount' => $discount_info
            ) );
            return;
        }

        // Validate the referral code
        $referrer_id = $custom_cms_referral_codes->validate_referral_code( $referral_code );

        if ( ! $referrer_id ) {
            // Check if any referral codes exist in the system for better error messaging
            global $wpdb;
            $codes_exist = $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code'");

            if ($codes_exist == 0) {
                wp_send_json_error( array(
                    'message' => __( 'No referral codes are available yet. Please try again later or contact support.', 'custom-cms-referal' )
                ) );
            } else {
                wp_send_json_error( array(
                    'message' => sprintf( __( 'Invalid referral code: %s. Please check the code and try again.', 'custom-cms-referal' ), $referral_code )
                ) );
            }
            return;
        }

        // Code is valid, track this referral attempt
        $custom_cms_referral_codes->get_referral_discount($referral_code);

        // CRITICAL FIX: Create tracking record in database
        $this->create_referral_tracking_record($referral_code, $referrer_id);

        // Get discount settings
        $discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
        $discount_amount = floatval(get_option('custom_cms_referral_discount_amount', 10));

        // Set the active discount using our discount handler
        $success = $custom_cms_referral_discount_handler->set_active_discount(
            $referral_code,
            $referrer_id,
            $discount_type,
            $discount_amount
        );

        if (!$success) {
            wp_send_json_error(array(
                'message' => __('Error applying discount. Please try again.', 'custom-cms-referal')
            ));
            return;
        }

        // Get the updated discount information
        $discount_info = $custom_cms_referral_discount_handler->get_active_discount();

        // Return success response
        wp_send_json_success(array(
            'message' => sprintf(
                __('Referral code %s applied successfully! You will receive a %s discount on your purchase.', 'custom-cms-referal'),
                '<strong>' . esc_html($referral_code) . '</strong>',
                '<strong>' . esc_html($discount_info['formatted']) . '</strong>'
            ),
            'refresh' => true,
            'discount' => $discount_info
        ));
    }

    /**
     * Create referral tracking record in database
     * This is the critical missing piece that connects frontend to admin tracking
     *
     * @param string $referral_code The referral code being used
     * @param int $referrer_id The user ID who owns the referral code
     */
    private function create_referral_tracking_record($referral_code, $referrer_id) {
        global $wpdb;

        // Get current user (the person using the referral code)
        $current_user_id = get_current_user_id();

        if (!$current_user_id) {
            $this->debug_log('ERROR: Cannot create tracking record - no current user');
            return false;
        }

        // Prevent self-referrals
        if ($current_user_id == $referrer_id) {
            $this->debug_log('ERROR: Cannot create tracking record - self-referral detected');
            return false;
        }

        // Get user details
        $referrer = get_userdata($referrer_id);
        $referred = get_userdata($current_user_id);

        if (!$referrer || !$referred) {
            $this->debug_log('ERROR: Cannot create tracking record - invalid user data');
            return false;
        }

        // PROBLEM 1 FIX: Check if tracking record already exists for this user
        $tracking_table = $wpdb->prefix . 'custom_cms_referral_tracking';
        $existing = $wpdb->get_var($wpdb->prepare(
            "SELECT id FROM {$tracking_table} WHERE referred_id = %d AND status IN ('pending', 'completed') ORDER BY logged_in_at DESC LIMIT 1",
            $current_user_id
        ));

        if ($existing) {
            // Check the status of the existing record
            $existing_record = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$tracking_table} WHERE id = %d",
                $existing
            ));

            if ($existing_record && $existing_record->status === 'pending') {
                // Update existing pending record with new referral code
                $this->debug_log('INFO: Updating existing pending tracking record for user ' . $current_user_id);
                $wpdb->update(
                    $tracking_table,
                    array(
                        'referrer_id' => $referrer_id,
                        'referral_code' => $referral_code,
                        'logged_in_at' => current_time('mysql')
                    ),
                    array('id' => $existing),
                    array('%d', '%s', '%s'),
                    array('%d')
                );
                return true;
            } elseif ($existing_record && $existing_record->status === 'completed') {
                // Create new pending record for completed users who want to use referral codes again
                $this->debug_log('INFO: Creating new tracking record for user with completed referrals');
                // Continue to insert new record below
            }
        }

        // Insert tracking record
        $result = $wpdb->insert(
            $tracking_table,
            array(
                'referrer_id' => $referrer_id,
                'referred_id' => $current_user_id,
                'referral_code' => $referral_code,
                'status' => 'pending',
                'logged_in_at' => current_time('mysql')
            ),
            array('%d', '%d', '%s', '%s', '%s')
        );

        if ($result) {
            $this->debug_log("SUCCESS: Created tracking record - Referrer: {$referrer->display_name} (ID: {$referrer_id}), Referred: {$referred->display_name} (ID: {$current_user_id}), Code: {$referral_code}");
            return true;
        } else {
            $this->debug_log('ERROR: Failed to create tracking record - ' . $wpdb->last_error);
            return false;
        }
    }

    /**
     * Debug logging to custom debug.log file
     *
     * @param string $message The message to log
     */
    private function debug_log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] FRONTEND: {$message}" . PHP_EOL;

        // Get the plugin root directory (go up from frontend/)
        $plugin_root = dirname(dirname(__FILE__));
        $debug_file = $plugin_root . '/debug.log';

        // Append to debug file
        file_put_contents($debug_file, $log_entry, FILE_APPEND | LOCK_EX);
    }

}
