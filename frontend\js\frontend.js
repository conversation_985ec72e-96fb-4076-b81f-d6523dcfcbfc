/**
 * Frontend JavaScript for Custom CMS Referral Plugin
 *
 * Handles functionality for the referral system on the frontend
 */

(function($) {
    'use strict';

    // Initialize after DOM is fully loaded
    $(document).ready(function() {
        initReferralSystem();

        // Check localStorage for referral code after page refresh
        restoreReferralFromLocalStorage();
    });

    /**
     * Initialize the referral system
     */
    function initReferralSystem() {
        console.log('CMS Referral: Initializing referral system');

        // First, check if we need to perform a state restoration
        // This is critical - if localStorage has a code but cookies don't, we need to restore
        checkAndRestoreReferralState();

        // Check if we have a referral input
        if ($('#referral_code').length) {
            console.log('CMS Referral: Found referral code input, setting up submission');
            // Set up the referral code submission
            setupReferralCodeSubmission();
        }

        // Set up copy button if it exists - check for both old and new selectors
        if ($('.copy-referral-code, .copy-code-btn, .copy-link-btn').length) {
            console.log('CMS Referral: Found copy buttons, setting up copy functionality');
            setupCopyButton();
        } else {
            console.log('CMS Referral: No copy buttons found');
        }

        // Restore referral code from localStorage if available
        restoreReferralFromLocalStorage();

        console.log('CMS Referral: Initialization complete');
    }

    /**
     * Check if we need to restore referral state between page loads
     */
    function checkAndRestoreReferralState() {
        // Only run if localStorage is available
        if (typeof localStorage === 'undefined') {
            return;
        }

        // Get stored code from localStorage
        const storedCode = localStorage.getItem('custom_cms_referral_code');
        const storedTimestamp = localStorage.getItem('custom_cms_referral_timestamp');

        // If we have a stored code but no cookie, add a query param to trigger a refresh
        if (storedCode && !getCookie('custom_cms_referral_code')) {
            // Only do this if we're on the checkout or cart page
            if ($('.stm_lms_checkout').length > 0 || $('.stm_lms_cart').length > 0) {
                // Set the cookie first
                setCookie('custom_cms_referral_code', storedCode, 7);

                // If the URL doesn't already have the restore parameter, add it and reload
                if (window.location.href.indexOf('restore_referral=1') === -1) {
                    // Add a timestamp to avoid caching issues
                    const separator = window.location.href.indexOf('?') > -1 ? '&' : '?';
                    window.location.href = window.location.href + separator + 'restore_referral=1&t=' + Date.now();
                }
            }
        }
    }

    /**
     * Set up referral code submission
     */
    function setupReferralCodeSubmission() {
        $('#custom-cms-referral-form').on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $message = $form.find('.form-message');
            const referralCode = $form.find('#referral_code').val();

            // Validate referral code
            if (!referralCode || referralCode.trim() === '') {
                $message.addClass('error').text(custom_cms_referral_params.i18n.form_error);
                return false;
            }

            // Clear any previous messages
            $message.removeClass('error success').text('');

            // Submit form via AJAX
            $.ajax({
                url: custom_cms_referral_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_apply_discount',
                    referral_code: referralCode, // Standardized parameter name
                    nonce: custom_cms_referral_params.nonce, // Standardized nonce parameter
                    security: custom_cms_referral_params.nonce // Also send as security for backward compatibility
                },
                beforeSend: function() {
                    $form.find('button').prop('disabled', true);
                    $message.text('Processing...').removeClass('error success');
                },
                success: function(response) {
                    if (response.success) {
                        // Show success message with dynamic discount info - use html() instead of text() to render HTML
                        $message.addClass('success').html(response.data.message);

                        // Store in localStorage for persistence
                        if (typeof localStorage !== 'undefined') {
                            localStorage.setItem('custom_cms_referral_code', referralCode);
                            localStorage.setItem('custom_cms_referral_timestamp', Date.now());

                            // Store discount info if available
                            if (response.data.discount) {
                                localStorage.setItem('custom_cms_referral_discount', JSON.stringify(response.data.discount));
                            }
                        }

                        // Show the active discount notice without refreshing
                        if (!$('.stm-lms-referral-applied-notice').length) {
                            // Create dynamic notice with discount info
                            displayActiveReferralNotice(referralCode, response.data.discount);
                        }

                        // Keep the referral code in the input field (don't clear it)

                        // Show a brief message before refreshing
                        $message.addClass('success').html('<strong>Discount applied!</strong> Refreshing page to update prices...');

                        // Wait a moment to show the success message before refreshing
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500); // 1.5 second delay before refresh
                    } else {
                        $message.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $message.addClass('error').text('Error processing request. Please try again.');
                },
                complete: function() {
                    $form.find('button').prop('disabled', false);
                }
            });
        });
    }

    /**
     * Set up copy button using clipboard.js library
     */
    function setupCopyButton() {
        console.log('CMS Referral: Setting up copy button functionality');

        // Check if ClipboardJS is available
        if (typeof ClipboardJS === 'undefined') {
            console.error('CMS Referral: ClipboardJS library not loaded, using fallback only');
            setupFallbackCopy();
            return;
        }

        console.log('CMS Referral: ClipboardJS library is available');

        // Common function to handle the clipboard setup
        function setupClipboard(selector) {
            const $buttons = $(selector);
            console.log('CMS Referral: Found ' + $buttons.length + ' copy buttons with selector: ' + selector);

            if ($buttons.length) {
                const clipboard = new ClipboardJS(selector, {
                    text: function(trigger) {
                        console.log('CMS Referral: Getting text to copy from trigger:', trigger);

                        // First try to get text from data-clipboard-text attribute
                        const clipboardText = $(trigger).attr('data-clipboard-text');
                        if (clipboardText) {
                            console.log('CMS Referral: Using data-clipboard-text:', clipboardText);
                            return clipboardText;
                        }

                        // Fallback: get value from associated input field
                        const $container = $(trigger).closest('.referral-code-container, .referral-link-container');
                        const $input = $container.find('.referral-code, .referral-link');
                        if ($input.length) {
                            const inputValue = $input.val();
                            console.log('CMS Referral: Using input field value:', inputValue);
                            return inputValue;
                        }

                        // Last fallback: try to get text content
                        const textContent = $(trigger).text() || '';
                        console.log('CMS Referral: Using text content fallback:', textContent);
                        return textContent;
                    }
                });

                clipboard.on('success', function(e) {
                    // Add visual feedback by briefly changing the button style
                    const $button = $(e.trigger);
                    const originalText = $button.html();

                    // Add success class and change icon
                    $button.addClass('copying').html('<i class="fas fa-check"></i>');

                    // Show success message
                    showCopyMessage(custom_cms_referral_params.i18n.copy_success, 'success');

                    // Reset button style after a delay
                    setTimeout(function() {
                        $button.removeClass('copying').html(originalText);
                    }, 1500);

                    e.clearSelection();
                });

                clipboard.on('error', function(e) {
                    // Add visual feedback for error
                    const $button = $(e.trigger);
                    const originalText = $button.html();

                    // Add error class and change icon
                    $button.addClass('error').html('<i class="fas fa-times"></i>');

                    // Show error message
                    showCopyMessage(custom_cms_referral_params.i18n.copy_failed, 'error');

                    // Reset button style after a delay
                    setTimeout(function() {
                        $button.removeClass('error').html(originalText);
                    }, 1500);

                    console.error('Copy failed:', e);
                });

                return clipboard;
            }
        }

        // Set up for both code and link copying with a single selector
        setupClipboard('.copy-code-btn, .copy-link-btn');

        // Add fallback for browsers without clipboard.js support
        setupFallbackCopy();
    }

    /**
     * Set up fallback copy functionality for older browsers
     */
    function setupFallbackCopy() {
        // Add click handler for copy buttons as fallback
        $(document).on('click', '.copy-code-btn, .copy-link-btn', function(e) {
            // Only use fallback if ClipboardJS failed or isn't available
            if (typeof ClipboardJS !== 'undefined') {
                return; // Let ClipboardJS handle it
            }

            e.preventDefault();

            const $button = $(this);
            let textToCopy = '';

            // Get text to copy
            const clipboardText = $button.attr('data-clipboard-text');
            if (clipboardText) {
                textToCopy = clipboardText;
            } else {
                const $container = $button.closest('.referral-code-container, .referral-link-container');
                const $input = $container.find('.referral-code, .referral-link');
                if ($input.length) {
                    textToCopy = $input.val();
                }
            }

            if (!textToCopy) {
                showCopyMessage(custom_cms_referral_params.i18n.copy_failed);
                return;
            }

            // Try to copy using the legacy method
            if (copyToClipboardFallback(textToCopy)) {
                // Visual feedback for success
                const originalText = $button.html();
                $button.addClass('copying').html('<i class="fas fa-check"></i>');
                showCopyMessage(custom_cms_referral_params.i18n.copy_success, 'success');

                setTimeout(function() {
                    $button.removeClass('copying').html(originalText);
                }, 1500);
            } else {
                // Visual feedback for failure
                const originalText = $button.html();
                $button.addClass('error').html('<i class="fas fa-times"></i>');
                showCopyMessage(custom_cms_referral_params.i18n.copy_failed, 'error');

                setTimeout(function() {
                    $button.removeClass('error').html(originalText);
                }, 1500);
            }
        });
    }

    /**
     * Fallback copy method for older browsers
     *
     * @param {string} text Text to copy
     * @return {boolean} Success status
     */
    function copyToClipboardFallback(text) {
        // Create a temporary textarea element
        const $temp = $('<textarea>');
        $temp.val(text);
        $temp.css({
            position: 'fixed',
            top: '-1000px',
            left: '-1000px',
            opacity: 0
        });

        $('body').append($temp);

        try {
            // Select the text
            $temp[0].select();
            $temp[0].setSelectionRange(0, 99999); // For mobile devices

            // Copy the text
            const successful = document.execCommand('copy');

            // Remove the temporary element
            $temp.remove();

            return successful;
        } catch (err) {
            console.error('Fallback copy failed:', err);
            $temp.remove();
            return false;
        }
    }

    // Note: We're using clipboard.js library for copy functionality with fallback support

    /**
     * Show copy success/failure message
     *
     * @param {string} message Message to display
     * @param {string} type Message type ('success' or 'error')
     */
    function showCopyMessage(message, type) {
        // Remove any existing copy messages
        $('.copy-message').remove();

        const $message = $('<div class="copy-message"></div>');
        $message.text(message);

        // Add type class if provided
        if (type) {
            $message.addClass(type);
        }

        $('body').append($message);

        // Show the message with animation
        setTimeout(function() {
            $message.addClass('show');
        }, 10);

        // Hide and remove the message after delay
        setTimeout(function() {
            $message.removeClass('show');
            setTimeout(function() {
                $message.remove();
            }, 300);
        }, 2500);
    }

    /**
     * Initialize referral form submission
     */
    function initializeReferralForm() {
        $('#referral-code-form').on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $button = $form.find('button[type="submit"]');
            const $message = $form.find('.message');
            const referralCode = $form.find('input[name="referral_code"]').val();

            // Basic validation
            if (!referralCode || referralCode.trim() === '') {
                $message.addClass('error').text('Please enter a valid referral code.');
                return false;
            }

            // Clear previous messages
            $message.removeClass('error success').empty();

            // Submit via AJAX
            $.ajax({
                url: custom_cms_referral_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_apply_discount',
                    referral_code: referralCode,
                    nonce: custom_cms_referral_params.nonce,
                    security: custom_cms_referral_params.nonce // Also send as security for backward compatibility
                },
                beforeSend: function() {
                    $button.prop('disabled', true).text('Processing...');
                },
                success: function(response) {
                    if (response.success) {
                        $message.addClass('success').text(response.data.message);

                        // Store in localStorage for persistence
                        if (typeof localStorage !== 'undefined') {
                            localStorage.setItem('custom_cms_referral_code', referralCode);
                            localStorage.setItem('custom_cms_referral_timestamp', Date.now());

                            // Store discount info if available
                            if (response.data.discount) {
                                localStorage.setItem('custom_cms_referral_discount', JSON.stringify(response.data.discount));
                            }
                        }

                        // Set cookie
                        setCookie('custom_cms_referral_code', referralCode, 7);

                        // Reload to show applied discount
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        $message.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $message.addClass('error').text('Error processing request. Please try again.');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Apply');
                }
            });
        });
    }

    /**
     * Initialize LMS checkout referral code application
     */
    function initializeLmsReferralCode() {
        const $checkoutReferralForm = $('#lms-checkout-referral-form');

        if (!$checkoutReferralForm.length) {
            return;
        }

        // Handle submission
        $checkoutReferralForm.on('submit', function(e) {
            e.preventDefault();

            const $form = $(this);
            const $button = $form.find('button[type="submit"]');
            const $message = $form.find('.message');
            const referralCode = $form.find('input[name="referral_code"]').val();

            // Basic validation
            if (!referralCode || referralCode.trim() === '') {
                $message.addClass('error').text('Please enter a valid referral code.');
                return false;
            }

            // Clear previous messages
            $message.removeClass('error success').empty();

            // Submit via AJAX
            $.ajax({
                url: custom_cms_referral_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'custom_cms_referral_apply_discount',
                    referral_code: referralCode,
                    nonce: custom_cms_referral_params.nonce,
                    security: custom_cms_referral_params.nonce // Also send as security for backward compatibility
                },
                beforeSend: function() {
                    $button.prop('disabled', true).text('Processing...');
                },
                success: function(response) {
                    if (response.success) {
                        $message.addClass('success').text(response.data.message);

                        // Store in localStorage for persistence
                        if (typeof localStorage !== 'undefined') {
                            localStorage.setItem('custom_cms_referral_code', referralCode);
                            localStorage.setItem('custom_cms_referral_timestamp', Date.now());

                            // Store discount info if available
                            if (response.data.discount) {
                                localStorage.setItem('custom_cms_referral_discount', JSON.stringify(response.data.discount));
                            }
                        }

                        // Set cookie
                        setCookie('custom_cms_referral_code', referralCode, 7);

                        // Reload to show applied discount
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        $message.addClass('error').text(response.data.message);
                    }
                },
                error: function() {
                    $message.addClass('error').text('Error processing request. Please try again.');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Apply');
                }
            });
        });
    }

    /**
     * Parse URL query parameters
     *
     * @return {Object} Object containing query parameters
     */
    function getQueryParams() {
        const params = {};
        const queryString = window.location.search.substring(1);
        const pairs = queryString.split('&');

        for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split('=');
            if (pair[0]) {
                params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
            }
        }

        return params;
    }

    /**
     * Set a cookie
     *
     * @param {string} name Cookie name
     * @param {string} value Cookie value
     * @param {number} days Days until expiration
     */
    function setCookie(name, value, days) {
        let expires = '';

        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = '; expires=' + date.toUTCString();
        }

        document.cookie = name + '=' + (value || '') + expires + '; path=/';
    }

    /**
     * Get cookie value
     *
     * @param {string} name Cookie name
     * @return {string|null} Cookie value or null if not found
     */
    function getCookie(name) {
        const nameEQ = name + '=';
        const ca = document.cookie.split(';');

        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') {
                c = c.substring(1, c.length);
            }

            if (c.indexOf(nameEQ) === 0) {
                return c.substring(nameEQ.length, c.length);
            }
        }

        return null;
    }

    /**
     * Display active referral notice with discount information
     *
     * @param {string} referralCode The active referral code
     * @param {object} discountInfo Discount information object
     */
    function displayActiveReferralNotice(referralCode, discountInfo) {
        // Check if we're on the checkout page or cart page
        const isCheckoutPage = $('.stm_lms_checkout').length > 0;
        const isCartPage = $('.stm_lms_cart').length > 0;

        if (!isCheckoutPage && !isCartPage) {
            return;
        }

        // Create an informative notice about the referral code with discount info
        const noticeElement = $('<div class="stm-lms-referral-applied-notice"></div>');

        // Add nice styling
        noticeElement.css({
            'background-color': '#e6f7e8',
            'border-left': '4px solid #46b450',
            'padding': '12px 15px',
            'margin-bottom': '20px',
            'border-radius': '3px',
            'font-size': '14px'
        });

        // Format the discount text
        let discountText = '';
        if (discountInfo && discountInfo.formatted) {
            discountText = ' with a <strong>' + discountInfo.formatted + '</strong> discount';
        }

        // Set the notice content with discount details
        noticeElement.html(
            '<strong>Referral Code Applied!</strong><br>' +
            'Code: <b>' + referralCode + '</b>' + discountText
        );

        // Add to page
        if (isCheckoutPage) {
            $('.stm_lms_checkout').prepend(noticeElement);
        } else if (isCartPage) {
            $('.stm_lms_cart').prepend(noticeElement);
        }
    }

    /**
     * Restore referral code from localStorage after page refresh
     */
    function restoreReferralFromLocalStorage() {
        // Check if we have localStorage available
        if (typeof localStorage === 'undefined') {
            return;
        }

        // Check if we have a referral code stored in localStorage
        const referralCode = localStorage.getItem('custom_cms_referral_code');

        if (!referralCode) {
            return;
        }

        // Check if we already have a cookie set
        const cookieCode = getCookie('custom_cms_referral_code');

        // Always ensure the cookie is set if we have localStorage data
        // This ensures consistency between browser storage mechanisms
        if (!cookieCode) {
            setCookie('custom_cms_referral_code', referralCode, 7);
        }

        // Check if we have discount info stored
        let discountInfo = null;
        try {
            const storedDiscount = localStorage.getItem('custom_cms_referral_discount');
            if (storedDiscount) {
                discountInfo = JSON.parse(storedDiscount);
            }
        } catch (e) {
            console.error('Error parsing discount info:', e);
        }

        // If we don't have discount info in localStorage, fetch it from the server
        if (!discountInfo) {
            // Use our global discount handler to get current discount settings
            // Since we can't make an AJAX call here directly, we'll use default settings
            discountInfo = {
                type: 'percentage',
                amount: 10,
                formatted: '10%'
            };
        }

        // Use the displayActiveReferralNotice function to show the notice
        if (!$('.stm-lms-referral-applied-notice').length) {
            displayActiveReferralNotice(referralCode, discountInfo);
        }
    }

})(jQuery);
