<?php
/**
 * WooCommerce Checkout Field Template
 *
 * Displays the referral code field on WooCommerce checkout.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}
?>
<div class="custom-cms-referral-checkout-field">
    <h3><?php _e( 'Referral Code', 'custom-cms-referal' ); ?></h3>
    
    <div class="woocommerce-referral-code-field">
        <p class="form-row form-row-wide" id="referral_code_field">
            <label for="referral_code"><?php _e( 'If you have a referral code, please enter it here', 'custom-cms-referal' ); ?></label>
            <span class="woocommerce-input-wrapper">
                <input type="text" class="input-text" name="referral_code" id="referral_code" placeholder="<?php esc_attr_e( 'Enter code', 'custom-cms-referal' ); ?>" value="<?php echo esc_attr( $referral_code ); ?>" />
            </span>
            
            <?php if ( ! empty( $referral_code ) ): ?>
            <span class="referral-code-applied">
                <?php _e( 'A referral code has been applied.', 'custom-cms-referal' ); ?>
            </span>
            <?php endif; ?>
        </p>
    </div>
</div>
