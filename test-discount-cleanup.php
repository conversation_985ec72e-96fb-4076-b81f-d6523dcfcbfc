<?php
/**
 * Test script for discount cleanup functionality
 * 
 * This script can be used to manually test the discount cleanup implementation.
 * Place this file in the plugin root and access it via browser to test.
 * 
 * IMPORTANT: Remove this file after testing!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // For testing purposes, we'll allow direct access
    // In production, this should be removed
    define('ABSPATH', dirname(__FILE__) . '/../../../../');
    require_once(ABSPATH . 'wp-config.php');
}

// Only allow testing for administrators
if (!current_user_can('manage_options')) {
    wp_die('Access denied. Administrator privileges required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Discount Cleanup Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Discount Cleanup Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Check Current Discount State</h2>
        <button onclick="checkDiscountState()">Check Current State</button>
        <div id="state-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Simulate Discount Application</h2>
        <button onclick="simulateDiscountApplication()">Apply Test Discount</button>
        <div id="apply-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Trigger Cleanup</h2>
        <button onclick="triggerCleanup()">Trigger Cleanup</button>
        <div id="cleanup-result" class="log"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Check localStorage</h2>
        <button onclick="checkLocalStorage()">Check localStorage</button>
        <button onclick="clearLocalStorage()">Clear localStorage</button>
        <div id="localstorage-result" class="log"></div>
    </div>

    <script>
        function checkDiscountState() {
            const result = document.getElementById('state-result');
            result.innerHTML = 'Checking discount state...\n';
            
            // Check localStorage
            const localCode = localStorage.getItem('custom_cms_referral_code');
            const localDiscount = localStorage.getItem('custom_cms_referral_discount');
            const localTimestamp = localStorage.getItem('custom_cms_referral_timestamp');
            
            result.innerHTML += `localStorage:\n`;
            result.innerHTML += `  - referral_code: ${localCode || 'NOT SET'}\n`;
            result.innerHTML += `  - discount: ${localDiscount || 'NOT SET'}\n`;
            result.innerHTML += `  - timestamp: ${localTimestamp || 'NOT SET'}\n\n`;
            
            // Check cookies
            const cookies = document.cookie.split(';');
            const referralCookie = cookies.find(c => c.trim().startsWith('custom_cms_referral_code='));
            const discountCookie = cookies.find(c => c.trim().startsWith('custom_cms_referral_discount='));
            
            result.innerHTML += `Cookies:\n`;
            result.innerHTML += `  - referral_code: ${referralCookie || 'NOT SET'}\n`;
            result.innerHTML += `  - discount: ${discountCookie || 'NOT SET'}\n\n`;
            
            // Make AJAX call to check server state
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=check_discount_state&nonce=<?php echo wp_create_nonce('discount_test'); ?>'
            })
            .then(response => response.json())
            .then(data => {
                result.innerHTML += `Server State:\n`;
                result.innerHTML += `  - Response: ${JSON.stringify(data, null, 2)}\n`;
            })
            .catch(error => {
                result.innerHTML += `Server State: ERROR - ${error}\n`;
            });
        }
        
        function simulateDiscountApplication() {
            const result = document.getElementById('apply-result');
            result.innerHTML = 'Applying test discount...\n';
            
            // Set localStorage
            localStorage.setItem('custom_cms_referral_code', 'TEST123');
            localStorage.setItem('custom_cms_referral_timestamp', Date.now());
            localStorage.setItem('custom_cms_referral_discount', JSON.stringify({
                type: 'percentage',
                amount: 10,
                formatted: '10%'
            }));
            
            // Set cookies
            document.cookie = 'custom_cms_referral_code=TEST123; path=/; max-age=604800';
            document.cookie = 'custom_cms_referral_discount=test_discount; path=/; max-age=604800';
            
            result.innerHTML += 'Test discount applied to localStorage and cookies.\n';
            result.innerHTML += 'Check current state to verify.\n';
        }
        
        function triggerCleanup() {
            const result = document.getElementById('cleanup-result');
            result.innerHTML = 'Triggering cleanup...\n';
            
            // Call the global cleanup function if available
            if (typeof window.customCmsReferralClearData === 'function') {
                window.customCmsReferralClearData();
                result.innerHTML += 'Called window.customCmsReferralClearData()\n';
            } else {
                result.innerHTML += 'window.customCmsReferralClearData not available\n';
            }
            
            // Make AJAX call to trigger server-side cleanup
            fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=trigger_discount_cleanup&nonce=<?php echo wp_create_nonce('discount_test'); ?>'
            })
            .then(response => response.json())
            .then(data => {
                result.innerHTML += `Server cleanup result: ${JSON.stringify(data, null, 2)}\n`;
            })
            .catch(error => {
                result.innerHTML += `Server cleanup error: ${error}\n`;
            });
        }
        
        function checkLocalStorage() {
            const result = document.getElementById('localstorage-result');
            result.innerHTML = 'localStorage contents:\n';
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.includes('custom_cms_referral')) {
                    const value = localStorage.getItem(key);
                    result.innerHTML += `  ${key}: ${value}\n`;
                }
            }
            
            if (result.innerHTML === 'localStorage contents:\n') {
                result.innerHTML += '  No referral data found\n';
            }
        }
        
        function clearLocalStorage() {
            localStorage.removeItem('custom_cms_referral_code');
            localStorage.removeItem('custom_cms_referral_timestamp');
            localStorage.removeItem('custom_cms_referral_discount');
            
            document.getElementById('localstorage-result').innerHTML = 'localStorage cleared manually\n';
        }
    </script>
</body>
</html>

<?php
// Add AJAX handlers for testing
add_action('wp_ajax_check_discount_state', 'test_check_discount_state');
add_action('wp_ajax_trigger_discount_cleanup', 'test_trigger_discount_cleanup');

function test_check_discount_state() {
    if (!wp_verify_nonce($_POST['nonce'], 'discount_test')) {
        wp_die('Invalid nonce');
    }
    
    global $custom_cms_referral_discount_handler;
    
    $response = array(
        'has_active_discount' => false,
        'discount_flag' => get_option('_custom_cms_discount_already_applied', 0),
        'user_id' => get_current_user_id(),
        'transients' => array(),
        'user_meta' => array()
    );
    
    if ($custom_cms_referral_discount_handler) {
        $response['has_active_discount'] = $custom_cms_referral_discount_handler->has_active_discount();
        $response['active_discount'] = $custom_cms_referral_discount_handler->get_active_discount();
    }
    
    // Check transients
    $user_id = get_current_user_id();
    if ($user_id) {
        $response['transients']['discount'] = get_transient('custom_cms_referral_discount_' . $user_id);
        $response['transients']['cart_total'] = get_transient('stm_lms_cart_total_' . $user_id);
    }
    
    wp_send_json_success($response);
}

function test_trigger_discount_cleanup() {
    if (!wp_verify_nonce($_POST['nonce'], 'discount_test')) {
        wp_die('Invalid nonce');
    }
    
    global $custom_cms_referral_discount_handler;
    
    if ($custom_cms_referral_discount_handler) {
        $user_id = get_current_user_id();
        $custom_cms_referral_discount_handler->clear_all_discount_data($user_id);
        wp_send_json_success(array('message' => 'Cleanup triggered successfully'));
    } else {
        wp_send_json_error(array('message' => 'Discount handler not available'));
    }
}
?>
