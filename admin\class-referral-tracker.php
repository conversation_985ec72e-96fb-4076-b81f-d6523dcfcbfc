<?php
/**
 * Referral Tracker Class
 *
 * This file contains all referral tracking functionality for the Custom CMS Referral Plugin.
 * It handles database operations, admin interface logic, tracking, and AJAX handlers.
 * Following the simple approach like class-discount-handler.php - everything in one file.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Custom_CMS_Referral_Tracker Class
 *
 * Handles all referral tracking functionality including:
 * - Database operations (CRUD)
 * - Admin interface logic
 * - Purchase tracking
 * - AJAX handlers
 * - Statistics and reporting
 *
 * @since 1.0.0
 */
class Custom_CMS_Referral_Tracker {

    /**
     * Instance of this class.
     *
     * @since 1.0.0
     * @var object
     */
    protected static $instance = null;

    /**
     * Database table names
     *
     * @since 1.0.0
     * @var array
     */
    private $tables;

    /**
     * Constructor for the referral tracker class.
     *
     * @since 1.0.0
     */
    public function __construct() {
        // Initialize table names
        $this->init_tables();

        // Register AJAX handlers
        $this->register_ajax_handlers();

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Return an instance of this class.
     *
     * @since 1.0.0
     * @return object A single instance of this class.
     */
    public static function get_instance() {
        if ( null == self::$instance ) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * Initialize database table names
     *
     * @since 1.0.0
     */
    private function init_tables() {
        global $wpdb;

        $this->tables = array(
            'referrals'    => $wpdb->prefix . 'mst_referrals',
            'rewards'      => $wpdb->prefix . 'mst_rewards',
            'transactions' => $wpdb->prefix . 'mst_transactions',
            'settings'     => $wpdb->prefix . 'mst_settings',
            'logs'         => $wpdb->prefix . 'mst_logs'
        );
    }

    /**
     * Register AJAX handlers
     *
     * @since 1.0.0
     */
    private function register_ajax_handlers() {
        // Admin AJAX handlers
        add_action( 'wp_ajax_custom_cms_referral_get_referrals', array( $this, 'get_referrals_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_get_referral', array( $this, 'get_referral_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_update_referral', array( $this, 'update_referral_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_delete_referral', array( $this, 'delete_referral_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_bulk_action', array( $this, 'bulk_action_ajax' ) );
        add_action( 'wp_ajax_custom_cms_referral_get_stats', array( $this, 'get_stats_ajax' ) );
    }

    /**
     * Register WordPress hooks
     * Following the exact hooks from custom-referal-plugin
     *
     * @since 1.0.0
     */
    private function register_hooks() {
        // MasterStudy LMS integration hooks - exact same as reference plugin
        add_action( 'masterstudy_lms_order_completed', array( $this, 'track_order_completion' ), 10, 4 );
        add_action( 'user_register', array( $this, 'track_user_registration' ), 10, 1 );

        // PROBLEM 2 FIX: Hook into order status changes for real-time referral status updates
        // Use a delayed hook that fires AFTER referral records are created
        add_action( 'transition_post_status', array( $this, 'handle_order_status_change_delayed' ), 99, 3 );

        // Additional MasterStudy LMS specific hook for order status changes
        add_action( 'stm_lms_order_accepted', array( $this, 'handle_masterstudy_order_status_change' ), 99, 2 );

        // Additional hooks for referral code tracking
        add_action( 'wp_ajax_apply_referral_code', array( $this, 'apply_referral_code_ajax' ) );
        add_action( 'wp_ajax_nopriv_apply_referral_code', array( $this, 'apply_referral_code_ajax' ) );

        // WooCommerce integration hooks (if needed)
        add_action( 'woocommerce_order_status_completed', array( $this, 'track_wc_order_completion' ), 10, 1 );
    }

    // ========================================
    // DATABASE OPERATIONS
    // ========================================

    /**
     * Get referrals from database with search and pagination
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array Referrals data
     */
    public function get_referrals( $args = array() ) {
        global $wpdb;

        // Default arguments
        $defaults = array(
            'search'     => '',
            'status'     => '',
            'page'       => 1,
            'per_page'   => 20,
            'orderby'    => 'id',
            'order'      => 'DESC'
        );

        $args = wp_parse_args( $args, $defaults );

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        if ( ! empty( $args['search'] ) ) {
            $where_conditions[] = "(referrer_name LIKE %s OR referred_name LIKE %s OR referral_code LIKE %s)";
            $search_term = '%' . $wpdb->esc_like( $args['search'] ) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        if ( ! empty( $args['status'] ) ) {
            $where_conditions[] = "status = %s";
            $where_values[] = $args['status'];
        }

        $where_clause = '';
        if ( ! empty( $where_conditions ) ) {
            $where_clause = 'WHERE ' . implode( ' AND ', $where_conditions );
        }

        // Calculate offset
        $offset = ( $args['page'] - 1 ) * $args['per_page'];

        // Build query
        $query = "SELECT * FROM {$this->tables['referrals']} {$where_clause} ORDER BY {$args['orderby']} {$args['order']} LIMIT %d OFFSET %d";
        $where_values[] = $args['per_page'];
        $where_values[] = $offset;

        // Execute query
        if ( ! empty( $where_values ) ) {
            $referrals = $wpdb->get_results( $wpdb->prepare( $query, $where_values ) );
        } else {
            $referrals = $wpdb->get_results( $query );
        }

        // Get total count for pagination
        $count_query = "SELECT COUNT(*) FROM {$this->tables['referrals']} {$where_clause}";
        if ( ! empty( $where_conditions ) ) {
            $count_values = array_slice( $where_values, 0, -2 ); // Remove LIMIT and OFFSET values
            $total_items = $wpdb->get_var( $wpdb->prepare( $count_query, $count_values ) );
        } else {
            $total_items = $wpdb->get_var( $count_query );
        }

        return array(
            'referrals'   => $referrals,
            'total_items' => $total_items,
            'total_pages' => ceil( $total_items / $args['per_page'] )
        );
    }

    /**
     * Get single referral by ID
     *
     * @since 1.0.0
     * @param int $referral_id Referral ID
     * @return object|null Referral data or null if not found
     */
    public function get_referral( $referral_id ) {
        global $wpdb;

        return $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->tables['referrals']} WHERE id = %d",
            $referral_id
        ) );
    }

    /**
     * Insert new referral record
     *
     * @since 1.0.0
     * @param array $data Referral data
     * @return int|false Insert ID on success, false on failure
     */
    public function insert_referral( $data ) {
        global $wpdb;

        $this->debug_log( 'insert_referral called with data: ' . print_r( $data, true ) );

        $defaults = array(
            'referrer_id'   => 0,
            'referrer_name' => '',
            'referred_id'   => 0,
            'referred_name' => '',
            'referral_code' => '',
            'course_id'     => 0,
            'course_name'   => '', // MISSING FIELD ADDED
            'order_id'      => 0,
            'status'        => 'pending',
            'amount'        => 0.00,
            'points_awarded' => 0,
            'created_at'    => function_exists( 'current_time' ) ? current_time( 'mysql' ) : date( 'Y-m-d H:i:s' ),
            'updated_at'    => function_exists( 'current_time' ) ? current_time( 'mysql' ) : date( 'Y-m-d H:i:s' )
        );

        // Manual merge instead of wp_parse_args
        $merged_data = array();
        foreach ( $defaults as $key => $default_value ) {
            $merged_data[$key] = isset( $data[$key] ) ? $data[$key] : $default_value;
        }

        $this->debug_log( 'Merged data for insertion: ' . print_r( $merged_data, true ) );
        $this->debug_log( 'Target table: ' . $this->tables['referrals'] );

        $result = $wpdb->insert(
            $this->tables['referrals'],
            $merged_data,
            array( '%d', '%s', '%d', '%s', '%s', '%d', '%s', '%d', '%s', '%f', '%d', '%s', '%s' ) // FIXED: Added %s for course_name
        );

        $this->debug_log( 'Database insert result: ' . ( $result ? 'SUCCESS' : 'FAILED' ) );
        if ( ! $result ) {
            $this->debug_log( 'Database error: ' . $wpdb->last_error );
            $this->debug_log( 'Last query: ' . $wpdb->last_query );
        }

        return $result ? $wpdb->insert_id : false;
    }

    /**
     * Update referral record
     *
     * @since 1.0.0
     * @param int $referral_id Referral ID
     * @param array $data Update data
     * @return bool True on success, false on failure
     */
    public function update_referral( $referral_id, $data ) {
        global $wpdb;

        // Add updated timestamp
        $data['updated_at'] = current_time( 'mysql' );

        $result = $wpdb->update(
            $this->tables['referrals'],
            $data,
            array( 'id' => $referral_id ),
            null, // Let WordPress determine format
            array( '%d' )
        );

        return $result !== false;
    }

    /**
     * Delete referral record
     *
     * @since 1.0.0
     * @param int $referral_id Referral ID
     * @return bool True on success, false on failure
     */
    public function delete_referral( $referral_id ) {
        global $wpdb;

        $result = $wpdb->delete(
            $this->tables['referrals'],
            array( 'id' => $referral_id ),
            array( '%d' )
        );

        return $result !== false;
    }

    /**
     * Get referral statistics
     *
     * @since 1.0.0
     * @return array Statistics data
     */
    public function get_stats() {
        global $wpdb;

        $stats = array();

        // Total referrals
        $stats['total_referrals'] = $wpdb->get_var( "SELECT COUNT(*) FROM {$this->tables['referrals']}" );

        // Pending referrals
        $stats['pending_referrals'] = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->tables['referrals']} WHERE status = %s",
            'pending'
        ) );

        // Completed referrals
        $stats['completed_referrals'] = $wpdb->get_var( $wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->tables['referrals']} WHERE status = %s",
            'completed'
        ) );

        // Total rewards given
        $stats['total_rewards'] = $wpdb->get_var( "SELECT SUM(amount) FROM {$this->tables['referrals']} WHERE status = 'completed'" ) ?: 0;

        return $stats;
    }

    // ========================================
    // AJAX HANDLERS
    // ========================================

    /**
     * AJAX handler for getting referrals
     *
     * @since 1.0.0
     */
    public function get_referrals_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get parameters
        $search = isset( $_POST['search'] ) ? sanitize_text_field( $_POST['search'] ) : '';
        $status = isset( $_POST['status'] ) ? sanitize_text_field( $_POST['status'] ) : '';
        $page = isset( $_POST['page'] ) ? absint( $_POST['page'] ) : 1;
        $per_page = isset( $_POST['per_page'] ) ? absint( $_POST['per_page'] ) : 20;

        // Get referrals
        $result = $this->get_referrals( array(
            'search'   => $search,
            'status'   => $status,
            'page'     => $page,
            'per_page' => $per_page
        ) );

        wp_send_json_success( $result );
    }

    /**
     * AJAX handler for getting single referral
     *
     * @since 1.0.0
     */
    public function get_referral_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get referral ID
        $referral_id = isset( $_POST['referral_id'] ) ? absint( $_POST['referral_id'] ) : 0;

        if ( ! $referral_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid referral ID.', 'custom-cms-referal' ) ) );
        }

        // Get referral
        $referral = $this->get_referral( $referral_id );

        if ( $referral ) {
            wp_send_json_success( $referral );
        } else {
            wp_send_json_error( array( 'message' => __( 'Referral not found.', 'custom-cms-referal' ) ) );
        }
    }

    /**
     * AJAX handler for updating referral
     *
     * @since 1.0.0
     */
    public function update_referral_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get referral ID
        $referral_id = isset( $_POST['referral_id'] ) ? absint( $_POST['referral_id'] ) : 0;

        if ( ! $referral_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid referral ID.', 'custom-cms-referal' ) ) );
        }

        // Get update data
        $data = array();
        if ( isset( $_POST['referrer_name'] ) ) {
            $data['referrer_name'] = sanitize_text_field( $_POST['referrer_name'] );
        }
        if ( isset( $_POST['referred_name'] ) ) {
            $data['referred_name'] = sanitize_text_field( $_POST['referred_name'] );
        }
        if ( isset( $_POST['referral_code'] ) ) {
            $data['referral_code'] = sanitize_text_field( $_POST['referral_code'] );
        }
        if ( isset( $_POST['status'] ) ) {
            $data['status'] = sanitize_text_field( $_POST['status'] );
        }
        if ( isset( $_POST['amount'] ) ) {
            $data['amount'] = floatval( $_POST['amount'] );
        }

        // Update referral
        $result = $this->update_referral( $referral_id, $data );

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Referral updated successfully!', 'custom-cms-referal' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to update referral.', 'custom-cms-referal' ) ) );
        }
    }

    /**
     * AJAX handler for deleting referral
     *
     * @since 1.0.0
     */
    public function delete_referral_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get referral ID
        $referral_id = isset( $_POST['referral_id'] ) ? absint( $_POST['referral_id'] ) : 0;

        if ( ! $referral_id ) {
            wp_send_json_error( array( 'message' => __( 'Invalid referral ID.', 'custom-cms-referal' ) ) );
        }

        // Delete referral
        $result = $this->delete_referral( $referral_id );

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Referral deleted successfully!', 'custom-cms-referal' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Failed to delete referral.', 'custom-cms-referal' ) ) );
        }
    }

    /**
     * AJAX handler for bulk actions
     *
     * @since 1.0.0
     */
    public function bulk_action_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        // Get action and IDs
        $action = isset( $_POST['action'] ) ? sanitize_text_field( $_POST['action'] ) : '';
        $ids = isset( $_POST['ids'] ) ? array_map( 'absint', $_POST['ids'] ) : array();

        if ( empty( $action ) || empty( $ids ) ) {
            wp_send_json_error( array( 'message' => __( 'Invalid action or no items selected.', 'custom-cms-referal' ) ) );
        }

        $success_count = 0;
        $error_count = 0;

        foreach ( $ids as $id ) {
            switch ( $action ) {
                case 'approve':
                    $result = $this->update_referral( $id, array( 'status' => 'completed' ) );
                    break;
                case 'cancel':
                    $result = $this->update_referral( $id, array( 'status' => 'cancelled' ) );
                    break;
                case 'delete':
                    $result = $this->delete_referral( $id );
                    break;
                default:
                    $result = false;
            }

            if ( $result ) {
                $success_count++;
            } else {
                $error_count++;
            }
        }

        $message = sprintf(
            __( 'Bulk action completed. %d items processed successfully, %d errors.', 'custom-cms-referal' ),
            $success_count,
            $error_count
        );

        wp_send_json_success( array( 'message' => $message ) );
    }

    /**
     * AJAX handler for getting statistics
     *
     * @since 1.0.0
     */
    public function get_stats_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Check permissions
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_send_json_error( array( 'message' => __( 'Permission denied.', 'custom-cms-referal' ) ) );
        }

        $stats = $this->get_stats();
        wp_send_json_success( $stats );
    }

    // ========================================
    // TRACKING METHODS
    // ========================================

    /**
     * Track order completion from MasterStudy LMS
     * Following the exact pattern from custom-referal-plugin
     *
     * @since 1.0.0
     * @param int $user_id User ID who made the purchase
     * @param array $cart_items Cart items purchased
     * @param string $payment_code Payment method code
     * @param int $order_id Order ID
     */
    public function track_order_completion( $user_id, $cart_items, $payment_code, $order_id ) {
        global $wpdb;

        // CRITICAL DEBUG: Log the order completion with detailed info
        $this->debug_log( '=== ORDER COMPLETION HOOK FIRED ===' );
        $this->debug_log( 'User ID: ' . $user_id );
        $this->debug_log( 'Order ID: ' . $order_id );
        $this->debug_log( 'Payment Code: ' . $payment_code );
        $this->debug_log( 'Cart Items: ' . print_r( $cart_items, true ) );

        // Log the order completion
        $this->log( 'Order completion tracked', 'info', array(
            'user_id' => $user_id,
            'order_id' => $order_id,
            'payment_code' => $payment_code,
            'cart_items' => $cart_items
        ) );

        // Check if this user was referred by someone
        $temp_table = $wpdb->prefix . 'custom_cms_referral_tracking';

        // CRITICAL DEBUG: Check what's in the tracking table
        $this->debug_log( 'Looking for tracking record for user ' . $user_id );
        $this->debug_log( 'Tracking table: ' . $temp_table );

        // Check all tracking records for debugging
        $all_tracking = $wpdb->get_results( "SELECT * FROM {$temp_table}" );
        $this->debug_log( 'All tracking records: ' . print_r( $all_tracking, true ) );

        // Look for referral tracking record for this user (including completed ones for reuse)
        $referral_record = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$temp_table} WHERE referred_id = %d AND status IN ('pending', 'completed') ORDER BY logged_in_at DESC LIMIT 1",
            $user_id
        ) );

        $this->debug_log( 'Found referral record: ' . print_r( $referral_record, true ) );

        if ( ! $referral_record ) {
            // No referral found, this is a direct purchase
            $this->debug_log( 'No referral record found for user ' . $user_id . ' - direct purchase' );
            return;
        }

        // Get referrer and referred user details
        $this->debug_log( 'Attempting to get user data...' );

        // Check if WordPress functions are available
        if ( ! function_exists( 'get_userdata' ) ) {
            $this->debug_log( 'WARNING: get_userdata function not available - using direct database query' );

            // Get user data directly from database
            $referrer = $wpdb->get_row( $wpdb->prepare(
                "SELECT ID, user_login, display_name FROM {$wpdb->users} WHERE ID = %d",
                $referral_record->referrer_id
            ) );

            $referred = $wpdb->get_row( $wpdb->prepare(
                "SELECT ID, user_login, display_name FROM {$wpdb->users} WHERE ID = %d",
                $user_id
            ) );
        } else {
            $referrer = get_userdata( $referral_record->referrer_id );
            $referred = get_userdata( $user_id );
        }

        $this->debug_log( 'User data retrieved - Referrer: ' . ( $referrer ? $referrer->display_name : 'NULL' ) . ', Referred: ' . ( $referred ? $referred->display_name : 'NULL' ) );

        if ( ! $referrer || ! $referred ) {
            $this->debug_log( 'ERROR: Failed to get user data for referral completion' );
            $this->log( 'Failed to get user data for referral completion', 'error', array(
                'referrer_id' => $referral_record->referrer_id,
                'referred_id' => $user_id
            ) );
            return;
        }

        // Get course details from cart items
        $course_names = array();
        $total_amount = 0;

        if ( is_array( $cart_items ) ) {
            foreach ( $cart_items as $item ) {
                if ( isset( $item['item_id'] ) ) {
                    // Try to get course data
                    if ( function_exists( 'get_post' ) ) {
                        $course = get_post( $item['item_id'] );
                        if ( $course ) {
                            $course_names[] = $course->post_title;
                        }
                    } else {
                        // Fallback: get course title directly from database
                        $course_title = $wpdb->get_var( $wpdb->prepare(
                            "SELECT post_title FROM {$wpdb->posts} WHERE ID = %d",
                            $item['item_id']
                        ) );
                        if ( $course_title ) {
                            $course_names[] = $course_title;
                        }
                    }
                }
                if ( isset( $item['price'] ) ) {
                    $total_amount += floatval( $item['price'] );
                }
            }
        }

        $course_name = ! empty( $course_names ) ? implode( ', ', $course_names ) : 'Unknown Course';

        $this->debug_log( 'Preparing referral data for insertion...' );
        $this->debug_log( 'Course name: ' . $course_name );
        $this->debug_log( 'Total amount: ' . $total_amount );

        // Check actual order status from WordPress
        $actual_order_status = $this->get_order_status( $order_id );
        $this->debug_log( 'Actual WordPress order status: ' . $actual_order_status );

        // Determine referral status based on actual order status
        $referral_status = ( $actual_order_status === 'completed' ) ? 'completed' : 'pending';
        $this->debug_log( 'Setting referral status to: ' . $referral_status );

        // Insert into permanent referrals table
        $referral_data = array(
            'referrer_id'   => $referral_record->referrer_id,
            'referrer_name' => $referrer->display_name,
            'referred_id'   => $user_id,
            'referred_name' => $referred->display_name,
            'referral_code' => $referral_record->referral_code,
            'course_id'     => isset( $cart_items[0]['item_id'] ) ? $cart_items[0]['item_id'] : 0,
            'course_name'   => $course_name,
            'order_id'      => $order_id,
            'status'        => $referral_status,
            'amount'        => $total_amount,
            'points_awarded' => $this->calculate_points( $total_amount ),
            'created_at'    => function_exists( 'current_time' ) ? current_time( 'mysql' ) : date( 'Y-m-d H:i:s' ),
            'updated_at'    => function_exists( 'current_time' ) ? current_time( 'mysql' ) : date( 'Y-m-d H:i:s' )
        );

        $this->debug_log( 'Calling insert_referral method...' );
        $result = $this->insert_referral( $referral_data );
        $this->debug_log( 'Insert referral result: ' . ( $result ? 'SUCCESS (ID: ' . $result . ')' : 'FAILED' ) );

        if ( $result ) {
            $this->debug_log( 'Referral record inserted successfully!' );

            // Only update temp record status if the referral is actually completed
            if ( $referral_status === 'completed' ) {
                $this->debug_log( 'Order is completed - updating temp record status...' );
                $update_result = $wpdb->update(
                    $temp_table,
                    array( 'status' => 'completed' ),
                    array( 'id' => $referral_record->id ),
                    array( '%s' ),
                    array( '%d' )
                );
                $this->debug_log( 'Temp record update result: ' . ( $update_result !== false ? 'SUCCESS' : 'FAILED' ) );

                // Award points to referrer (only for completed orders)
                $this->debug_log( 'Awarding points to referrer...' );
                $this->award_points( $referral_record->referrer_id, $referral_data['points_awarded'] );
            } else {
                $this->debug_log( 'Order is pending - keeping temp record as pending for potential reuse' );
            }

            $this->debug_log( 'REFERRAL TRACKING SUCCESSFUL!' );
            $this->log( 'Referral tracked successfully', 'info', array(
                'referral_id' => $result,
                'referrer' => $referrer->display_name,
                'referred' => $referred->display_name,
                'course' => $course_name,
                'amount' => $total_amount,
                'status' => $referral_status
            ) );
        } else {
            $this->debug_log( 'ERROR: Failed to insert referral record' );
            $this->log( 'Failed to insert referral record', 'error', $referral_data );
        }
    }

    /**
     * Track user registration and assign referral code
     * Following the pattern from custom-referal-plugin
     *
     * @since 1.0.0
     * @param int $user_id User ID
     */
    public function track_user_registration( $user_id ) {
        global $wpdb;

        $user = get_userdata( $user_id );
        if ( ! $user ) {
            return;
        }

        // Generate referral code for the new user
        $referral_code = $this->generate_referral_code( $user );

        // Store the referral code in user meta (use same key as referral codes class)
        update_user_meta( $user_id, 'custom_cms_referral_code', $referral_code );

        // Create initial tracking record for this user (as potential referrer)
        $temp_table = $wpdb->prefix . 'custom_cms_referral_tracking';

        $wpdb->insert(
            $temp_table,
            array(
                'referrer_id'   => $user_id,
                'referred_id'   => 0, // Will be filled when someone uses this code
                'referral_code' => $referral_code,
                'status'        => 'active',
                'logged_in_at'  => current_time( 'mysql' )
            ),
            array( '%d', '%d', '%s', '%s', '%s' )
        );

        $this->log( 'User registration tracked and referral code generated', 'info', array(
            'user_id' => $user_id,
            'username' => $user->user_login,
            'referral_code' => $referral_code
        ) );
    }

    /**
     * Track referral code usage
     * Called when someone enters a referral code
     *
     * @since 1.0.0
     * @param string $referral_code The referral code entered
     * @param int $referred_user_id The user who entered the code
     * @return bool True if tracking was successful
     */
    public function track_referral_code_usage( $referral_code, $referred_user_id ) {
        global $wpdb;

        // Find the referrer by referral code (use same key as referral codes class)
        $referrer_id = $wpdb->get_var( $wpdb->prepare(
            "SELECT user_id FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code' AND meta_value = %s",
            $referral_code
        ) );

        if ( ! $referrer_id ) {
            $this->log( 'Invalid referral code used', 'warning', array(
                'referral_code' => $referral_code,
                'referred_user_id' => $referred_user_id
            ) );
            return false;
        }

        // Don't allow self-referral
        if ( $referrer_id == $referred_user_id ) {
            $this->log( 'Self-referral attempt blocked', 'warning', array(
                'user_id' => $referred_user_id,
                'referral_code' => $referral_code
            ) );
            return false;
        }

        // Check if this user already has a pending referral
        $temp_table = $wpdb->prefix . 'custom_cms_referral_tracking';
        $existing = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$temp_table} WHERE referred_id = %d AND status = 'pending'",
            $referred_user_id
        ) );

        if ( $existing ) {
            // Update existing record
            $wpdb->update(
                $temp_table,
                array(
                    'referrer_id'   => $referrer_id,
                    'referral_code' => $referral_code,
                    'logged_in_at'  => current_time( 'mysql' )
                ),
                array( 'id' => $existing ),
                array( '%d', '%s', '%s' ),
                array( '%d' )
            );
        } else {
            // Create new tracking record
            $wpdb->insert(
                $temp_table,
                array(
                    'referrer_id'   => $referrer_id,
                    'referred_id'   => $referred_user_id,
                    'referral_code' => $referral_code,
                    'status'        => 'pending',
                    'logged_in_at'  => current_time( 'mysql' )
                ),
                array( '%d', '%d', '%s', '%s', '%s' )
            );
        }

        $referrer = get_userdata( $referrer_id );
        $referred = get_userdata( $referred_user_id );

        $this->log( 'Referral code usage tracked', 'info', array(
            'referrer' => $referrer ? $referrer->display_name : 'Unknown',
            'referred' => $referred ? $referred->display_name : 'Unknown',
            'referral_code' => $referral_code
        ) );

        return true;
    }

    /**
     * Track WooCommerce order completion
     *
     * @since 1.0.0
     * @param int $order_id Order ID
     */
    public function track_wc_order_completion( $order_id ) {
        // This will be implemented for WooCommerce integration if needed

        // Log for debugging
        error_log( 'Referral Tracker: WooCommerce order completion tracked - Order ID: ' . $order_id );
    }

    /**
     * PROBLEM 2 FIX: Handle order status changes for real-time referral status updates
     * This method is called when any post status changes in WordPress with a delay
     *
     * @since 1.0.0
     * @param string $new_status New post status
     * @param string $old_status Old post status
     * @param WP_Post $post Post object
     */
    public function handle_order_status_change_delayed( $new_status, $old_status, $post ) {
        global $wpdb;

        // Only process if status actually changed
        if ( $new_status === $old_status ) {
            return;
        }

        // Check if this is a MasterStudy LMS order post type
        if ( ! $this->is_masterstudy_order( $post ) ) {
            return;
        }

        $order_id = $post->ID;
        $this->debug_log( "=== ORDER STATUS CHANGE DETECTED ===" );
        $this->debug_log( "Order ID: {$order_id}" );
        $this->debug_log( "Old Status: {$old_status}" );
        $this->debug_log( "New Status: {$new_status}" );

        // Find referral records for this order
        $referrals = $wpdb->get_results( $wpdb->prepare(
            "SELECT * FROM {$this->tables['referrals']} WHERE order_id = %d",
            $order_id
        ) );

        if ( empty( $referrals ) ) {
            $this->debug_log( "No referral records found for order {$order_id}" );
            return;
        }

        $this->debug_log( "Found " . count( $referrals ) . " referral record(s) for order {$order_id}" );

        // Update referral status based on new order status
        foreach ( $referrals as $referral ) {
            $this->update_referral_status_from_order( $referral, $old_status, $new_status );
        }
    }

    /**
     * Handle MasterStudy LMS specific order status changes
     * This method is called after order is accepted/completed
     * Based on the actual MasterStudy LMS hook signature: stm_lms_order_accepted($user_id, $cart_items)
     *
     * @since 1.0.0
     * @param int $user_id User ID who made the purchase
     * @param array $cart_items Cart items purchased
     */
    public function handle_masterstudy_order_status_change( $user_id, $cart_items ) {
        $this->debug_log( "=== MASTERSTUDY ORDER STATUS CHANGE HOOK ===" );
        $this->debug_log( "User ID: {$user_id}" );
        $this->debug_log( "Cart Items: " . print_r( $cart_items, true ) );

        // Since order_id is not passed directly, we need to find recent orders for this user
        global $wpdb;

        // Get the most recent order for this user (within last 5 minutes to be safe)
        $recent_order = $wpdb->get_row( $wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts}
             WHERE post_type = 'stm-orders'
             AND post_author = %d
             AND post_date > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
             ORDER BY post_date DESC
             LIMIT 1",
            $user_id
        ) );

        if ( ! $recent_order ) {
            $this->debug_log( "No recent orders found for user {$user_id} in MasterStudy hook" );
            return;
        }

        $order_id = $recent_order->ID;
        $this->debug_log( "Found recent order ID: {$order_id}" );

        // Get current order status
        $current_status = $this->get_order_status( $order_id );
        $this->debug_log( "Current order status: {$current_status}" );

        // Find referral records for this order
        $referrals = $wpdb->get_results( $wpdb->prepare(
            "SELECT * FROM {$this->tables['referrals']} WHERE order_id = %d",
            $order_id
        ) );

        if ( empty( $referrals ) ) {
            $this->debug_log( "No referral records found for order {$order_id} in MasterStudy hook" );
            return;
        }

        $this->debug_log( "Found " . count( $referrals ) . " referral record(s) for order {$order_id} in MasterStudy hook" );

        // Update referral status based on current order status
        foreach ( $referrals as $referral ) {
            $new_referral_status = $this->map_order_status_to_referral_status( $current_status );

            if ( $referral->status !== $new_referral_status ) {
                $this->debug_log( "Updating referral {$referral->id} from {$referral->status} to {$new_referral_status}" );

                $update_result = $this->update_referral( $referral->id, array(
                    'status' => $new_referral_status
                ) );

                if ( $update_result ) {
                    $this->handle_points_on_status_change( $referral, $referral->status, $new_referral_status );
                    $this->debug_log( "Successfully updated referral {$referral->id} via MasterStudy hook" );
                } else {
                    $this->debug_log( "Failed to update referral {$referral->id} via MasterStudy hook" );
                }
            } else {
                $this->debug_log( "Referral {$referral->id} status already correct: {$new_referral_status}" );
            }
        }
    }

    /**
     * Check if a post is a MasterStudy LMS order
     *
     * @since 1.0.0
     * @param WP_Post $post Post object
     * @return bool True if it's a MasterStudy order
     */
    private function is_masterstudy_order( $post ) {
        // Check for MasterStudy LMS order post types
        $masterstudy_order_types = array(
            'stm-orders',           // Common MasterStudy order post type
            'stm_lms_order',        // Alternative post type
            'masterstudy_order',    // Another possible post type
        );

        if ( in_array( $post->post_type, $masterstudy_order_types ) ) {
            $this->debug_log( "Detected MasterStudy order post type: {$post->post_type}" );
            return true;
        }

        // Additional check: look for MasterStudy-specific meta fields
        $masterstudy_meta_keys = array(
            '_stm_lms_order_type',
            '_stm_order_items',
            'stm_lms_order_total',
        );

        foreach ( $masterstudy_meta_keys as $meta_key ) {
            if ( function_exists( 'get_post_meta' ) && get_post_meta( $post->ID, $meta_key, true ) ) {
                $this->debug_log( "Detected MasterStudy order via meta key: {$meta_key}" );
                return true;
            }
        }

        return false;
    }

    /**
     * Update referral status based on order status change
     *
     * @since 1.0.0
     * @param object $referral Referral record
     * @param string $old_order_status Old order status
     * @param string $new_order_status New order status
     */
    private function update_referral_status_from_order( $referral, $old_order_status, $new_order_status ) {
        global $wpdb;

        $this->debug_log( "Processing referral ID {$referral->id} for order status change" );

        // Determine new referral status based on order status
        $new_referral_status = $this->map_order_status_to_referral_status( $new_order_status );
        $old_referral_status = $referral->status;

        $this->debug_log( "Referral status mapping: {$new_order_status} -> {$new_referral_status}" );

        // Only update if referral status needs to change
        if ( $new_referral_status === $old_referral_status ) {
            $this->debug_log( "Referral status unchanged, no update needed" );
            return;
        }

        // Update referral status
        $update_result = $this->update_referral( $referral->id, array(
            'status' => $new_referral_status
        ) );

        if ( $update_result ) {
            $this->debug_log( "Successfully updated referral {$referral->id} status from {$old_referral_status} to {$new_referral_status}" );

            // Handle points awarding/removal
            $this->handle_points_on_status_change( $referral, $old_referral_status, $new_referral_status );

            // Log the change
            $this->log( 'Referral status updated due to order status change', 'info', array(
                'referral_id' => $referral->id,
                'order_id' => $referral->order_id,
                'old_order_status' => $old_order_status,
                'new_order_status' => $new_order_status,
                'old_referral_status' => $old_referral_status,
                'new_referral_status' => $new_referral_status
            ) );
        } else {
            $this->debug_log( "Failed to update referral {$referral->id} status" );
        }
    }

    /**
     * Map order status to referral status
     *
     * @since 1.0.0
     * @param string $order_status Order status
     * @return string Referral status
     */
    private function map_order_status_to_referral_status( $order_status ) {
        $status_mapping = array(
            'publish'   => 'completed',  // MasterStudy LMS completed orders
            'completed' => 'completed',  // WooCommerce completed orders
            'processing' => 'pending',   // WooCommerce processing orders
            'pending'   => 'pending',    // Pending orders
            'draft'     => 'pending',    // Draft orders
            'cancelled' => 'cancelled',  // Cancelled orders
            'refunded'  => 'cancelled',  // Refunded orders
            'failed'    => 'cancelled',  // Failed orders
        );

        return isset( $status_mapping[$order_status] ) ? $status_mapping[$order_status] : 'pending';
    }

    /**
     * Handle points awarding/removal when referral status changes
     *
     * @since 1.0.0
     * @param object $referral Referral record
     * @param string $old_status Old referral status
     * @param string $new_status New referral status
     */
    private function handle_points_on_status_change( $referral, $old_status, $new_status ) {
        // Award points when changing from non-completed to completed
        if ( $old_status !== 'completed' && $new_status === 'completed' ) {
            $this->debug_log( "Awarding points for referral completion" );
            $this->award_points( $referral->referrer_id, $referral->points_awarded );
        }
        // Remove points when changing from completed to non-completed
        elseif ( $old_status === 'completed' && $new_status !== 'completed' ) {
            $this->debug_log( "Removing points for referral status change" );
            $this->remove_points( $referral->referrer_id, $referral->points_awarded );
        }
    }

    /**
     * Remove points from user (opposite of award_points)
     *
     * @since 1.0.0
     * @param int $user_id User ID
     * @param int $points Points to remove
     */
    private function remove_points( $user_id, $points ) {
        global $wpdb;

        if ( $points <= 0 ) {
            return;
        }

        // Insert negative points record
        $wpdb->insert(
            $this->tables['rewards'],
            array(
                'user_id'    => $user_id,
                'points'     => -$points, // Negative points to remove
                'type'       => 'referral_reversal',
                'status'     => 'active',
                'created_at' => function_exists( 'current_time' ) ? current_time( 'mysql' ) : date( 'Y-m-d H:i:s' )
            ),
            array( '%d', '%d', '%s', '%s', '%s' )
        );

        $this->debug_log( "Removed {$points} points from user {$user_id}" );
    }

    // ========================================
    // UTILITY METHODS
    // ========================================

    /**
     * Generate referral code for user
     * Following the pattern from custom-referal-plugin
     *
     * @since 1.0.0
     * @param object $user User object
     * @return string Generated referral code
     */
    public function generate_referral_code( $user ) {
        global $wpdb;

        // Clean username (remove special characters)
        $clean_username = preg_replace( '/[^a-zA-Z0-9]/', '', $user->user_login );
        $clean_username = strtolower( $clean_username );

        // Get next number for this username (use same key as referral codes class)
        $existing_codes = $wpdb->get_col( $wpdb->prepare(
            "SELECT meta_value FROM {$wpdb->usermeta} WHERE meta_key = 'custom_cms_referral_code' AND meta_value LIKE %s",
            $clean_username . '%'
        ) );

        $user_number = 1;
        if ( ! empty( $existing_codes ) ) {
            $numbers = array();
            foreach ( $existing_codes as $code ) {
                if ( preg_match( '/^' . preg_quote( $clean_username, '/' ) . '(\d+)$/', $code, $matches ) ) {
                    $numbers[] = intval( $matches[1] );
                }
            }
            if ( ! empty( $numbers ) ) {
                $user_number = max( $numbers ) + 1;
            }
        }

        return $clean_username . $user_number;
    }

    /**
     * Get order status from WordPress/MasterStudy LMS
     *
     * @since 1.0.0
     * @param int $order_id Order ID
     * @return string Order status
     */
    public function get_order_status( $order_id ) {
        global $wpdb;

        // Try to get order status from posts table (MasterStudy LMS orders)
        $order_status = $wpdb->get_var( $wpdb->prepare(
            "SELECT post_status FROM {$wpdb->posts} WHERE ID = %d",
            $order_id
        ) );

        if ( $order_status ) {
            $this->debug_log( 'Found order status in posts table: ' . $order_status );
            return $order_status;
        }

        // Fallback: check if it's a WooCommerce order
        if ( function_exists( 'wc_get_order' ) ) {
            $order = wc_get_order( $order_id );
            if ( $order ) {
                $status = $order->get_status();
                $this->debug_log( 'Found WooCommerce order status: ' . $status );
                return $status;
            }
        }

        // Default fallback
        $this->debug_log( 'Could not determine order status, defaulting to pending' );
        return 'pending';
    }

    /**
     * Calculate points for referral amount
     *
     * @since 1.0.0
     * @param float $amount Purchase amount
     * @return int Points to award
     */
    public function calculate_points( $amount ) {
        // Simple calculation: 10% of amount as points
        return intval( $amount * 0.1 );
    }

    /**
     * Award points to user
     *
     * @since 1.0.0
     * @param int $user_id User ID
     * @param int $points Points to award
     * @return bool Success status
     */
    public function award_points( $user_id, $points ) {
        global $wpdb;

        // Insert into rewards table
        $result = $wpdb->insert(
            $this->tables['rewards'],
            array(
                'user_id'    => $user_id,
                'points'     => $points,
                'type'       => 'referral',
                'status'     => 'active',
                'created_at' => current_time( 'mysql' )
            ),
            array( '%d', '%d', '%s', '%s', '%s' )
        );

        return $result !== false;
    }

    /**
     * AJAX handler for applying referral code
     *
     * @since 1.0.0
     */
    public function apply_referral_code_ajax() {
        // Check nonce
        check_ajax_referer( 'custom-cms-referral-nonce', 'nonce' );

        // Get referral code and user ID
        $referral_code = isset( $_POST['referral_code'] ) ? sanitize_text_field( $_POST['referral_code'] ) : '';
        $user_id = get_current_user_id();

        if ( empty( $referral_code ) ) {
            wp_send_json_error( array( 'message' => __( 'Please enter a referral code.', 'custom-cms-referal' ) ) );
        }

        if ( ! $user_id ) {
            wp_send_json_error( array( 'message' => __( 'You must be logged in to use a referral code.', 'custom-cms-referal' ) ) );
        }

        // Track the referral code usage
        $result = $this->track_referral_code_usage( $referral_code, $user_id );

        if ( $result ) {
            wp_send_json_success( array( 'message' => __( 'Referral code applied successfully!', 'custom-cms-referal' ) ) );
        } else {
            wp_send_json_error( array( 'message' => __( 'Invalid referral code or already used.', 'custom-cms-referal' ) ) );
        }
    }

    /**
     * Log activity
     *
     * @since 1.0.0
     * @param string $message Log message
     * @param string $level Log level (info, warning, error)
     * @param array $context Additional context
     */
    public function log( $message, $level = 'info', $context = array() ) {
        global $wpdb;

        $wpdb->insert(
            $this->tables['logs'],
            array(
                'level'      => $level,
                'message'    => $message,
                'context'    => json_encode( $context ),
                'user_id'    => get_current_user_id(),
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'created_at' => current_time( 'mysql' )
            ),
            array( '%s', '%s', '%s', '%d', '%s', '%s' )
        );
    }

    /**
     * Get data for admin interface
     *
     * @since 1.0.0
     * @param array $args Query arguments
     * @return array Data for admin interface
     */
    public function get_admin_data( $args = array() ) {
        // Get referrals data
        $referrals_data = $this->get_referrals( $args );

        // Get statistics
        $stats = $this->get_stats();

        return array(
            'referrals' => $referrals_data['referrals'],
            'total_items' => $referrals_data['total_items'],
            'total_pages' => $referrals_data['total_pages'],
            'stats' => $stats
        );
    }

    /**
     * Debug logging to custom debug.log file
     *
     * @param string $message The message to log
     */
    private function debug_log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[{$timestamp}] TRACKER: {$message}" . PHP_EOL;

        // Get the plugin root directory (go up from admin/)
        $plugin_root = dirname(dirname(__FILE__));
        $debug_file = $plugin_root . '/debug.log';

        // Append to debug file
        file_put_contents($debug_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Initialize the referral tracker if we're in admin
if ( is_admin() ) {
    // The class will be instantiated by the core class
    // This ensures it's only loaded when needed
}
