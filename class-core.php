<?php
/**
 * Core plugin class for Custom CMS Referral Plugin.
 *
 * This is the main class that handles the initialization of the plugin,
 * component loading, and hook registration.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Main plugin class that implements the Singleton pattern.
 */
class Custom_CMS_Referral_Core {

    /**
     * The single instance of this class.
     *
     * @var Custom_CMS_Referral_Core
     */
    private static $instance = null;

    /**
     * Database manager instance.
     *
     * @var object
     */
    public $db;

    /**
     * Admin manager instance.
     *
     * @var object
     */
    public $admin;

    /**
     * Frontend manager instance.
     *
     * @var object
     */
    public $frontend;

    /**
     * Shortcodes manager instance.
     *
     * @var object
     */
    public $shortcodes;

    /**
     * Referral system instance.
     *
     * @var object
     */
    public $referral;

    /**
     * Rewards system instance.
     *
     * @var object
     */
    public $rewards;

    /**
     * AJAX handler instance.
     *
     * @var object
     */
    public $ajax;

    /**
     * Plugin constructor.
     * Private to ensure only one instance is created.
     */
    private function __construct() {
        // Validate dependencies
        if ( ! $this->check_dependencies() ) {
            return;
        }

        // Define plugin constants
        $this->define_constants();

        // Initialize components
        $this->init_components();

        // Register hooks
        $this->register_hooks();
    }

    /**
     * Get the singleton instance of this class.
     *
     * @return Custom_CMS_Referral_Core
     */
    public static function instance() {
        if ( self::$instance === null ) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * Check required dependencies.
     *
     * @return bool True if dependencies exist, false otherwise.
     */
    private function check_dependencies() {
        // Check if MasterStudy LMS is active using a more reliable method
        if ( ! function_exists( 'is_plugin_active' ) ) {
            include_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        // Check for MasterStudy LMS using the plugin file path
        $masterstudy_plugin = 'masterstudy-lms-learning-management-system/masterstudy-lms-learning-management-system.php';

        if ( ! is_plugin_active( $masterstudy_plugin ) ) {
            add_action( 'admin_notices', array( $this, 'missing_masterstudylms_notice' ) );
            return false;
        }

        return true;
    }

    /**
     * Define additional plugin constants.
     */
    private function define_constants() {
        // Option keys
        define( 'CUSTOM_CMS_REFERAL_OPTION_PREFIX', 'mst_referral_' );
        define( 'CUSTOM_CMS_REFERAL_DB_VERSION_KEY', CUSTOM_CMS_REFERAL_OPTION_PREFIX . 'db_version' );
        define( 'CUSTOM_CMS_REFERAL_SETTINGS_KEY', CUSTOM_CMS_REFERAL_OPTION_PREFIX . 'settings' );

        // Paths and URLs
        define( 'CUSTOM_CMS_REFERAL_ADMIN_PATH', CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'admin/' );
        define( 'CUSTOM_CMS_REFERAL_ADMIN_URL', CUSTOM_CMS_REFERAL_PLUGIN_URL . 'admin/' );
        define( 'CUSTOM_CMS_REFERAL_FRONTEND_PATH', CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'frontend/' );
        define( 'CUSTOM_CMS_REFERAL_FRONTEND_URL', CUSTOM_CMS_REFERAL_PLUGIN_URL . 'frontend/' );
        define( 'CUSTOM_CMS_REFERAL_TEMPLATES_PATH', CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'templates/' );
    }

    /**
     * Referral codes handler instance
     *
     * @var object
     */
    public $referral_codes;

    /**
     * Discount handler instance
     *
     * @var object
     */
    public $discount_handler;

    /**
     * Referral tracker instance
     *
     * @var object
     */
    public $referral_tracker;

    /**
     * Initialize all plugin components.
     */
    private function init_components() {
        // Initialize the database component first (if needed)
        // $this->db = new Custom_CMS_Referral_Database();

        // Initialize the referral codes handler
        require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'includes/class-referral-codes.php';
        $this->referral_codes = new Custom_CMS_Referral_Codes();

        // CRITICAL: Initialize the referral codes hooks
        add_action('init', array($this->referral_codes, 'init'));

        // Initialize the discount handler - this is a key component for our discount functionality
        require_once CUSTOM_CMS_REFERAL_PLUGIN_DIR . 'includes/class-discount-handler.php';
        $this->discount_handler = new Custom_CMS_Referral_Discount_Handler();

        // Make the discount handler available globally
        global $custom_cms_referral_discount_handler;
        $custom_cms_referral_discount_handler = $this->discount_handler;

        // Also make the referral codes handler available globally
        global $custom_cms_referral_codes;
        $custom_cms_referral_codes = $this->referral_codes;

        // Initialize the admin component
        if ( is_admin() ) {
            require_once CUSTOM_CMS_REFERAL_ADMIN_PATH . 'admin.php';
            require_once CUSTOM_CMS_REFERAL_ADMIN_PATH . 'class-referral-tracker.php';

            // Admin class initializes itself with get_instance(), no need to call init()
            $this->admin = Custom_CMS_Referral_Admin::get_instance();

            // Initialize the referral tracker
            $this->referral_tracker = new Custom_CMS_Referral_Tracker();

            // Make the referral tracker available globally
            global $custom_cms_referral_tracker;
            $custom_cms_referral_tracker = $this->referral_tracker;
        }

        // Initialize the frontend component
        if ( ! is_admin() || wp_doing_ajax() ) {
            require_once CUSTOM_CMS_REFERAL_FRONTEND_PATH . 'frontend.php';
            require_once CUSTOM_CMS_REFERAL_FRONTEND_PATH . 'shortcodes.php';

            $this->frontend = new Custom_CMS_Referral_Frontend();
            // Explicitly call init since the Frontend class doesn't self-initialize
            add_action('init', array($this->frontend, 'init'));

            // Initialize shortcodes
            $this->shortcodes = new Custom_CMS_Referral_Shortcodes();
            add_action('init', array($this->shortcodes, 'init'));

            error_log('CMS Referral: Frontend and shortcodes initialized by core');
        }
    }

    /**
     * Register all WordPress hooks.
     */
    private function register_hooks() {
        // Register WordPress actions
        add_action( 'plugins_loaded', array( $this, 'load_textdomain' ) );

        // MasterStudy LMS integration hooks
        add_action( 'stm_lms_order_created', array( $this, 'process_lms_order' ), 10, 3 );
        add_action( 'stm_lms_user_registered', array( $this, 'process_user_registration' ), 10, 1 );
    }

    /**
     * Load plugin text domain for translations.
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'custom-cms-referal',
            false,
            dirname( plugin_basename( CUSTOM_CMS_REFERAL_PLUGIN_DIR ) ) . '/languages/'
        );
    }

    /**
     * Process MasterStudy LMS orders.
     *
     * @param int   $order_id Order ID.
     * @param array $data Order data.
     * @param int   $user_id User ID.
     */
    public function process_lms_order( $order_id, $data, $user_id ) {
        // Process order through referral tracker if available
        if ( $this->referral_tracker ) {
            $this->referral_tracker->process_order( $order_id, $data, $user_id );
        }
    }

    /**
     * Process MasterStudy LMS user registrations.
     *
     * @param int $user_id User ID.
     */
    public function process_user_registration( $user_id ) {
        // Process registration through referral tracker if available
        if ( $this->referral_tracker ) {
            $this->referral_tracker->process_registration( $user_id );
        }
    }

    /**
     * Admin notice for missing MasterStudy LMS plugin.
     */
    public function missing_masterstudylms_notice() {
        ?>
        <div class="notice notice-error">
            <p>
                <?php esc_html_e( 'Custom CMS Referral Plugin requires MasterStudy LMS to be installed and activated.', 'custom-cms-referal' ); ?>
            </p>
        </div>
        <?php
    }
}
