<?php
/**
 * Email Templates <PERSON><PERSON>
 *
 * This class handles email template functionality for the Custom CMS Referral Plugin.
 * It provides methods to get, format, and process email templates.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Email Templates Class
 */
class Custom_CMS_Referral_Email_Templates {

    /**
     * Get email sharing template with variables replaced
     *
     * @param array $variables Array of variables to replace in template
     * @return array Array with 'subject' and 'body' keys
     * @since 1.0.0
     */
    public static function get_sharing_template( $variables = array() ) {
        // Get templates from options
        $subject = get_option( 'custom_cms_referral_sharing_subject', 'Check out this course discount!' );
        $template = get_option( 'custom_cms_referral_sharing_template', 
            'Hi there!

I wanted to share this great opportunity with you. Use my referral code {referral_code} to get a discount on courses.

Click here to get started: {referral_link}

Thanks!
{user_name}' );

        // Default variables
        $default_variables = array(
            '{user_name}' => '',
            '{referral_code}' => '',
            '{referral_link}' => '',
            '{site_name}' => get_bloginfo( 'name' )
        );

        // Merge with provided variables
        $variables = wp_parse_args( $variables, $default_variables );

        // Replace variables in subject and template
        $processed_subject = self::replace_variables( $subject, $variables );
        $processed_body = self::replace_variables( $template, $variables );

        return array(
            'subject' => $processed_subject,
            'body' => $processed_body
        );
    }

    /**
     * Get invitation email template with variables replaced
     *
     * @param array $variables Array of variables to replace in template
     * @return array Array with 'subject' and 'body' keys
     * @since 1.0.0
     */
    public static function get_invitation_template( $variables = array() ) {
        // Get templates from options
        $subject = get_option( 'custom_cms_referral_invitation_subject', 'You\'ve been invited to join!' );
        $template = get_option( 'custom_cms_referral_invitation_template', 
            'Hi {friend_name},

{user_name} has invited you to check out our courses!

Use this referral code to get a discount: {referral_code}

Click here to sign up: {referral_link}

Thanks!' );

        // Default variables
        $default_variables = array(
            '{user_name}' => '',
            '{friend_name}' => '',
            '{referral_code}' => '',
            '{referral_link}' => '',
            '{site_name}' => get_bloginfo( 'name' )
        );

        // Merge with provided variables
        $variables = wp_parse_args( $variables, $default_variables );

        // Replace variables in subject and template
        $processed_subject = self::replace_variables( $subject, $variables );
        $processed_body = self::replace_variables( $template, $variables );

        return array(
            'subject' => $processed_subject,
            'body' => $processed_body
        );
    }

    /**
     * Replace variables in a string
     *
     * @param string $content Content to process
     * @param array $variables Variables to replace
     * @return string Processed content
     * @since 1.0.0
     */
    private static function replace_variables( $content, $variables ) {
        foreach ( $variables as $variable => $value ) {
            $content = str_replace( $variable, $value, $content );
        }
        return $content;
    }

    /**
     * Get email sharing URL for mailto link
     *
     * @param string $referral_code The referral code
     * @param string $referral_url The referral URL
     * @param string $user_name The user's name (optional)
     * @return string Complete mailto URL
     * @since 1.0.0
     */
    public static function get_sharing_mailto_url( $referral_code, $referral_url, $user_name = '' ) {
        // Get the email template
        $template_data = self::get_sharing_template( array(
            '{user_name}' => $user_name,
            '{referral_code}' => $referral_code,
            '{referral_link}' => $referral_url
        ) );

        // Build mailto URL
        $mailto_url = 'mailto:?subject=' . urlencode( $template_data['subject'] ) . 
                     '&body=' . urlencode( $template_data['body'] );

        return $mailto_url;
    }

    /**
     * Check if email templates are enabled
     *
     * @return bool True if enabled, false otherwise
     * @since 1.0.0
     */
    public static function are_emails_enabled() {
        return (bool) get_option( 'custom_cms_referral_enable_emails', 1 );
    }

    /**
     * Send invitation email
     *
     * @param string $to_email Recipient email address
     * @param array $variables Template variables
     * @return bool True if email was sent successfully
     * @since 1.0.0
     */
    public static function send_invitation_email( $to_email, $variables = array() ) {
        if ( ! self::are_emails_enabled() ) {
            return false;
        }

        $template_data = self::get_invitation_template( $variables );

        // Send email using WordPress wp_mail function
        $sent = wp_mail(
            $to_email,
            $template_data['subject'],
            $template_data['body']
        );

        return $sent;
    }

    /**
     * Get available template variables
     *
     * @return array Array of available variables with descriptions
     * @since 1.0.0
     */
    public static function get_available_variables() {
        return array(
            '{user_name}' => __( 'The name of the user sharing the referral', 'custom-cms-referal' ),
            '{friend_name}' => __( 'The name of the friend being invited (invitation emails only)', 'custom-cms-referal' ),
            '{referral_code}' => __( 'The referral code', 'custom-cms-referal' ),
            '{referral_link}' => __( 'The complete referral link', 'custom-cms-referal' ),
            '{site_name}' => __( 'The name of your website', 'custom-cms-referal' )
        );
    }
}
