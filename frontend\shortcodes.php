<?php
/**
 * Shortcodes Class
 *
 * Handles all shortcodes for the referral system.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Exit if accessed directly
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Shortcodes class
 */
class Custom_CMS_Referral_Shortcodes {

    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here
    }

    /**
     * Initialize shortcodes
     */
    public function init() {
        // Register main shortcode
        add_shortcode( 'mst_referral', array( $this, 'referral_shortcode' ) );
    }

    /**
     * Main referral shortcode
     *
     * @param array $atts Shortcode attributes
     * @return string Shortcode output
     */
    public function referral_shortcode( $atts ) {
        // Parse attributes
        $atts = shortcode_atts( array(
            'type' => 'full', // Options: full, link-only, form-only
            'theme' => 'default', // Theme for styling
        ), $atts, 'mst_referral' );

        // Start output buffer
        ob_start();

        // Get referral code format settings to pass to the view
        $code_prefix = get_option('custom_cms_referral_code_prefix', 'MST-');
        $code_length = (int) get_option('custom_cms_referral_code_length', 8);

        // Initialize variable for pre-filling the referral code input field (e.g., from a URL parameter)
        $referral_code_for_input = '';
        if (isset($_GET['ref'])) {
            $referral_code_for_input = sanitize_text_field($_GET['ref']);
        }

        // Check if user is logged in
        if ( is_user_logged_in() ) {
            // Get frontend class
            $frontend = new Custom_CMS_Referral_Frontend();

            // Get user's referral code
            $referral_code = $frontend->get_user_referral_code();

            // If user has a code, show referral box
            if ( $referral_code ) {
                // Get referral URL
                $referral_url = add_query_arg( 'ref', $referral_code, home_url() );

                // Get user stats
                $user_stats = $frontend->get_user_referral_stats();

                // Include the appropriate templates based on 'type'
                if ( $atts['type'] === 'full' || $atts['type'] === 'link-only' ) {
                    include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/referral-box.php';
                }

                if ( $atts['type'] === 'full' ) {
                    include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/referral-stats.php';
                }

                // Show the referral form for all users, regardless of login status
                if ( $atts['type'] === 'full' || $atts['type'] === 'form-only' ) {
                    include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/referral-form.php';
                }
            } else {
                // User doesn't have a referral code yet
                echo '<div class="custom-cms-referral-notice">';
                echo __( 'You don\'t have a referral code yet. Make a purchase to get one!', 'custom-cms-referal' );
                echo '</div>';
            }
        } else {
            // Not logged in, show only the form
            if ( $atts['type'] === 'full' || $atts['type'] === 'form-only' ) {
                include CUSTOM_CMS_REFERAL_PLUGIN_FRONTEND_DIR . 'views/referral-form.php';
            }
        }

        // Get the buffer and clean it
        $output = ob_get_clean();

        return $output;
    }
}
