<?php
/**
 * Database deactivator for Custom CMS Referral Plugin.
 *
 * Handles the cleanup of database tables and plugin data when the plugin is deactivated.
 * Removes all traces of the plugin from the WordPress database.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Class responsible for cleaning up all plugin data on deactivation.
 */
class Custom_CMS_Referral_Deactivator {

    /**
     * The main deactivation method - runs when the plugin is deactivated.
     * Removes all database tables and plugin data.
     *
     * @return void
     */
    public static function deactivate() {
        self::drop_tables();
        self::remove_options();
        self::remove_transients();
        self::remove_cron_jobs();
    }
    
    /**
     * Drop all plugin database tables.
     *
     * @return void
     */
    private static function drop_tables() {
        global $wpdb;
        
        // List of tables to drop
        $tables = [
            $wpdb->prefix . 'mst_referrals',
            $wpdb->prefix . 'mst_rewards',
            $wpdb->prefix . 'mst_transactions',
            $wpdb->prefix . 'mst_coupons',
            $wpdb->prefix . 'mst_settings',
            $wpdb->prefix . 'mst_logs',
            $wpdb->prefix . 'custom_cms_referral_tracking'
        ];
        
        // Drop each table
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }
    
    /**
     * Remove all plugin options from the WordPress options table.
     *
     * @return void
     */
    private static function remove_options() {
        // List of option names to remove
        $options = [
            'mst_referral_db_version',
            'mst_referral_activated_time',
            'mst_referral_settings',
            

        ];
        
        // Remove each option
        foreach ($options as $option) {
            delete_option($option);
        }
    }
    
    /**
     * Remove all plugin transients.
     *
     * @return void
     */
    private static function remove_transients() {
        global $wpdb;
        
        // Delete all transients containing 'mst_referral'
        $wpdb->query(
            "DELETE FROM $wpdb->options 
            WHERE option_name LIKE '%_transient_mst_referral%' 
            OR option_name LIKE '%_transient_timeout_mst_referral%'"
        );
    }
    
    /**
     * Remove any scheduled cron jobs for the plugin.
     *
     * @return void
     */
    private static function remove_cron_jobs() {
        // Clear any scheduled hooks
        wp_clear_scheduled_hook('mst_referral_daily_cron');
        wp_clear_scheduled_hook('mst_referral_weekly_cron');
        wp_clear_scheduled_hook('mst_referral_monthly_cron');
    }
}
