<?php
/**
 * Admin Helper Functions
 *
 * This file contains helper functions for the admin panel.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Check if MasterStudy LMS is active
 *
 * @since 1.0.0
 * @return bool Whether MasterStudy LMS is active
 */
function custom_cms_referral_is_masterstudy_active() {
    // Check various MasterStudy LMS classes
    $ms_classes = array(
        'STM_LMS_Course',
        'STM_LMS_User',
        'STM_LMS_Courses',
        'STM_LMS_Cart',
        'Stm_Lms_Popular_Courses',
        'STM_LMS_Lesson',
        'STM_LMS_Quiz',
        'STM_LMS_Order',
        'STM_LMS_Reviews'
    );
    
    foreach ($ms_classes as $class) {
        if (class_exists($class)) {
            return true;
        }
    }
    
    // Check if the plugin file exists as a fallback
    $plugin_file = WP_PLUGIN_DIR . '/masterstudy-lms-learning-management-system/masterstudy-lms-learning-management-system.php';
    if (file_exists($plugin_file)) {
        return true;
    }
    
    // Check if the plugin is activated using WordPress function
    if (function_exists('is_plugin_active')) {
        return is_plugin_active('masterstudy-lms-learning-management-system/masterstudy-lms-learning-management-system.php');
    }
    
    return false;
}

/**
 * Check if the database tables are created
 *
 * @since 1.0.0
 * @return bool Whether database tables are created
 */
function custom_cms_referral_check_database_tables() {
    global $wpdb;
    
    $required_tables = array(
        $wpdb->prefix . 'mst_referrals',
        $wpdb->prefix . 'mst_rewards',
        $wpdb->prefix . 'mst_transactions',
        $wpdb->prefix . 'mst_settings'
    );
    
    $existing_tables = $wpdb->get_col("SHOW TABLES");
    
    foreach ($required_tables as $table) {
        if (!in_array($table, $existing_tables)) {
            return false;
        }
    }
    
    return true;
}

/**
 * Fix quick action links URLs in dashboard
 *
 * @since 1.0.0
 * @return array Array of quick action links
 */
function custom_cms_referral_get_quick_actions() {
    return array(
        array(
            'title' => __('Manage Referrals', 'custom-cms-referal'),
            'url' => admin_url('admin.php?page=custom-cms-referral-referrals'),
            'icon' => 'dashicons-list-view'
        ),
        array(
            'title' => __('Settings', 'custom-cms-referal'),
            'url' => admin_url('admin.php?page=custom-cms-referral-settings'),
            'icon' => 'dashicons-admin-settings'
        ),
        array(
            'title' => __('View Shortcode', 'custom-cms-referal'),
            'url' => '#',
            'icon' => 'dashicons-share',
            'onclick' => 'alert("[mst_referral] - Insert this shortcode on any page to display the referral form.");return false;'
        )
    );
}
