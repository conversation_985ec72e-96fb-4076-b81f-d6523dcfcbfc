<?php
/**
 * Admin Settings UI
 *
 * This file displays the settings page for the Custom CMS Referral Plugin.
 * It includes general settings, discount management, and more.
 *
 * @package Custom_CMS_Referral
 * @since 1.0.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check user permissions
if ( ! current_user_can( 'manage_options' ) ) {
    return;
}

// Settings are registered in admin.php

// Get active tab
$active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'general';

// Get saved settings
$discount_type = get_option('custom_cms_referral_discount_type', 'percentage');
$discount_amount = get_option('custom_cms_referral_discount_amount', 10);
$usage_limit = get_option('custom_cms_referral_usage_limit', 1);

// Get general settings
$enable_referral_system = get_option('custom_cms_referral_enable_system', 1);
$cookie_duration = get_option('custom_cms_referral_cookie_duration', 30);
$code_prefix = get_option('custom_cms_referral_code_prefix', 'MST-');
$enable_emails = get_option('custom_cms_referral_enable_emails', 1);

// Get email template settings - using the correct option names that are actually saved
$sharing_subject = get_option('custom_cms_referral_sharing_subject', 'Check out this course discount!');
$sharing_template = get_option('custom_cms_referral_sharing_template', 'Hi there!

I wanted to share this great opportunity with you. Use my referral code {referral_code} to get a discount on courses.

Click here to get started: {referral_link}

Thanks!
{user_name}');

// CRITICAL FIX: Ensure default values are set if they don't exist
if (empty($sharing_subject) || $sharing_subject === false) {
    $sharing_subject = 'Check out this course discount!';
    update_option('custom_cms_referral_sharing_subject', $sharing_subject);
}

if (empty($sharing_template) || $sharing_template === false) {
    $sharing_template = 'Hi there!

I wanted to share this great opportunity with you. Use my referral code {referral_code} to get a discount on courses.

Click here to get started: {referral_link}

Thanks!
{user_name}';
    update_option('custom_cms_referral_sharing_template', $sharing_template);
}


?>

<div class="wrap custom-cms-referal-settings">
    <h1><?php _e('Referral Settings', 'custom-cms-referal'); ?></h1>

    <!-- Settings Tabs -->
    <h2 class="nav-tab-wrapper">
        <a href="?page=custom-cms-referral-settings&tab=general" class="nav-tab <?php echo $active_tab == 'general' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-admin-generic"></span> <?php _e('General', 'custom-cms-referal'); ?>
        </a>
        <a href="?page=custom-cms-referral-settings&tab=discounts" class="nav-tab <?php echo $active_tab == 'discounts' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-groups"></span> <?php _e('Referrals', 'custom-cms-referal'); ?>
        </a>
        <a href="?page=custom-cms-referral-settings&tab=emails" class="nav-tab <?php echo $active_tab == 'emails' ? 'nav-tab-active' : ''; ?>">
            <span class="dashicons dashicons-email"></span> <?php _e('Email Templates', 'custom-cms-referal'); ?>
        </a>
    </h2>

    <?php settings_errors(); ?>

    <form method="post" id="custom-cms-referral-settings-form">
        <?php
        // Create nonce for AJAX security
        wp_nonce_field('custom-cms-referral-nonce', 'settings_nonce');

        // Add hidden field to maintain current tab after saving
        echo '<input type="hidden" name="active_tab" value="' . esc_attr($active_tab) . '" />';

        if ($active_tab == 'general'): ?>
            <!-- General Settings Tab -->
            <div id="general-settings" class="settings-section">
                <h2><?php _e('General Settings', 'custom-cms-referal'); ?></h2>
                <p class="description"><?php _e('Configure general referral system settings.', 'custom-cms-referal'); ?></p>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_referral_system"><?php _e('Enable Referral System', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <label class="switch">
                                <input type="checkbox" id="enable_referral_system" name="enable_referral_system" value="1" <?php checked($enable_referral_system, 1); ?>>
                                <span class="slider round"></span>
                            </label>
                            <p class="description"><?php _e('Turn the referral system on or off.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="cookie_duration"><?php _e('Cookie Duration (Days)', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="cookie_duration" name="cookie_duration" value="<?php echo esc_attr($cookie_duration); ?>" min="1" max="365" class="small-text">
                            <p class="description"><?php _e('How many days to store referral cookies.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="code_prefix"><?php _e('Referral Code Prefix', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="code_prefix" name="code_prefix" value="<?php echo esc_attr($code_prefix); ?>" class="regular-text">
                            <p class="description"><?php _e('Prefix to add to referral codes (optional).', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

        <?php elseif ($active_tab == 'discounts'): ?>
            <!-- Discounts Tab -->
            <div id="discount-settings" class="settings-section">
                <h2><?php _e('Discount & Referral Management', 'custom-cms-referal'); ?></h2>
                <p class="description"><?php _e('Configure discount and referral tracking settings.', 'custom-cms-referal'); ?></p>

                <div id="settings-message" class="notice" style="display:none;"></div>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="discount_type"><?php _e('Discount Type', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <select id="discount_type" name="discount_type" class="discount-type-select">
                                <option value="percentage" <?php selected($discount_type, 'percentage'); ?>><?php _e('Percentage', 'custom-cms-referal'); ?></option>
                                <option value="fixed" <?php selected($discount_type, 'fixed'); ?>><?php _e('Fixed Amount', 'custom-cms-referal'); ?></option>
                            </select>
                            <p class="description"><?php _e('Choose whether the discount is a percentage or fixed amount.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="discount_amount"><?php _e('Discount Amount', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <div class="discount-amount-wrapper">
                                <input type="number" id="discount_amount" name="discount_amount" value="<?php echo esc_attr($discount_amount); ?>" min="0" step="0.01" class="small-text">
                                <span id="discount-symbol"><?php echo $discount_type == 'percentage' ? '%' : '₹'; ?></span>
                            </div>
                            <p class="description"><?php _e('The amount of discount to apply for successful referrals.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="usage_limit"><?php _e('Usage Limit', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="usage_limit" name="usage_limit" value="<?php echo esc_attr($usage_limit); ?>" min="0" class="small-text">
                            <p class="description"><?php _e('Maximum number of times a referral code can be used (0 for unlimited).', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>
                </table>

            </div>

        <?php elseif ($active_tab == 'emails'): ?>
            <!-- Email Templates Tab -->
            <div id="email-settings" class="settings-section">
                <h2><?php _e('Email Templates', 'custom-cms-referal'); ?></h2>
                <p class="description"><?php _e('Configure email notification templates for referral sharing and notifications.', 'custom-cms-referal'); ?></p>

                <!-- Email System Settings -->
                <h3><?php _e('Email System Settings', 'custom-cms-referal'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="enable_emails"><?php _e('Enable Email Notifications', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <label class="switch">
                                <input type="checkbox" id="enable_emails" name="enable_emails" value="1" <?php checked($enable_emails, 1); ?>>
                                <span class="slider round"></span>
                            </label>
                            <p class="description"><?php _e('Send email notifications for referral events and enable email sharing functionality.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>
                </table>

                <!-- Email Sharing Template -->
                <h3><?php _e('Email Sharing Template', 'custom-cms-referal'); ?></h3>
                <p class="description"><?php _e('This template is used when users share their referral code via email from the frontend.', 'custom-cms-referal'); ?></p>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="sharing_subject"><?php _e('Email Subject', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="sharing_subject" name="sharing_subject" value="<?php echo esc_attr($sharing_subject); ?>" class="regular-text">
                            <p class="description"><?php _e('Subject line for email sharing.', 'custom-cms-referal'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="sharing_template"><?php _e('Email Body Template', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <textarea id="sharing_template" name="sharing_template" rows="8" class="large-text code"><?php echo esc_textarea($sharing_template); ?></textarea>
                            <p class="description">
                                <?php _e('Available variables:', 'custom-cms-referal'); ?>
                                <code>{user_name}</code>, <code>{referral_code}</code>, <code>{referral_link}</code>, <code>{site_name}</code>
                            </p>
                        </td>
                    </tr>
                </table>



                <!-- Template Preview Section -->
                <h3><?php _e('Template Preview', 'custom-cms-referal'); ?></h3>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label><?php _e('Preview Email Sharing Template', 'custom-cms-referal'); ?></label>
                        </th>
                        <td>
                            <button type="button" id="preview_sharing_template" class="button button-secondary">
                                <?php _e('Preview Sharing Template', 'custom-cms-referal'); ?>
                            </button>
                            <div id="sharing_template_preview" class="email-preview" style="display: none; margin-top: 10px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9;">
                                <h4><?php _e('Email Preview:', 'custom-cms-referal'); ?></h4>
                                <div id="sharing_preview_content"></div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($active_tab == 'discounts'): ?>
        <p class="submit">
            <input type="button" name="save_discount_settings" id="save_discount_settings" class="button button-primary" value="<?php esc_attr_e('Save Discount Settings', 'custom-cms-referal'); ?>">
            <span class="spinner" style="float: none; margin-top: 0;" id="discount-settings-spinner"></span>
        </p>
        <?php elseif ($active_tab == 'emails'): ?>
        <p class="submit">
            <button type="button" id="save_email_settings" class="button button-primary"><?php _e('Save Email Settings', 'custom-cms-referal'); ?></button>
        </p>
        <?php else: ?>
        <p class="submit">
            <input type="submit" name="submit" id="submit" class="button button-primary" value="<?php esc_attr_e('Save Changes', 'custom-cms-referal'); ?>">
        </p>
        <?php endif; ?>
    </form>

    <!-- Plugin Footer -->
    <div class="plugin-footer">
        <div class="footer-content">
            <div class="footer-left">
                <p class="plugin-info">
                    <strong>Custom CMS Referral Plugin</strong> - Version <?php echo CUSTOM_CMS_REFERAL_PLUGIN_VERSION; ?>
                </p>
            </div>
            <div class="footer-right">
                <p class="thank-you-message">
                    Thank you for creating with <a href="https://wordpress.org" target="_blank">WordPress</a>
                </p>
            </div>
        </div>
    </div>
</div>
