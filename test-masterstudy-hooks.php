<?php
/**
 * Test script to verify MasterStudy LMS ADMIN order status synchronization
 *
 * This script tests the integration approach by hooking into MasterStudy LMS
 * ADMIN order status changes (when admin manually changes status in admin panel)
 * and verifying we receive the correct data.
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== MasterStudy LMS Order Status Synchronization Test ===\n";
echo "Current time: " . current_time('mysql') . "\n\n";

// Test data storage
$test_results = array(
    'hooks_registered' => false,
    'save_post_fired' => false,
    'meta_updated_fired' => false,
    'data_received' => array(),
    'errors' => array()
);

/**
 * Test hook for save_post (MasterStudy admin order status changes)
 */
function test_save_post_hook($post_id, $post, $update) {
    global $test_results;

    // Only process MasterStudy orders
    if ($post->post_type !== 'stm-orders') {
        return;
    }

    echo "🎉 HOOK FIRED: save_post for MasterStudy order\n";
    echo "Post ID: {$post_id}\n";
    echo "Post Type: {$post->post_type}\n";
    echo "Update: " . ($update ? 'true' : 'false') . "\n";

    // Get order status from POST data (simulating admin form submission)
    $status = isset($_POST['order_status']) ? sanitize_text_field($_POST['order_status']) : '';
    $user_id = get_post_meta($post_id, 'user_id', true);
    $previous_status = get_post_meta($post_id, 'status', true);

    echo "Status: {$status}\n";
    echo "User ID: {$user_id}\n";
    echo "Previous Status: {$previous_status}\n";

    $test_results['save_post_fired'] = true;
    $test_results['data_received']['save_post'] = array(
        'post_id' => $post_id,
        'status' => $status,
        'user_id' => $user_id,
        'previous_status' => $previous_status,
        'timestamp' => date('Y-m-d H:i:s')
    );

    // Test referral status update logic
    if ($previous_status !== $status && !empty($user_id)) {
        echo "Testing referral status update for status change: {$previous_status} → {$status}\n";
        test_referral_status_update_by_order($post_id, $user_id, $status);
    }
}

/**
 * Test hook for updated_post_meta (when order status meta is updated)
 */
function test_updated_post_meta_hook($meta_id, $post_id, $meta_key, $meta_value) {
    global $test_results;

    // Only process status meta updates for MasterStudy orders
    if ($meta_key !== 'status' || get_post_type($post_id) !== 'stm-orders') {
        return;
    }

    echo "🎉 HOOK FIRED: updated_post_meta for order status\n";
    echo "Post ID: {$post_id}\n";
    echo "Meta Key: {$meta_key}\n";
    echo "New Status: {$meta_value}\n";

    $user_id = get_post_meta($post_id, 'user_id', true);
    echo "User ID: {$user_id}\n";

    $test_results['meta_updated_fired'] = true;
    $test_results['data_received']['meta_updated'] = array(
        'post_id' => $post_id,
        'meta_key' => $meta_key,
        'meta_value' => $meta_value,
        'user_id' => $user_id,
        'timestamp' => date('Y-m-d H:i:s')
    );

    // Test referral status update logic
    echo "Testing referral status update for meta update: {$meta_value}\n";
    test_referral_status_update_by_order($post_id, $user_id, $meta_value);
}

/**
 * Test referral status update logic by order
 */
function test_referral_status_update_by_order($order_id, $user_id, $new_status) {
    global $wpdb;

    echo "  → Testing referral status update by order...\n";
    echo "  → Order ID: {$order_id}\n";
    echo "  → User ID: {$user_id}\n";
    echo "  → New Status: {$new_status}\n";

    // Get order items from post meta
    $cart_items = get_post_meta($order_id, 'items', true);
    if (!$cart_items) {
        echo "  → No cart items found for order {$order_id}\n";
        return;
    }

    echo "  → Cart Items: " . print_r($cart_items, true) . "\n";

    // Call the original test function
    test_referral_status_update($user_id, $cart_items, $new_status);
}

/**
 * Test referral status update logic
 */
function test_referral_status_update($user_id, $cart_items, $new_status) {
    global $wpdb;
    
    echo "  → Searching for referrals to update...\n";
    echo "  → User ID: {$user_id}\n";
    echo "  → New Status: {$new_status}\n";
    
    // Check if we have referrals for this user
    $referrals_table = $wpdb->prefix . 'mst_referrals';
    
    $referrals = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$referrals_table} WHERE referred_id = %d OR referrer_id = %d",
        $user_id,
        $user_id
    ));
    
    if ($referrals) {
        echo "  → Found " . count($referrals) . " referral(s) for user {$user_id}\n";
        foreach ($referrals as $referral) {
            echo "    - Referral ID: {$referral->id}, Current Status: {$referral->status}\n";
            
            // Simulate status update
            if ($referral->status !== $new_status) {
                echo "    - Would update referral {$referral->id} from '{$referral->status}' to '{$new_status}'\n";
            } else {
                echo "    - Referral {$referral->id} already has status '{$new_status}'\n";
            }
        }
    } else {
        echo "  → No referrals found for user {$user_id}\n";
    }
    
    // Check cart items for course-specific referrals
    if (is_array($cart_items)) {
        foreach ($cart_items as $item) {
            $course_id = isset($item['item_id']) ? $item['item_id'] : (isset($item['course_id']) ? $item['course_id'] : null);
            if ($course_id) {
                echo "  → Checking course-specific referrals for course {$course_id}\n";
                
                $course_referrals = $wpdb->get_results($wpdb->prepare(
                    "SELECT * FROM {$referrals_table} WHERE course_id = %d AND (referred_id = %d OR referrer_id = %d)",
                    $course_id,
                    $user_id,
                    $user_id
                ));
                
                if ($course_referrals) {
                    echo "    - Found " . count($course_referrals) . " course-specific referral(s)\n";
                } else {
                    echo "    - No course-specific referrals found\n";
                }
            }
        }
    }
}

/**
 * Register test hooks for admin order status changes
 */
function register_test_hooks() {
    global $test_results;

    echo "Registering test hooks for ADMIN order status changes...\n";

    // Register hooks for MasterStudy LMS ADMIN order status changes
    add_action('save_post', 'test_save_post_hook', 20, 3);  // Same priority as MasterStudy
    add_action('updated_post_meta', 'test_updated_post_meta_hook', 10, 4);

    $test_results['hooks_registered'] = true;
    echo "✅ Admin hooks registered successfully!\n";
    echo "   - save_post (priority 20, same as MasterStudy)\n";
    echo "   - updated_post_meta (priority 10)\n\n";
}

/**
 * Test if MasterStudy LMS is active and hooks exist
 */
function test_masterstudy_availability() {
    echo "Testing MasterStudy LMS availability...\n";
    
    // Check if MasterStudy LMS is active
    if (!class_exists('STM_LMS_Order')) {
        echo "❌ ERROR: MasterStudy LMS Order class not found!\n";
        echo "   Make sure MasterStudy LMS plugin is active.\n";
        return false;
    }
    
    echo "✅ MasterStudy LMS Order class found\n";
    
    // Check if the hooks are available by looking at registered actions
    global $wp_filter;
    
    $hooks_to_check = array('stm_lms_order_accepted', 'stm_lms_order_remove');
    foreach ($hooks_to_check as $hook) {
        if (isset($wp_filter[$hook])) {
            echo "✅ Hook '{$hook}' is available\n";
        } else {
            echo "⚠️  Hook '{$hook}' not currently registered (this is normal before orders are processed)\n";
        }
    }
    
    return true;
}

/**
 * Create a test order to trigger hooks
 */
function create_test_order() {
    echo "\nCreating test order to trigger hooks...\n";
    
    // Check if we can create a test order
    if (!class_exists('STM_LMS_Order')) {
        echo "❌ Cannot create test order - STM_LMS_Order class not available\n";
        return false;
    }
    
    // Create test order data
    $test_order_data = array(
        'user_id' => 1,
        'cart_items' => array(
            array(
                'item_id' => 100,
                'price' => 50.00,
                'quantity' => 1
            )
        ),
        'payment_code' => 'test',
        '_order_total' => 50.00,
        '_order_currency' => 'USD'
    );
    
    echo "Test order data prepared:\n";
    echo print_r($test_order_data, true) . "\n";
    
    // Note: We won't actually create the order to avoid side effects
    echo "⚠️  Test order creation skipped to avoid side effects\n";
    echo "   To test hooks, manually change an order status in MasterStudy LMS admin\n";
    
    return true;
}

/**
 * Manual hook trigger for testing admin order status changes
 */
function trigger_test_hooks() {
    echo "\nTriggering test hooks manually for ADMIN order status changes...\n";

    // Create a test order post to simulate admin changes
    echo "Creating test MasterStudy order...\n";

    $test_order_data = array(
        'post_type' => 'stm-orders',
        'post_title' => 'Test Order #12345',
        'post_status' => 'publish',
        'meta_input' => array(
            'user_id' => 1,
            'status' => 'pending',
            'items' => array(
                array(
                    'item_id' => 100,
                    'price' => 50.00,
                    'quantity' => 1
                )
            )
        )
    );

    $order_id = wp_insert_post($test_order_data);

    if ($order_id && !is_wp_error($order_id)) {
        echo "✅ Test order created with ID: {$order_id}\n";

        // Simulate admin changing order status to completed
        echo "\nSimulating admin changing order status to 'completed'...\n";
        $_POST['order_status'] = 'completed';

        // Trigger save_post hook (simulating admin form submission)
        $post = get_post($order_id);
        do_action('save_post', $order_id, $post, true);

        // Simulate meta update (what MasterStudy does internally)
        echo "\nSimulating meta update to 'completed'...\n";
        update_post_meta($order_id, 'status', 'completed');

        // Clean up
        unset($_POST['order_status']);
        wp_delete_post($order_id, true);
        echo "✅ Test order cleaned up\n";

    } else {
        echo "❌ Failed to create test order\n";
    }
}

/**
 * Display test results
 */
function display_test_results() {
    global $test_results;
    
    echo "\n=== TEST RESULTS ===\n";
    echo "Hooks Registered: " . ($test_results['hooks_registered'] ? '✅ YES' : '❌ NO') . "\n";
    echo "Save Post Hook Fired: " . ($test_results['save_post_fired'] ? '✅ YES' : '❌ NO') . "\n";
    echo "Meta Updated Hook Fired: " . ($test_results['meta_updated_fired'] ? '✅ YES' : '❌ NO') . "\n";
    
    if (!empty($test_results['data_received'])) {
        echo "\nData Received:\n";
        foreach ($test_results['data_received'] as $hook => $data) {
            echo "  {$hook}: " . print_r($data, true) . "\n";
        }
    }
    
    if (!empty($test_results['errors'])) {
        echo "\nErrors:\n";
        foreach ($test_results['errors'] as $error) {
            echo "  ❌ {$error}\n";
        }
    }
    
    echo "\n=== CONCLUSION ===\n";
    if ($test_results['hooks_registered'] && ($test_results['save_post_fired'] || $test_results['meta_updated_fired'])) {
        echo "✅ SUCCESS: MasterStudy LMS ADMIN integration approach is working!\n";
        echo "   Ready to implement in main referral tracker.\n";
    } else {
        echo "⚠️  PARTIAL SUCCESS: Hooks registered but not triggered.\n";
        echo "   This is normal - hooks will fire when actual admin order status changes occur.\n";
        echo "   Test by manually changing order status in MasterStudy LMS admin panel.\n";
    }
}

// Run the test
echo "Starting MasterStudy LMS integration test...\n\n";

// Test MasterStudy availability
if (!test_masterstudy_availability()) {
    echo "❌ Test failed - MasterStudy LMS not available\n";
    exit(1);
}

// Register hooks
register_test_hooks();

// Create test order (simulation only)
create_test_order();

// Trigger test hooks manually
trigger_test_hooks();

// Display results
display_test_results();

echo "\nTest completed.\n";
echo "\n📋 NEXT STEPS:\n";
echo "1. Test ADMIN order status changes in MasterStudy LMS admin panel\n";
echo "2. Go to WordPress Admin → Orders (MasterStudy LMS)\n";
echo "3. Edit an existing order and change status from 'pending' to 'completed' or 'cancelled'\n";
echo "4. Check if our hooks fire when admin saves the order\n";
echo "5. If hooks work, implement the correct approach in main referral tracker\n";
echo "\n🔧 CORRECT HOOKS TO USE:\n";
echo "   - save_post (priority 20+) for admin order status changes\n";
echo "   - updated_post_meta for meta field updates\n";
echo "   - NOT stm_lms_order_accepted/remove (those are for purchases)\n";
?>
