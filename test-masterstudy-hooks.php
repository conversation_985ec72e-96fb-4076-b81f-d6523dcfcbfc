<?php
/**
 * Test script to verify MasterStudy LMS order status synchronization
 * 
 * This script tests the integration approach by hooking into MasterStudy LMS
 * order status changes and verifying we receive the correct data.
 */

// Include WordPress
require_once('c:/xampp/htdocs/code/wp-config.php');

echo "=== MasterStudy LMS Order Status Synchronization Test ===\n";
echo "Current time: " . current_time('mysql') . "\n\n";

// Test data storage
$test_results = array(
    'hooks_registered' => false,
    'order_accepted_fired' => false,
    'order_remove_fired' => false,
    'data_received' => array(),
    'errors' => array()
);

/**
 * Test hook for stm_lms_order_accepted
 */
function test_order_accepted_hook($user_id, $cart_items) {
    global $test_results;
    
    echo "🎉 HOOK FIRED: stm_lms_order_accepted\n";
    echo "User ID: {$user_id}\n";
    echo "Cart Items: " . print_r($cart_items, true) . "\n";
    
    $test_results['order_accepted_fired'] = true;
    $test_results['data_received']['order_accepted'] = array(
        'user_id' => $user_id,
        'cart_items' => $cart_items,
        'timestamp' => current_time('mysql')
    );
    
    // Test referral status update logic
    echo "Testing referral status update for completed order...\n";
    test_referral_status_update($user_id, $cart_items, 'completed');
}

/**
 * Test hook for stm_lms_order_remove
 */
function test_order_remove_hook($course_id, $cart_item, $user_id) {
    global $test_results;
    
    echo "🎉 HOOK FIRED: stm_lms_order_remove\n";
    echo "Course ID: {$course_id}\n";
    echo "Cart Item: " . print_r($cart_item, true) . "\n";
    echo "User ID: {$user_id}\n";
    
    $test_results['order_remove_fired'] = true;
    $test_results['data_received']['order_remove'] = array(
        'course_id' => $course_id,
        'cart_item' => $cart_item,
        'user_id' => $user_id,
        'timestamp' => current_time('mysql')
    );
    
    // Test referral status update logic
    echo "Testing referral status update for cancelled order...\n";
    test_referral_status_update($user_id, array($cart_item), 'cancelled');
}

/**
 * Test referral status update logic
 */
function test_referral_status_update($user_id, $cart_items, $new_status) {
    global $wpdb;
    
    echo "  → Searching for referrals to update...\n";
    echo "  → User ID: {$user_id}\n";
    echo "  → New Status: {$new_status}\n";
    
    // Check if we have referrals for this user
    $referrals_table = $wpdb->prefix . 'mst_referrals';
    
    $referrals = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM {$referrals_table} WHERE referred_id = %d OR referrer_id = %d",
        $user_id,
        $user_id
    ));
    
    if ($referrals) {
        echo "  → Found " . count($referrals) . " referral(s) for user {$user_id}\n";
        foreach ($referrals as $referral) {
            echo "    - Referral ID: {$referral->id}, Current Status: {$referral->status}\n";
            
            // Simulate status update
            if ($referral->status !== $new_status) {
                echo "    - Would update referral {$referral->id} from '{$referral->status}' to '{$new_status}'\n";
            } else {
                echo "    - Referral {$referral->id} already has status '{$new_status}'\n";
            }
        }
    } else {
        echo "  → No referrals found for user {$user_id}\n";
    }
    
    // Check cart items for course-specific referrals
    if (is_array($cart_items)) {
        foreach ($cart_items as $item) {
            $course_id = isset($item['item_id']) ? $item['item_id'] : (isset($item['course_id']) ? $item['course_id'] : null);
            if ($course_id) {
                echo "  → Checking course-specific referrals for course {$course_id}\n";
                
                $course_referrals = $wpdb->get_results($wpdb->prepare(
                    "SELECT * FROM {$referrals_table} WHERE course_id = %d AND (referred_id = %d OR referrer_id = %d)",
                    $course_id,
                    $user_id,
                    $user_id
                ));
                
                if ($course_referrals) {
                    echo "    - Found " . count($course_referrals) . " course-specific referral(s)\n";
                } else {
                    echo "    - No course-specific referrals found\n";
                }
            }
        }
    }
}

/**
 * Register test hooks
 */
function register_test_hooks() {
    global $test_results;
    
    echo "Registering test hooks...\n";
    
    // Register hooks for MasterStudy LMS order status changes
    add_action('stm_lms_order_accepted', 'test_order_accepted_hook', 10, 2);
    add_action('stm_lms_order_remove', 'test_order_remove_hook', 10, 3);
    
    $test_results['hooks_registered'] = true;
    echo "✅ Hooks registered successfully!\n\n";
}

/**
 * Test if MasterStudy LMS is active and hooks exist
 */
function test_masterstudy_availability() {
    echo "Testing MasterStudy LMS availability...\n";
    
    // Check if MasterStudy LMS is active
    if (!class_exists('STM_LMS_Order')) {
        echo "❌ ERROR: MasterStudy LMS Order class not found!\n";
        echo "   Make sure MasterStudy LMS plugin is active.\n";
        return false;
    }
    
    echo "✅ MasterStudy LMS Order class found\n";
    
    // Check if the hooks are available by looking at registered actions
    global $wp_filter;
    
    $hooks_to_check = array('stm_lms_order_accepted', 'stm_lms_order_remove');
    foreach ($hooks_to_check as $hook) {
        if (isset($wp_filter[$hook])) {
            echo "✅ Hook '{$hook}' is available\n";
        } else {
            echo "⚠️  Hook '{$hook}' not currently registered (this is normal before orders are processed)\n";
        }
    }
    
    return true;
}

/**
 * Create a test order to trigger hooks
 */
function create_test_order() {
    echo "\nCreating test order to trigger hooks...\n";
    
    // Check if we can create a test order
    if (!class_exists('STM_LMS_Order')) {
        echo "❌ Cannot create test order - STM_LMS_Order class not available\n";
        return false;
    }
    
    // Create test order data
    $test_order_data = array(
        'user_id' => 1,
        'cart_items' => array(
            array(
                'item_id' => 100,
                'price' => 50.00,
                'quantity' => 1
            )
        ),
        'payment_code' => 'test',
        '_order_total' => 50.00,
        '_order_currency' => 'USD'
    );
    
    echo "Test order data prepared:\n";
    echo print_r($test_order_data, true) . "\n";
    
    // Note: We won't actually create the order to avoid side effects
    echo "⚠️  Test order creation skipped to avoid side effects\n";
    echo "   To test hooks, manually change an order status in MasterStudy LMS admin\n";
    
    return true;
}

/**
 * Manual hook trigger for testing
 */
function trigger_test_hooks() {
    echo "\nTriggering test hooks manually...\n";
    
    // Simulate order accepted
    echo "Simulating stm_lms_order_accepted hook...\n";
    $test_cart_items = array(
        array(
            'item_id' => 100,
            'price' => 50.00,
            'quantity' => 1
        )
    );
    do_action('stm_lms_order_accepted', 1, $test_cart_items);
    
    echo "\nSimulating stm_lms_order_remove hook...\n";
    $test_cart_item = array(
        'item_id' => 100,
        'price' => 50.00,
        'quantity' => 1
    );
    do_action('stm_lms_order_remove', 100, $test_cart_item, 1);
}

/**
 * Display test results
 */
function display_test_results() {
    global $test_results;
    
    echo "\n=== TEST RESULTS ===\n";
    echo "Hooks Registered: " . ($test_results['hooks_registered'] ? '✅ YES' : '❌ NO') . "\n";
    echo "Order Accepted Hook Fired: " . ($test_results['order_accepted_fired'] ? '✅ YES' : '❌ NO') . "\n";
    echo "Order Remove Hook Fired: " . ($test_results['order_remove_fired'] ? '✅ YES' : '❌ NO') . "\n";
    
    if (!empty($test_results['data_received'])) {
        echo "\nData Received:\n";
        foreach ($test_results['data_received'] as $hook => $data) {
            echo "  {$hook}: " . print_r($data, true) . "\n";
        }
    }
    
    if (!empty($test_results['errors'])) {
        echo "\nErrors:\n";
        foreach ($test_results['errors'] as $error) {
            echo "  ❌ {$error}\n";
        }
    }
    
    echo "\n=== CONCLUSION ===\n";
    if ($test_results['hooks_registered'] && ($test_results['order_accepted_fired'] || $test_results['order_remove_fired'])) {
        echo "✅ SUCCESS: MasterStudy LMS integration approach is working!\n";
        echo "   Ready to implement in main referral tracker.\n";
    } else {
        echo "⚠️  PARTIAL SUCCESS: Hooks registered but not triggered.\n";
        echo "   This is normal - hooks will fire when actual orders are processed.\n";
        echo "   Test by manually changing order status in MasterStudy LMS admin.\n";
    }
}

// Run the test
echo "Starting MasterStudy LMS integration test...\n\n";

// Test MasterStudy availability
if (!test_masterstudy_availability()) {
    echo "❌ Test failed - MasterStudy LMS not available\n";
    exit(1);
}

// Register hooks
register_test_hooks();

// Create test order (simulation only)
create_test_order();

// Trigger test hooks manually
trigger_test_hooks();

// Display results
display_test_results();

echo "\nTest completed.\n";
echo "\n📋 NEXT STEPS:\n";
echo "1. ✅ IMPLEMENTATION COMPLETE: MasterStudy LMS integration added to referral tracker\n";
echo "2. Go to MasterStudy LMS admin orders page\n";
echo "3. Change an order status from 'pending' to 'completed' or 'cancelled'\n";
echo "4. Check referral status updates automatically in admin\n";
echo "5. Verify real-time synchronization is working\n";
?>
